"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { FileText, Lock, Unlock, Download } from "lucide-react";
import { ContactUsFormFields } from "~/components/contact-components/contact-us-form/contact-us-form-fields";
import { useContactUsForm } from "~/hooks/use-contact-us-form";
import useLocalStorage from "use-local-storage";
import { sendGAEvent } from "@next/third-parties/google";
import { courseOptions } from "~/constants/courses";
import { DownloadButton } from "~/components/ui/download-button";

interface MockTestDownloadProps {
  testId: string;
  testTitle: string;
  testDescription: string;
  downloadUrl: string; // For backward compatibility
  questionPaperUrl?: string; // URL for question paper PDF
  answerKeyUrl?: string; // URL for answer key PDF
  defaultCourse?: string;
}

export default function MockTestDownload({
  testId,
  testTitle,
  testDescription,
  downloadUrl,
  questionPaperUrl,
  answerKeyUrl,
  defaultCourse = courseOptions[0] ?? "",
}: MockTestDownloadProps) {
  // We'll use the nullish coalescing operator directly in the component
  // State to track if component is mounted (client-side)
  const [isMounted, setIsMounted] = useState(false);

  // Use useLocalStorage hook with default values
  const [hasAccess, setHasAccess] = useLocalStorage<boolean>(
    `mock-test-access-${testId}`,
    false,
  );

  // We're now using the hook's form data directly

  // We don't need to store the form data in localStorage anymore
  // since we're using the hook's form data directly

  // Set isMounted to true after hydration
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Use the hook's form handling functions with our defaultCourse
  const {
    formData: hookFormData,
    loading,
    handleSubmit: submitContactForm,
    handleFormChange: hookHandleFormChange,
  } = useContactUsForm({ course: defaultCourse }, `download-form-${testId}`);

  // We're now using the hook's handleFormChange directly

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    // Track form submission
    sendGAEvent("event", "mock_test_download", {
      test_id: testId,
      test_title: testTitle,
    });

    try {
      // Create a copy of the event for the contact form submission
      // We need to override preventDefault to avoid issues with the original event
      const customSubmitEvent = {
        ...event,
        preventDefault: () => {
          // This is intentionally empty as we've already called preventDefault on the original event
        },
      };

      // We need to track the submission status
      const result = await submitContactForm(customSubmitEvent);

      // Only set access to true if the submission was successful
      if (result && result.status === "success") {
        // Set access to true after successful submission
        setHasAccess(true);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      // Don't set access to true if there was an error
    }
  };

  // We'll use the DownloadButton component directly instead of these handlers

  return (
    <section id={`download-form-${testId}`} className="mb-12">
      <Card className="overflow-hidden border-primary/20">
        <div className="relative">
          {/* Visual indicator for locked/unlocked state - only show on client side */}
          {isMounted && (
            <div className="absolute right-5 top-5 z-10">
              {hasAccess ? (
                <div className="flex items-center gap-3 rounded-full bg-green-100 px-5 py-2.5 text-base font-medium text-green-800 shadow-sm dark:bg-green-900/30 dark:text-green-400">
                  <Unlock className="h-5 w-5" />
                  <span>Unlocked</span>
                </div>
              ) : (
                <div className="flex items-center gap-3 rounded-full bg-amber-100 px-5 py-2.5 text-base font-medium text-amber-800 shadow-sm dark:bg-amber-900/30 dark:text-amber-400">
                  <Lock className="h-5 w-5" />
                  <span>Locked</span>
                </div>
              )}
            </div>
          )}

          <CardContent className="p-8">
            <div className="mb-6 flex flex-col items-center justify-center md:flex-row md:items-start md:gap-6">
              {/* PDF Icon */}
              <div className="mb-4 flex h-24 w-24 items-center justify-center rounded-lg bg-primary/10 md:mb-0">
                <FileText className="h-12 w-12 text-primary" />
              </div>

              {/* Test Information */}
              <div className="flex-1 text-center md:text-left">
                <h2 className="mb-2 text-2xl font-semibold">{testTitle}</h2>
                <p className="mb-4 text-muted-foreground">{testDescription}</p>

                {isMounted && hasAccess ? (
                  <div>
                    <div className="mt-4 flex flex-col items-center gap-3">
                      <DownloadButton
                        downloadUrl={questionPaperUrl ?? downloadUrl}
                        fileName={`${testId}-question-paper.pdf`}
                        className="flex w-full items-center justify-center gap-2"
                        trackingEvent={{
                          eventName: "mock_test_question_paper_download",
                          eventData: {
                            test_id: testId,
                            test_title: testTitle,
                          },
                        }}
                      >
                        <Download className="mr-3 h-5 w-5" />
                        Download Question Paper
                      </DownloadButton>

                      {answerKeyUrl ? (
                        <DownloadButton
                          downloadUrl={answerKeyUrl}
                          fileName={`${testId}-answer-key.pdf`}
                          className="flex w-full items-center justify-center gap-2"
                          trackingEvent={{
                            eventName: "mock_test_answer_key_download",
                            eventData: {
                              test_id: testId,
                              test_title: testTitle,
                            },
                          }}
                        >
                          <Download className="mr-3 h-5 w-5" />
                          Download Answer Key
                        </DownloadButton>
                      ) : (
                        <Button
                          className="flex w-full items-center justify-center gap-2 bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:hover:bg-amber-900/50"
                          disabled
                        >
                          <span className="mr-2 text-lg">🔜</span>
                          Answer Key - Will Release Shortly
                        </Button>
                      )}
                    </div>
                    <p className="mt-3 text-center font-medium">
                      {answerKeyUrl
                        ? "Download the question paper and answer key now."
                        : "Download the question paper now. Answer key will be available soon."}
                    </p>
                  </div>
                ) : (
                  <div className="rounded-lg bg-muted p-4 text-sm">
                    <h3 className="mb-2 font-medium">
                      Fill out the form below to unlock:
                    </h3>
                    <ul className="ml-4 list-disc space-y-1">
                      <li>Question paper with detailed explanations</li>
                      <li>Answer key for self-assessment</li>
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {/* Form Section - Only show if user doesn't have access and component is mounted */}
            {isMounted && !hasAccess && (
              <div className="mt-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <ContactUsFormFields
                    formData={hookFormData}
                    onFormChange={hookHandleFormChange}
                    formId={testId}
                  />
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "Submitting..." : "Submit to Unlock"}
                  </Button>
                </form>
              </div>
            )}
          </CardContent>
        </div>
      </Card>
    </section>
  );
}
