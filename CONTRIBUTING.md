# Contributing to Aims Academy

Thank you for your interest in contributing to Aims Academy! We appreciate any help we can get to make this project better.

## Getting Started

1. Fork the repository: Click the "Fork" button in the top-right corner of the repository page.
2. Clone the repository: `git clone https://github.com/bharathganji/aims-academy.git`
3. Create a new branch: `git checkout -b your-branch-name`
4. Make your changes: Edit the files, add new features, fix bugs, etc.
5. Commit your changes: `git commit -m "Your commit message"`
6. Push your changes: `git push origin your-branch-name`
7. Create a pull request: Go to the repository page and click the "New pull request" button.

## Contribution Guidelines

- Make sure your code is formatted according to our coding standards (see [CODE_STYLE.md](CODE_STYLE.md)).
- Write unit tests for new features and bug fixes.
- Keep your commits small and focused on one feature or bug fix.
- Use meaningful commit messages that describe the changes you made.
- Make sure your code is compatible with the latest version of Next.js and other dependencies.

## Code Review

- All pull requests will be reviewed by the maintainers of the project.
- We will check for coding standards, functionality, and compatibility.
- We may ask you to make changes before merging your pull request.

## Reporting Issues

- If you find a bug or have a feature request, please create an issue on the repository page.
- Make sure to include as much detail as possible, including steps to reproduce the issue.

## Code of Conduct

- We follow the [Contributor Covenant Code of Conduct](https://www.contributor-covenant.org/version/2/0/code_of_conduct/).
- Please be respectful and considerate of others in your interactions with the project.

## License

- By contributing to this project, you agree to license your contributions under the [MIT License](LICENSE).
