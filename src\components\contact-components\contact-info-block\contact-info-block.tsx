import { Mail, MapPin, Phone } from "lucide-react";
import Link from "next/link";
import { SITE_CONFIG } from "~/constants/siteConfig";

const ContactInfoBlock = () => {
  return (
    <section className="grid gap-8 md:grid-cols-3">
      <div className="flex flex-col items-center space-y-4">
        <span className="mb-3 flex size-12 flex-col items-center justify-center rounded-full bg-accent">
          <Phone className="h-6 w-auto" />
        </span>
        <p className="mb-2 text-lg font-semibold">Call Us</p>
        <p className="mb-3 text-muted-foreground">We are available 24/7.</p>
        <div className="flex flex-col space-y-2">
          <Link
            href="tel:+919008266404"
            className="font-semibold hover:underline"
          >
            +91 9008266404
          </Link>
          <Link
            href="tel:+919008466404"
            className="font-semibold hover:underline"
          >
            +91 9008466404
          </Link>
        </div>
      </div>
      <div className="flex flex-col items-center space-y-4">
        <span className="mb-3 flex size-12 flex-col items-center justify-center rounded-full bg-accent">
          <Mail className="h-6 w-auto" />
        </span>
        <p className="mb-2 text-lg font-semibold">Email Us</p>
        <p className="mb-3 text-muted-foreground">
          Our team is ready to assist.
        </p>
        <Link
          href={`mailto:${SITE_CONFIG.contact.email}`}
          className="font-semibold hover:underline"
        >
          {SITE_CONFIG.contact.email}
        </Link>
      </div>
      <div className="flex flex-col items-center space-y-4">
        <span className="mb-3 flex size-12 flex-col items-center justify-center rounded-full bg-accent">
          <MapPin className="h-6 w-auto" />
        </span>
        <p className="mb-2 text-lg font-semibold">Visit Us</p>
        <p className="mb-3 text-muted-foreground">
          Drop by our office for a chat.
        </p>
        <Link
          href={SITE_CONFIG.maps.url}
          target="_blank"
          className="text-pretty text-center font-semibold hover:underline"
        >
          {SITE_CONFIG.contact.address}
        </Link>
      </div>
    </section>
  );
};

export { ContactInfoBlock };
