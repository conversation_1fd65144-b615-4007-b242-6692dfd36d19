/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ReviewRoot {
  success: boolean;
  widget: string;
  layout: string;
  published: boolean;
  pinnedReviews: any[];
  hiddenReviews: any[];
  highlights: any[];
  titles: any[];
  allowedDomains: any[];
  config: Config;
  totalReviewCount: number;
  averageRating: number;
  batchSize: number;
  profileUrl: string;
  reviews: Review[];
}

export interface Config {
  sortBy: string;
  theme: string;
  pageSize: number;
  logoVariant: string;
  minStars: number;
  reviewVariant: string;
  nameDisplay: string;
  title: any;
  showTitle: boolean;
  carouselAutoplay: boolean;
  carouselSpeed: number;
  sliderAutoplay: boolean;
  sliderSpeed: number;
  maxCharacters: number;
  dateDisplay: string;
  floatPosition: string;
  floating: boolean;
  dismissable: boolean;
  schema: boolean;
  schemaType: string;
  schemaSubType: any;
  schemaSubSubType: any;
  showBranding: boolean;
  summary: boolean;
  summaryPosition: string;
  summarySemantic: boolean;
  summarySemanticOverride: any;
  summaryLogoVariant: string;
  summaryReviewButton: boolean;
  summaryReviewButtonText: any;
  aiTitlesEnabled: boolean;
  language: string;
  name: any;
  hideEmptyReviews: boolean;
  showDatesOnPinnedReviews: boolean;
  showBadgeLabel: boolean;
  customCss: string;
  themeId: string;
  showProfilePictures: boolean;
}

export interface Review {
  reviewId: string;
  reviewer: Reviewer;
  starRating: number;
  comment: string;
  createTime: string;
  updateTime: string;
  reviewReply?: ReviewReply;
}

export interface Reviewer {
  profilePhotoUrl: string;
  displayName: string;
  isAnonymous?: boolean;
}

export interface ReviewReply {
  comment: string;
  updateTime: string;
}
