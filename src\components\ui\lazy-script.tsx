"use client";

import <PERSON>rip<PERSON>, { type ScriptProps } from "next/script";
import { useEffect, useState } from "react";

interface LazyScriptProps extends Omit<ScriptProps, "strategy"> {
  /**
   * Delay in milliseconds before loading the script
   */
  delay?: number;
}

/**
 * LazyScript component that loads scripts with a delay to improve initial page load performance
 * This is useful for non-critical scripts that can be loaded after the page is interactive
 */
export function LazyScript({ delay = 2000, ...props }: LazyScriptProps) {
  const [shouldLoad, setShouldLoad] = useState(false);

  useEffect(() => {
    // Set a timeout to load the script after the specified delay
    const timer = setTimeout(() => {
      setShouldLoad(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  if (!shouldLoad) {
    return null;
  }

  return <Script {...props} strategy="lazyOnload" />;
}
