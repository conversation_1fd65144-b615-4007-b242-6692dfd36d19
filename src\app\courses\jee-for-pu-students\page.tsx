import { type Metadata } from "next";
import { CoursesPageHero } from "~/components/courses-components/courses-page-hero/courses-page-hero";
import { ProgramFeatureSection } from "~/components/courses-components/feature-cards-section";
import { TrainingProcessSection } from "~/components/courses-components/training-process-section";
import { WhyChooseUs } from "~/components/courses-components/why-choose-us";
import { jeeForPuStudentsProgramFeatureList } from "~/constants/courses";

export const metadata: Metadata = {
  title: "JEE Coaching for PU Students | Expert Guidance at Aims Academy",
  description:
    "Prepare for JEE with Aims Academy's expert coaching for PU students. Comprehensive syllabus coverage, personalized mentoring, and regular mock tests ensure top results in Physics, Chemistry, and Mathematics.",
  keywords: [
    "JEE Coaching for PU Students",
    "JEE Preparation Bangalore",
    "PU JEE Integrated Program",
    "Best JEE Classes for PUC Students",
    "JEE Classes for PU Students",
  ],
  openGraph: {
    title: "JEE Coaching for PU Students | Expert Guidance at Aims Academy",
    description:
      "Join Aims Academy for top-notch JEE coaching tailored for PU students. Expert guidance, comprehensive syllabus coverage, and proven results. Enroll now!",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy JEE Coaching for PU Students",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "JEE Coaching for PU Students | Expert Guidance at Aims Academy",
    description:
      "Excel in JEE with Aims Academy's expert coaching for PU students. Comprehensive preparation and personalized mentoring for success!",
  },
};

const heroProps = {
  heading: "JEE for PU Students",
  subheading: "Comprehensive JEE Preparation",
  description:
    "Our JEE coaching program for PU students focuses on conceptual clarity and problem-solving techniques. Join us to excel in your JEE exams and secure admission to top engineering colleges.",
  buttons: {
    primary: {
      text: "Enroll Now",
      url: "#contact-form",
    },
    secondary: {
      text: "Learn More",
      url: "#contact-form",
    },
  },
  image: {
    src: "/exams_courses_2.svg",
    alt: "JEE for PU Students",
  },
};

const JeeForPuStudents = () => {
  return (
    <article>
      <CoursesPageHero {...heroProps} />
      <ProgramFeatureSection
        features={jeeForPuStudentsProgramFeatureList}
        imageSrc="/program-feature-img.png"
      />
      <WhyChooseUs />
      <TrainingProcessSection courseType="JEE_FOR_PU_STUDENTS" />
    </article>
  );
};

export default JeeForPuStudents;
