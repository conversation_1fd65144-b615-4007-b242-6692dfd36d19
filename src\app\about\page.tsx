import { OurCurriculum } from "~/components/about-components/our-curriculum/our-curriculum";
import { OurFaculty } from "~/components/about-components/our-faculty/our-faculty";
import { OurStrikingTraits } from "~/components/about-components/our-strikIing-traits/our-strikIing-traits";

import { type Metadata } from "next";
import { SITE_DOMAIN } from "~/constants/siteConfig";
import OurVisionMission from "~/components/about-components/our-vision-mission/our-vision-mission";
import OurEducationalPhilosophy from "~/components/about-components/our-educational-philosophy/our-educational-philosophy";

export const metadata: Metadata = {
  title: "About Us | Aims Academy Bangalore",
  description:
    "Learn more about Aims Academy Bangalore, our mission, vision, and values. Discover our expert faculty, innovative curriculum, and commitment to transforming aspirants into achievers.",
  keywords: [
    "About Aims Academy Bangalore",
    "Aims Academy Faculty",
    "Best PU Coaching in Bangalore",
    "NEET Coaching in Yelahanka",
    "JEE Coaching in Bangalore",
    "KCET Coaching in Bangalore",
  ],
  openGraph: {
    title: "About Us | Aims Academy Bangalore",
    description:
      "Learn more about Aims Academy Bangalore, our mission, vision, and values. Discover our expert faculty, innovative curriculum, and commitment to transforming aspirants into achievers.",
    url: `${SITE_DOMAIN}/about`,
    siteName: "Aims Academy",
    // images: [
    //   {
    //     url: "/og-about.jpg", // Add an About-specific OG image
    //     width: 1200,
    //     height: 630,
    //     alt: "About Aims Academy Bangalore",
    //   },
    // ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "About Us | Aims Academy Bangalore",
    description:
      "Learn more about Aims Academy Bangalore, our mission, vision, and values. Discover our expert faculty, innovative curriculum, and commitment to transforming aspirants into achievers.",
    // images: ["/twitter-about.jpg"], // Add an About-specific Twitter image
  },
};

const AboutPage = () => {
  return (
    <section>
      <h1 className="mb-4 mt-1 text-balance text-3xl font-semibold md:text-4xl">
        About us - Learn More About Our Mission, Vision, and Values
      </h1>
      <OurFaculty />
      <OurCurriculum />
      <OurStrikingTraits />
      <OurVisionMission />
      <OurEducationalPhilosophy />
    </section>
  );
};

export default AboutPage;
