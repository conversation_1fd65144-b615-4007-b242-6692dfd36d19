{"name": "Subject Guides", "description": "In-depth subject-specific guides for Physics, Chemistry, and Biology with chapter-wise preparation strategies and important topics.", "color": "#10B981", "icon": "graduation-cap", "body": {"raw": "\n# Subject Guides Category\n\nComprehensive subject-wise preparation guides covering Physics, Chemistry, and Biology with detailed chapter analysis, important topics, and expert tips from our experienced faculty.\n\n## What You'll Find Here\n\n- **Physics Mastery**: Complete guides for mechanics, thermodynamics, optics, and modern physics\n- **Chemistry Excellence**: Organic, inorganic, and physical chemistry preparation strategies\n- **Biology Expertise**: Detailed coverage of botany, zoology, and human physiology\n- **Chapter-wise Analysis**: Important topics and weightage for each chapter\n- **Concept Clarity**: Clear explanations of complex concepts with practical examples\n\n## Featured Topics\n\n- High-yield topics in each subject with maximum weightage\n- Common mistakes to avoid in Physics, Chemistry, and Biology\n- Subject-specific study techniques and memory aids\n- Integration of concepts across different chapters\n- Previous year question analysis by subject and chapter\n", "code": "var Component=(()=>{var p=Object.create;var c=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var y=Object.getPrototypeOf,g=Object.prototype.hasOwnProperty;var f=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),x=(n,e)=>{for(var t in e)c(n,t,{get:e[t],enumerable:!0})},a=(n,e,t,s)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let r of m(e))!g.call(n,r)&&r!==t&&c(n,r,{get:()=>e[r],enumerable:!(s=u(e,r))||s.enumerable});return n};var j=(n,e,t)=>(t=n!=null?p(y(n)):{},a(e||!n||!n.__esModule?c(t,\"default\",{value:n,enumerable:!0}):t,n)),C=n=>a(c({},\"__esModule\",{value:!0}),n);var l=f((v,o)=>{o.exports=_jsx_runtime});var b={};x(b,{default:()=>d,frontmatter:()=>w});var i=j(l()),w={name:\"Subject Guides\",description:\"In-depth subject-specific guides for Physics, Chemistry, and Biology with chapter-wise preparation strategies and important topics.\",color:\"#10B981\",icon:\"graduation-cap\"};function h(n){let e={a:\"a\",h1:\"h1\",h2:\"h2\",li:\"li\",p:\"p\",strong:\"strong\",ul:\"ul\",...n.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e.h1,{id:\"subject-guides-category\",children:(0,i.jsx)(e.a,{className:\"anchor-link\",href:\"#subject-guides-category\",children:\"Subject Guides Category\"})}),`\n`,(0,i.jsx)(e.p,{children:\"Comprehensive subject-wise preparation guides covering Physics, Chemistry, and Biology with detailed chapter analysis, important topics, and expert tips from our experienced faculty.\"}),`\n`,(0,i.jsx)(e.h2,{id:\"what-youll-find-here\",children:(0,i.jsx)(e.a,{className:\"anchor-link\",href:\"#what-youll-find-here\",children:\"What You'll Find Here\"})}),`\n`,(0,i.jsxs)(e.ul,{children:[`\n`,(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:\"Physics Mastery\"}),\": Complete guides for mechanics, thermodynamics, optics, and modern physics\"]}),`\n`,(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:\"Chemistry Excellence\"}),\": Organic, inorganic, and physical chemistry preparation strategies\"]}),`\n`,(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:\"Biology Expertise\"}),\": Detailed coverage of botany, zoology, and human physiology\"]}),`\n`,(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:\"Chapter-wise Analysis\"}),\": Important topics and weightage for each chapter\"]}),`\n`,(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:\"Concept Clarity\"}),\": Clear explanations of complex concepts with practical examples\"]}),`\n`]}),`\n`,(0,i.jsx)(e.h2,{id:\"featured-topics\",children:(0,i.jsx)(e.a,{className:\"anchor-link\",href:\"#featured-topics\",children:\"Featured Topics\"})}),`\n`,(0,i.jsxs)(e.ul,{children:[`\n`,(0,i.jsx)(e.li,{children:\"High-yield topics in each subject with maximum weightage\"}),`\n`,(0,i.jsx)(e.li,{children:\"Common mistakes to avoid in Physics, Chemistry, and Biology\"}),`\n`,(0,i.jsx)(e.li,{children:\"Subject-specific study techniques and memory aids\"}),`\n`,(0,i.jsx)(e.li,{children:\"Integration of concepts across different chapters\"}),`\n`,(0,i.jsx)(e.li,{children:\"Previous year question analysis by subject and chapter\"}),`\n`]})]})}function d(n={}){let{wrapper:e}=n.components||{};return e?(0,i.jsx)(e,{...n,children:(0,i.jsx)(h,{...n})}):h(n)}return C(b);})();\n;return Component;"}, "_id": "blog-categories/subject-guides.mdx", "_raw": {"sourceFilePath": "blog-categories/subject-guides.mdx", "sourceFileName": "subject-guides.mdx", "sourceFileDir": "blog-categories", "contentType": "mdx", "flattenedPath": "blog-categories/subject-guides"}, "type": "BlogCategory", "slug": "subject-guides"}