{"name": "News & Updates", "description": "Latest NEET exam updates, syllabus changes, important dates, and medical entrance examination news from official sources.", "color": "#8B5CF6", "icon": "newspaper", "body": {"raw": "\n# News & Updates Category\n\nStay updated with the latest NEET exam news, official announcements, syllabus changes, and important updates from NTA and medical education authorities.\n\n## What You'll Find Here\n\n- **Official Announcements**: Latest updates from NTA and medical education boards\n- **Exam Dates**: Important dates for NEET registration, exam, and results\n- **Syllabus Changes**: Updates on NEET syllabus modifications and pattern changes\n- **Admission Updates**: Information about medical college admissions and counseling\n- **Policy Changes**: Updates on reservation policies, eligibility criteria, and other regulations\n\n## Featured Topics\n\n- NEET 2025 registration dates and important deadlines\n- Changes in NEET exam pattern and marking scheme\n- New medical colleges and seat matrix updates\n- State-wise NEET counseling schedules and procedures\n- Updates on NEET eligibility criteria and age limits\n", "code": "var Component=(()=>{var m=Object.create;var s=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var g=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty;var E=(a,e)=>()=>(e||a((e={exports:{}}).exports,e),e.exports),x=(a,e)=>{for(var i in e)s(a,i,{get:e[i],enumerable:!0})},l=(a,e,i,r)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let t of p(e))!f.call(a,t)&&t!==i&&s(a,t,{get:()=>e[t],enumerable:!(r=u(e,t))||r.enumerable});return a};var N=(a,e,i)=>(i=a!=null?m(g(a)):{},l(e||!a||!a.__esModule?s(i,\"default\",{value:a,enumerable:!0}):i,a)),y=a=>l(s({},\"__esModule\",{value:!0}),a);var o=E((C,d)=>{d.exports=_jsx_runtime});var T={};x(T,{default:()=>h,frontmatter:()=>w});var n=N(o()),w={name:\"News & Updates\",description:\"Latest NEET exam updates, syllabus changes, important dates, and medical entrance examination news from official sources.\",color:\"#8B5CF6\",icon:\"newspaper\"};function c(a){let e={a:\"a\",h1:\"h1\",h2:\"h2\",li:\"li\",p:\"p\",strong:\"strong\",ul:\"ul\",...a.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(e.h1,{id:\"news--updates-category\",children:(0,n.jsx)(e.a,{className:\"anchor-link\",href:\"#news--updates-category\",children:\"News & Updates Category\"})}),`\n`,(0,n.jsx)(e.p,{children:\"Stay updated with the latest NEET exam news, official announcements, syllabus changes, and important updates from NTA and medical education authorities.\"}),`\n`,(0,n.jsx)(e.h2,{id:\"what-youll-find-here\",children:(0,n.jsx)(e.a,{className:\"anchor-link\",href:\"#what-youll-find-here\",children:\"What You'll Find Here\"})}),`\n`,(0,n.jsxs)(e.ul,{children:[`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Official Announcements\"}),\": Latest updates from NTA and medical education boards\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Exam Dates\"}),\": Important dates for NEET registration, exam, and results\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Syllabus Changes\"}),\": Updates on NEET syllabus modifications and pattern changes\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Admission Updates\"}),\": Information about medical college admissions and counseling\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Policy Changes\"}),\": Updates on reservation policies, eligibility criteria, and other regulations\"]}),`\n`]}),`\n`,(0,n.jsx)(e.h2,{id:\"featured-topics\",children:(0,n.jsx)(e.a,{className:\"anchor-link\",href:\"#featured-topics\",children:\"Featured Topics\"})}),`\n`,(0,n.jsxs)(e.ul,{children:[`\n`,(0,n.jsx)(e.li,{children:\"NEET 2025 registration dates and important deadlines\"}),`\n`,(0,n.jsx)(e.li,{children:\"Changes in NEET exam pattern and marking scheme\"}),`\n`,(0,n.jsx)(e.li,{children:\"New medical colleges and seat matrix updates\"}),`\n`,(0,n.jsx)(e.li,{children:\"State-wise NEET counseling schedules and procedures\"}),`\n`,(0,n.jsx)(e.li,{children:\"Updates on NEET eligibility criteria and age limits\"}),`\n`]})]})}function h(a={}){let{wrapper:e}=a.components||{};return e?(0,n.jsx)(e,{...a,children:(0,n.jsx)(c,{...a})}):c(a)}return y(T);})();\n;return Component;"}, "_id": "blog-categories/news-updates.mdx", "_raw": {"sourceFilePath": "blog-categories/news-updates.mdx", "sourceFileName": "news-updates.mdx", "sourceFileDir": "blog-categories", "contentType": "mdx", "flattenedPath": "blog-categories/news-updates"}, "type": "BlogCategory", "slug": "news-updates"}