// ~/constants/gallery.ts
export interface TimelineItem {
  id: number;
  title: string;
  description: string;
  date: string; // Format: YYYY-MM
  category: string;
  image: string;
}

export const timelineItems: TimelineItem[] = [
  // Biology Lab
  {
    id: 1,
    title: "Biology Laboratory",
    description:
      "Practical space for zoology and botany experiments with basic microscopy facilities",
    date: "2025-02",
    category: "Biology Lab",
    image: "/gallery/biolab_new1.jpg",
  },
  {
    id: 2,
    title: "Biology Equipment Storage",
    description:
      "Storage area for laboratory apparatus and specimen collections",
    date: "2024-11",
    category: "Biology Lab",
    image: "/gallery/biolab_2.png",
  },

  // Chemistry Lab
  {
    id: 3,
    title: "Chemistry Practical Lab",
    description: "Working space for basic chemical experiments and titrations",
    date: "2025-01",
    category: "Chemistry Lab",
    image: "/gallery/chemistry_lab4.jpg",
  },
  {
    id: 4,
    title: "Chemical Safety Equipment",
    description: "Emergency shower and eye wash station in chemistry lab",
    date: "2024-09",
    category: "Chemistry Lab",
    image: "/gallery/chemistry_lab3.jpg",
  },

  // Computer Lab

  {
    id: 6,
    title: "Computer Lab Overview",
    description: "General view of student computer laboratory setup",
    date: "2025-02",
    category: "Computer Lab",
    image: "/gallery/computer_1.png",
  },

  // Library
  {
    id: 7,
    title: "Main Reading Hall",
    description: "Library reading area with reference books and study tables",
    date: "2025-03",
    category: "Library",
    image: "/gallery/library_img12.jpg",
  },
  {
    id: 8,
    title: "Reading Hall with ventilation",
    description: "Well-ventilated reading area with study tables",
    date: "2024-10",
    category: "Library",
    image: "/gallery/library_2.png",
  },

  // Campus

  {
    id: 10,
    title: "Classroom with Natural Ventilation",
    description:
      "Well-ventilated classrooms for a comfortable learning environment",
    date: "2025-03",
    category: "Campus",
    image: "/gallery/campus_1.png",
  },
  {
    id: 5,
    title: "Student Writing Mock Test",
    description: "Students participating in mock test ",
    date: "2025-01",
    category: "Campus",
    image: "/gallery/college_com3.png",
  },
  {
    id: 9,
    title: "Yoga and Physical Activities",
    description: "Students engaging in yoga and physical activities",
    date: "2025-02",
    category: "Campus",
    image: "/gallery/cocric_3.png",
  },

  // Physics Lab
  {
    id: 11,
    title: "Physics Laboratory",
    description: "Space for practical experiments in mechanics and electricity",
    date: "2025-01",
    category: "Physics Lab",
    image: "/gallery/physics_lab3.jpg",
  },
];
