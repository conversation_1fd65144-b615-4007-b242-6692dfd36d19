import Image from "next/image";
import Link from "next/link";
import <PERSON>rip<PERSON> from "next/script";
import {
  COURSES_PAGES,
  type CoursesPages,
  type Pages,
  PAGES,
} from "~/constants/pages";
import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";
import GoogleMap from "../contact-components/google-map-iframe/GoogleMap";
import { SOCIAL_LINKS, type SocialLinks } from "../nav-menu/social-links";
import { generateEducationalServiceSchema } from "~/lib/structured-data";
import { SEOKeywordSection } from "./seo-keyword-section";

interface MenuItem {
  title: string;
  links: SocialLinks;
}

interface FooterProps {
  logo?: {
    url: string;
    src: string;
    alt: string;
    title: string;
  };
  tagline?: string;
  menuItems?: MenuItem[];
  copyright?: string;
  bottomLinks?: {
    text: string;
    url: string;
  }[];
}

const QuickLinksPages = (value: Pages) => {
  return Object.keys(value).map((key: string) => {
    return {
      text: value[key as keyof Pages].title,
      url: value[key as keyof Pages].url,
    };
  });
};

const QuickLinksCourses = (value: CoursesPages) => {
  return Object.keys(value).map((key: string) => {
    return {
      text: value[key as keyof CoursesPages].title,
      url: value[key as keyof CoursesPages].url,
    };
  });
};

const Footer = ({
  logo = {
    src: "/logo.png",
    alt: "Institution Logo",
    title: "Aims Academy",
    url: SITE_DOMAIN,
  },
  tagline = "Transforming Aspirants into Achievers",
  menuItems = [
    {
      title: "Quick links",
      links: QuickLinksPages(PAGES),
    },
    {
      title: "Courses",
      links: QuickLinksCourses(COURSES_PAGES),
    },
    {
      title: "Social",
      links: SOCIAL_LINKS,
    },
  ],
  copyright = `© ${new Date().getFullYear()} Copyright. All rights reserved.`,
  bottomLinks = [
    { text: "Terms and Conditions", url: "/terms-and-conditions" },
    { text: "Privacy Policy", url: "/privacy-policy" },
  ],
}: FooterProps) => {
  return (
    <footer className="px-4 py-32">
      <hr className="mb-8 border-t border-gray-200" />
      <div className="container mx-auto">
        <div className="grid grid-cols-2 gap-8 lg:grid-cols-6">
          {/* Logo and Contact Section */}
          <div className="col-span-2 mb-8 lg:mb-0">
            <div className="flex items-center gap-2 lg:justify-start">
              <Image
                src={logo.src}
                alt={logo.alt}
                title={logo.title}
                width={128}
                height={119}
                className="aspect-[128/119] h-10 w-[43px] object-contain"
                priority={false}
              />
              <p className="text-xl font-semibold">{logo.title}</p>
            </div>
            <p className="mt-4 font-bold">{tagline}</p>
            <section className="mt-4">
              <p className="font-bold">Contact Us</p>
              <p className="mt-2">
                <span className="font-medium">Phone: </span>
                <Link
                  href="tel:+919008266404"
                  aria-label="Call us at 9008266404"
                >
                  9008266404
                </Link>{" "}
                |{" "}
                <Link
                  href="tel:+919008466404"
                  aria-label="Call us at 9008466404"
                >
                  9008466404
                </Link>
              </p>
              <p className="mt-2">
                <span className="font-medium">Address: </span>
                <Link
                  href={SITE_CONFIG.maps.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="View our location on Google Maps"
                >
                  Aims Academy, Survey No: 92/5, RTO Office Road,
                  Singanayakanahalli, Yelahanka Hobli, Bangalore North, 560064
                </Link>
              </p>
              <GoogleMap className="h-40" />
            </section>
          </div>

          {/* Menu Items */}
          {menuItems.map((section, sectionIdx) => (
            <div key={sectionIdx}>
              <h3 className="mb-4 font-bold">{section.title}</h3>
              <ul className="space-y-4 text-muted-foreground">
                {section.links.map((link, linkIdx) => (
                  <li key={linkIdx} className="font-medium hover:text-primary">
                    <Link
                      href={link.url}
                      className="flex items-center gap-2"
                      aria-label={`Go to ${link.text}`}
                    >
                      {link?.icon}
                      <p>{link.text}</p>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* SEO Keyword Section */}
        <SEOKeywordSection className="mx-auto max-w-5xl" />

        {/* Copyright and Bottom Links */}
        <div className="mt-6 flex flex-col justify-between gap-4 border-t pt-6 text-sm font-medium text-muted-foreground md:flex-row md:items-center">
          <p>{copyright}</p>
          <ul className="flex gap-4">
            {bottomLinks.map((link, linkIdx) => (
              <li key={linkIdx} className="underline hover:text-primary">
                <Link href={link.url} aria-label={`Go to ${link.text}`}>
                  {link.text}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Educational Services Schema with Location Targeting */}
      <Script
        id="educational-services-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateEducationalServiceSchema()),
        }}
      />
    </footer>
  );
};

export { Footer };
