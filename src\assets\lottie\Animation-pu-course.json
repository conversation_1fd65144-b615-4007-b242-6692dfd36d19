{"ddd": 0, "h": 950, "w": 1080, "meta": {"g": "@lottiefiles/toolkit-js 0.26.1"}, "layers": [{"ty": 3, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [237, 237, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540, 503, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "ef": [], "ind": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-189.41, 166.36], [-197.71, 166.36], [-197.71, 147.31], [-189.41, 147.31]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-4.55, -0.08], [4.55, -0.03], [0, 0], [0, 0], [4.55, 0.08], [-4.55, 0.03], [0, 0], [0, 0]], "o": [[-4.55, 0.08], [0, 0], [0, 0], [-4.55, -0.03], [4.55, -0.08], [0, 0], [0, 0], [4.55, 0.03]], "v": [[-105.85, 152.39], [-119.5, 152.55], [-133.15, 152.6], [-146.8, 152.55], [-160.45, 152.39], [-146.8, 152.23], [-133.15, 152.18], [-119.5, 152.23]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-4.55, -0.08], [4.55, -0.03], [0, 0], [0, 0], [4.55, 0.08], [-4.55, 0.03], [0, 0], [0, 0]], "o": [[-4.55, 0.08], [0, 0], [0, 0], [-4.55, -0.03], [4.55, -0.08], [0, 0], [0, 0], [4.55, 0.03]], "v": [[-105.85, 155.35], [-119.5, 155.51], [-133.15, 155.56], [-146.8, 155.51], [-160.45, 155.35], [-146.8, 155.19], [-133.15, 155.14], [-119.5, 155.19]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-4.55, -0.08], [4.55, -0.03], [0, 0], [0, 0], [4.55, 0.08], [-4.55, 0.03], [0, 0], [0, 0]], "o": [[-4.55, 0.08], [0, 0], [0, 0], [-4.55, -0.03], [4.55, -0.08], [0, 0], [0, 0], [4.55, 0.03]], "v": [[-105.85, 158.31], [-119.5, 158.47], [-133.15, 158.52], [-146.8, 158.48], [-160.45, 158.31], [-146.8, 158.15], [-133.15, 158.1], [-119.5, 158.15]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-4.55, -0.08], [4.55, -0.03], [0, 0], [0, 0], [4.55, 0.08], [-4.55, 0.03], [0, 0], [0, 0]], "o": [[-4.55, 0.08], [0, 0], [0, 0], [-4.55, -0.03], [4.55, -0.08], [0, 0], [0, 0], [4.55, 0.03]], "v": [[-105.85, 161.28], [-119.5, 161.44], [-133.15, 161.49], [-146.8, 161.44], [-160.45, 161.28], [-146.8, 161.12], [-133.15, 161.07], [-119.5, 161.12]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-160.45, 149.28], [-102.08, 149.28], [-102.08, 164.38], [-160.45, 164.38]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-204.77, 147.31], [-160.45, 147.31], [-160.45, 166.36], [-204.77, 166.36]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-160.45, 147.31], [-102.08, 147.31], [-102.08, 166.36], [-160.45, 166.36]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-102.53, 147.31], [-107.06, 147.31], [-107.06, 133.44], [-102.53, 133.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-130.6, 147.31], [-137.35, 147.31], [-137.35, 133.44], [-130.6, 133.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[5.43, 0.08], [-5.43, 0.03], [0, 0], [0, 0], [-5.43, -0.08], [5.43, -0.03], [0, 0], [0, 0]], "o": [[5.43, -0.08], [0, 0], [0, 0], [5.43, 0.03], [-5.43, 0.08], [0, 0], [0, 0], [-5.43, -0.03]], "v": [[-195.28, 143.6], [-178.99, 143.45], [-171.62, 143.4], [-155.33, 143.44], [-147.96, 143.6], [-155.33, 143.77], [-171.62, 143.81], [-178.99, 143.76]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[5.43, 0.08], [-5.43, 0.03], [0, 0], [0, 0], [-5.43, -0.08], [5.43, -0.03], [0, 0], [0, 0]], "o": [[5.43, -0.08], [0, 0], [0, 0], [5.43, 0.03], [-5.43, 0.08], [0, 0], [0, 0], [-5.43, -0.03]], "v": [[-195.28, 141.45], [-178.99, 141.29], [-171.62, 141.24], [-155.33, 141.29], [-147.96, 141.45], [-155.33, 141.61], [-171.62, 141.66], [-178.99, 141.61]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[5.43, 0.08], [-5.43, 0.03], [0, 0], [0, 0], [-5.43, -0.08], [5.43, -0.03], [0, 0], [0, 0]], "o": [[5.43, -0.08], [0, 0], [0, 0], [5.43, 0.03], [-5.43, 0.08], [0, 0], [0, 0], [-5.43, -0.03]], "v": [[-195.28, 139.29], [-178.99, 139.13], [-171.62, 139.09], [-155.33, 139.13], [-147.96, 139.29], [-155.33, 139.46], [-171.62, 139.5], [-178.99, 139.45]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[5.43, 0.08], [-5.43, 0.03], [0, 0], [0, 0], [-5.43, -0.08], [5.43, -0.03], [0, 0], [0, 0]], "o": [[5.43, -0.08], [0, 0], [0, 0], [5.43, 0.03], [-5.43, 0.08], [0, 0], [0, 0], [-5.43, -0.03]], "v": [[-195.28, 137.14], [-178.99, 136.98], [-171.62, 136.93], [-155.33, 136.98], [-147.96, 137.14], [-155.33, 137.3], [-171.62, 137.35], [-178.99, 137.3]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-147.96, 145.87], [-199.79, 145.87], [-199.79, 134.88], [-147.96, 134.88]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-100.05, 147.31], [-147.96, 147.31], [-147.96, 133.44], [-100.05, 133.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-147.96, 147.31], [-199.79, 147.31], [-199.79, 133.44], [-147.96, 133.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0], [0, 0.13], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0, 0], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.15, 0], [0, 0.13]], "o": [[0, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0]], "v": [[-142.59, 125.55], [-146.44, 125.55], [-146.44, 128.58], [-146.71, 128.82], [-146.97, 128.58], [-146.97, 122.03], [-146.71, 121.79], [-146.44, 122.03], [-146.44, 125.07], [-142.59, 125.07], [-142.59, 122.03], [-142.33, 121.79], [-142.06, 122.03], [-142.06, 128.58], [-142.33, 128.82], [-142.59, 128.58]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.07, 0.09], [-0.1, -0.08], [-0.97, 0.01], [-0.41, 0.28], [0, 0.39], [0.41, 0.26], [0.73, 0.13], [0, 0], [0.46, 0.29], [0, 0.52], [-0.47, 0.32], [-0.71, 0], [-0.59, -0.36], [0.08, -0.11], [0.11, 0.07], [0.83, 0], [0.38, -0.25], [0, -0.36], [-0.37, -0.24], [-0.67, -0.12], [0, 0], [-0.49, -0.31], [0, -0.56], [0.5, -0.34], [0.75, 0], [0.62, 0.46]], "o": [[0.09, -0.1], [0.56, 0.42], [0.65, 0], [0.42, -0.28], [0, -0.4], [-0.41, -0.25], [0, 0], [-0.7, -0.12], [-0.46, -0.28], [0, -0.53], [0.47, -0.32], [0.99, 0], [0.11, 0.07], [-0.06, 0.1], [-0.59, -0.36], [-0.61, 0], [-0.37, 0.26], [0.01, 0.35], [0.38, 0.24], [0, 0], [0.75, 0.13], [0.49, 0.3], [0, 0.56], [-0.5, 0.34], [-1.15, -0.01], [-0.1, -0.08]], "v": [[-154.45, 127.51], [-154.12, 127.48], [-151.66, 128.38], [-150.01, 127.92], [-149.36, 126.88], [-149.98, 125.91], [-151.71, 125.34], [-151.73, 125.34], [-153.5, 124.75], [-154.27, 123.52], [-153.49, 122.2], [-151.68, 121.69], [-149.36, 122.51], [-149.3, 122.82], [-149.62, 122.88], [-151.68, 122.13], [-153.21, 122.56], [-153.8, 123.52], [-153.24, 124.37], [-151.64, 124.91], [-151.61, 124.91], [-149.71, 125.55], [-148.88, 126.88], [-149.73, 128.28], [-151.66, 128.83], [-154.42, 127.82]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.15, 0], [0, 0.13]], "o": [[0, -0.13], [0.15, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0]], "v": [[-157.3, 122.03], [-157.04, 121.79], [-156.78, 122.03], [-156.78, 128.58], [-157.04, 128.82], [-157.3, 128.58]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0.13], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0, 0], [0, -0.14], [0.15, 0]], "o": [[0, 0], [-0.14, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0], [0.15, 0], [0, 0.13], [0, 0]], "v": [[-163.18, 128.82], [-163.2, 128.82], [-163.46, 128.58], [-163.46, 122.03], [-163.2, 121.79], [-162.93, 122.03], [-162.93, 128.33], [-159.34, 128.33], [-159.08, 128.58], [-159.34, 128.82]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0.14], [-0.15, 0], [0, 0], [0, 0], [0, -0.13], [0, 0], [0, 0], [0.05, -0.04], [0.92, 0], [0, 1.99], [-2.15, 0], [-0.67, -0.49], [0.1, -0.1], [0.13, 0.08], [0.8, 0], [0, -1.69], [-1.87, 0], [-0.56, 0.38], [0, 0]], "o": [[-0.15, 0], [0, -0.14], [0, 0], [0, 0], [0.13, 0.01], [0, 0], [0, 0], [-0.01, 0.07], [-0.66, 0.49], [-2.15, -0.01], [0, -1.96], [0.93, 0], [0.1, 0.07], [-0.09, 0.1], [-0.55, -0.42], [-1.87, 0], [0, 1.71], [0.73, 0], [0, 0], [0, 0]], "v": [[-168.14, 125.66], [-168.4, 125.42], [-168.14, 125.17], [-165.82, 125.17], [-165.79, 125.17], [-165.55, 125.42], [-165.55, 127.83], [-165.55, 127.87], [-165.65, 128.05], [-168.07, 128.82], [-171.97, 125.25], [-168.07, 121.69], [-165.64, 122.49], [-165.61, 122.83], [-165.98, 122.87], [-168.07, 122.18], [-171.45, 125.25], [-168.07, 128.33], [-166.09, 127.73], [-166.09, 125.66]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.14, 0], [0, 0.13], [0, 0], [-0.1, 0.07], [-0.07, -0.11], [0, 0], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.15, 0], [0.05, 0.06], [0.01, 0.02]], "o": [[0, 0], [0, 0.13], [-0.14, 0], [0, 0], [-0.03, -0.1], [0.12, -0.07], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0.13], [-0.09, 0], [-0.01, -0.01], [0, 0]], "v": [[-178.81, 122.83], [-178.81, 128.58], [-179.07, 128.82], [-179.33, 128.58], [-179.33, 122.13], [-179.23, 121.86], [-178.87, 121.92], [-174.32, 127.83], [-174.32, 122.1], [-174.07, 121.86], [-173.8, 122.1], [-173.8, 128.58], [-174.07, 128.82], [-174.27, 128.73], [-174.31, 128.69]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.14, 0], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, -0.14], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, -0.14], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, 0.13]], "o": [[0, -0.13], [0, 0], [0, 0], [0.15, 0], [0, 0.14], [0, 0], [0, 0], [0, 0], [0.14, 0], [0, 0.13], [0, 0], [0, 0], [0, 0], [0.15, 0], [0, 0.13], [0, 0], [0, 0], [0, 0], [-0.13, -0.01], [0, 0]], "v": [[-185.71, 122.03], [-185.45, 121.79], [-185.43, 121.79], [-181.59, 121.79], [-181.34, 122.03], [-181.59, 122.28], [-185.18, 122.28], [-185.18, 125.07], [-182.05, 125.07], [-181.79, 125.32], [-182.05, 125.56], [-185.18, 125.56], [-185.18, 128.33], [-181.59, 128.33], [-181.34, 128.58], [-181.59, 128.82], [-185.42, 128.82], [-185.45, 128.82], [-185.47, 128.82], [-185.71, 128.58]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -1.68], [1.8, 0], [0, 1.68], [-1.8, 0]], "o": [[0, 1.68], [-1.8, 0], [0, -1.68], [1.8, 0]], "v": [[-98.48, 125.59], [-101.73, 128.64], [-104.99, 125.59], [-101.73, 122.54]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-110.84, 128.34], [-135.48, 128.34], [-135.48, 122.83], [-110.84, 122.83]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-92.69, 133.44], [-195.28, 133.44], [-195.28, 117.73], [-92.69, 117.73]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2157, 0.2784, 0.3098]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.15, 115.6], [-185.9, 115.6], [-185.9, 112.9], [-170.15, 112.9]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.15, 111.42], [-185.9, 111.42], [-185.9, 108.73], [-170.15, 108.73]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.15, 107.25], [-185.9, 107.25], [-185.9, 104.56], [-170.15, 104.56]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-165.41, 117.73], [-190.65, 117.73], [-190.65, 102.42], [-165.41, 102.42]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.14, 0], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, -0.14], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, -0.14], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, 0.13]], "o": [[0, -0.13], [0, 0], [0, 0], [0.15, 0], [0, 0.14], [0, 0], [0, 0], [0, 0], [0.14, 0], [0, 0.13], [0, 0], [0, 0], [0, 0], [0.15, 0], [0, 0.13], [0, 0], [0, 0], [0, 0], [-0.13, -0.01], [0, 0]], "v": [[-108.65, 106.99], [-108.39, 106.74], [-108.37, 106.74], [-104.53, 106.74], [-104.28, 106.99], [-104.53, 107.24], [-108.12, 107.24], [-108.12, 110.02], [-104.98, 110.02], [-104.73, 110.27], [-104.98, 110.51], [-108.12, 110.51], [-108.12, 113.28], [-104.53, 113.28], [-104.28, 113.54], [-104.53, 113.78], [-108.36, 113.78], [-108.39, 113.78], [-108.41, 113.78], [-108.65, 113.54]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-2.15, 0.01], [-0.65, -0.47], [0.1, -0.09], [0.11, 0.08], [0.79, 0], [0, -1.72], [-1.87, 0], [-0.56, 0.44], [-0.07, -0.12], [0.12, -0.08], [0.92, 0], [0, 1.96]], "o": [[0.92, 0], [0.12, 0.09], [-0.09, 0.12], [-0.58, -0.42], [-1.87, 0], [0, 1.68], [0.79, 0], [0.13, -0.08], [0.11, 0.09], [-0.65, 0.47], [-2.15, 0], [0, -2]], "v": [[-113.33, 106.64], [-110.91, 107.42], [-110.87, 107.77], [-111.23, 107.82], [-113.33, 107.14], [-116.71, 110.23], [-113.33, 113.28], [-111.26, 112.6], [-110.88, 112.66], [-110.91, 113.01], [-113.33, 113.78], [-117.23, 110.23]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.14, 0], [0, 0.13], [0, 0], [-0.1, 0.07], [-0.07, -0.11], [0, 0], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.15, 0], [0.05, 0.06], [0.01, 0.02]], "o": [[0, 0], [0, 0.13], [-0.14, 0], [0, 0], [-0.03, -0.1], [0.12, -0.07], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0.13], [-0.09, 0], [-0.01, -0.01], [0, 0]], "v": [[-124.61, 107.78], [-124.61, 113.54], [-124.87, 113.78], [-125.13, 113.54], [-125.13, 107.09], [-125.03, 106.81], [-124.67, 106.88], [-120.12, 112.78], [-120.12, 107.06], [-119.87, 106.81], [-119.6, 107.06], [-119.6, 113.54], [-119.87, 113.78], [-120.07, 113.69], [-120.11, 113.65]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.14, 0], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, -0.14], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, -0.14], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, 0.13]], "o": [[0, -0.13], [0, 0], [0, 0], [0.15, 0], [0, 0.14], [0, 0], [0, 0], [0, 0], [0.14, 0], [0, 0.13], [0, 0], [0, 0], [0, 0], [0.15, 0], [0, 0.13], [0, 0], [0, 0], [0, 0], [-0.13, -0.01], [0, 0]], "v": [[-131.51, 106.99], [-131.25, 106.74], [-131.23, 106.74], [-127.39, 106.74], [-127.14, 106.99], [-127.39, 107.24], [-130.98, 107.24], [-130.98, 110.02], [-127.84, 110.02], [-127.59, 110.27], [-127.84, 110.51], [-130.98, 110.51], [-130.98, 113.28], [-127.39, 113.28], [-127.14, 113.54], [-127.39, 113.78], [-131.22, 113.78], [-131.25, 113.78], [-131.27, 113.78], [-131.51, 113.54]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.15, 0], [0, 0.13]], "o": [[0, -0.13], [0.15, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0]], "v": [[-134.7, 106.99], [-134.44, 106.74], [-134.17, 106.99], [-134.17, 113.54], [-134.44, 113.78], [-134.7, 113.54]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-2.15, 0.01], [-0.65, -0.47], [0.1, -0.09], [0.11, 0.08], [0.79, 0], [0, -1.72], [-1.87, 0], [-0.56, 0.44], [-0.07, -0.12], [0.12, -0.08], [0.92, 0], [0, 1.96]], "o": [[0.92, 0], [0.12, 0.09], [-0.09, 0.12], [-0.58, -0.42], [-1.87, 0], [0, 1.68], [0.79, 0], [0.13, -0.08], [0.11, 0.09], [-0.65, 0.47], [-2.15, 0], [0, -2]], "v": [[-139.46, 106.64], [-137.04, 107.42], [-137, 107.77], [-137.36, 107.82], [-139.46, 107.14], [-142.84, 110.23], [-139.46, 113.28], [-137.38, 112.6], [-137.01, 112.66], [-137.04, 113.01], [-139.46, 113.78], [-143.36, 110.23]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.07, 0.09], [-0.1, -0.08], [-0.97, 0.01], [-0.41, 0.28], [0, 0.39], [0.41, 0.26], [0.73, 0.13], [0, 0], [0.46, 0.29], [0, 0.52], [-0.47, 0.32], [-0.71, 0], [-0.59, -0.36], [0.08, -0.11], [0.11, 0.07], [0.83, 0], [0.38, -0.25], [0, -0.36], [-0.37, -0.24], [-0.67, -0.12], [0, 0], [-0.49, -0.31], [0, -0.56], [0.5, -0.34], [0.75, 0], [0.62, 0.46]], "o": [[0.09, -0.1], [0.56, 0.42], [0.65, 0], [0.42, -0.28], [0, -0.4], [-0.41, -0.25], [0, 0], [-0.7, -0.12], [-0.46, -0.28], [0, -0.53], [0.47, -0.32], [0.99, 0], [0.11, 0.07], [-0.06, 0.1], [-0.59, -0.36], [-0.61, 0], [-0.37, 0.26], [0.01, 0.35], [0.38, 0.24], [0, 0], [0.75, 0.13], [0.49, 0.3], [0, 0.56], [-0.5, 0.34], [-1.15, -0.01], [-0.1, -0.08]], "v": [[-150.51, 112.46], [-150.18, 112.43], [-147.72, 113.34], [-146.07, 112.87], [-145.43, 111.83], [-146.04, 110.86], [-147.77, 110.29], [-147.79, 110.29], [-149.56, 109.7], [-150.33, 108.47], [-149.55, 107.16], [-147.74, 106.64], [-145.43, 107.46], [-145.36, 107.77], [-145.68, 107.83], [-147.74, 107.09], [-149.27, 107.51], [-149.86, 108.47], [-149.3, 109.33], [-147.7, 109.86], [-147.68, 109.86], [-145.77, 110.5], [-144.94, 111.83], [-145.79, 113.23], [-147.72, 113.79], [-150.48, 112.77]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-98.66, 117.73], [-199.1, 117.73], [-199.1, 102.42], [-98.66, 102.42]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0], [0, 0.13], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0, 0], [0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.15, 0], [0, 0.13]], "o": [[0, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0], [0, 0], [0, -0.13], [0.15, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0]], "v": [[-128.58, 172.28], [-132.43, 172.28], [-132.43, 175.31], [-132.7, 175.55], [-132.96, 175.31], [-132.96, 168.76], [-132.7, 168.52], [-132.43, 168.76], [-132.43, 171.8], [-128.58, 171.8], [-128.58, 168.76], [-128.32, 168.52], [-128.05, 168.76], [-128.05, 175.31], [-128.32, 175.55], [-128.58, 175.31]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0.14], [-0.14, 0], [0, 0], [0, -0.13], [0.14, 0], [0, 0], [0, 0], [0.14, 0], [0, 0.13]], "o": [[0, 0], [-0.14, 0], [0, -0.13], [0, 0], [0.14, 0], [0, 0.14], [0, 0], [0, 0], [0, 0.13], [-0.14, 0], [0, 0]], "v": [[-137.96, 169.01], [-140.22, 169.01], [-140.47, 168.76], [-140.22, 168.52], [-135.18, 168.52], [-134.93, 168.76], [-135.18, 169.01], [-137.44, 169.01], [-137.44, 175.31], [-137.7, 175.55], [-137.96, 175.31]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-144.86, 173.24], [-141.83, 173.24], [-143.34, 169.47]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.11, 0], [0.03, 0], [-0.05, 0.12], [0, 0], [-0.11, 0], [-0.03, -0.09], [0, 0], [0.13, -0.05], [0.03, 0], [0.04, 0.1]], "o": [[0, 0], [0, 0], [-0.04, 0.1], [-0.02, 0], [-0.13, -0.05], [0, 0], [0.04, -0.09], [0.12, 0], [0, 0], [0.05, 0.12], [-0.04, 0], [-0.1, 0], [0, 0]], "v": [[-141.64, 173.74], [-145.06, 173.74], [-145.72, 175.39], [-145.97, 175.55], [-146.06, 175.54], [-146.21, 175.22], [-143.61, 168.7], [-143.35, 168.55], [-143.1, 168.7], [-140.49, 175.22], [-140.63, 175.54], [-140.73, 175.55], [-140.98, 175.39]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.08, 0], [0, 0], [0.05, 0.08], [0, 0], [0, 0], [0.14, 0], [0, 0.13], [0, 0], [-0.06, 0.04], [-0.01, 0.01], [-0.08, -0.11], [0, 0], [0, 0], [-0.12, -0.07], [-0.02, -0.03], [0, -0.04], [0, 0], [0.14, 0], [0, 0.13]], "o": [[0, 0], [-0.05, 0.08], [0, 0], [-0.1, 0], [0, 0], [0, 0], [0, 0.13], [-0.15, 0], [0, 0], [0, -0.08], [0, -0.01], [0.12, -0.07], [0, 0], [0, 0], [0.08, -0.11], [0.03, 0.03], [0.02, 0.04], [0, 0], [0, 0.13], [-0.15, 0], [0, 0]], "v": [[-148.3, 169.53], [-150.7, 172.65], [-150.91, 172.76], [-150.93, 172.76], [-151.16, 172.65], [-153.56, 169.54], [-153.56, 175.31], [-153.82, 175.55], [-154.08, 175.31], [-154.08, 168.8], [-153.98, 168.61], [-153.96, 168.59], [-153.59, 168.65], [-150.92, 172.12], [-148.27, 168.65], [-147.9, 168.59], [-147.83, 168.67], [-147.78, 168.8], [-147.78, 175.31], [-148.04, 175.55], [-148.3, 175.31]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-102.44, 178.26], [-112.25, 178.26], [-112.25, 166.37], [-102.44, 166.37]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-167.26, 178.26], [-171.36, 178.26], [-171.36, 166.37], [-167.26, 166.37]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-90.62, 178.26], [-191.06, 178.26], [-191.06, 166.37], [-90.62, 166.37]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2157, 0.2784, 0.3098]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [125.79, 177.38, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [125.79, 177.38, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 92}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0.78], "t": 95}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 97}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 101}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [1], "t": 103}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 106}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 110}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0.78], "t": 112}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-0.13], "t": 115}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 119}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0.78], "t": 121}, {"s": [0], "t": 124}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.46, -4.33], [-4.33, 0], [-0.45, 4.33], [4.33, 0]], "o": [[-0.45, 4.33], [4.33, 0], [0.46, -4.33], [-4.33, 0]], "v": [[139.71, 141], [146.73, 148.84], [155.39, 141], [148.38, 133.16]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[81.72, 179.15], [199.32, 179.15], [215.94, 99.28], [98.34, 99.28]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.4, 173.75], [22.4, 179.16], [83.13, 179.16], [83.13, 173.75]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.651, 0.651, 0.651]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.4, 173.75], [22.4, 179.16], [143.07, 179.16], [143.07, 173.75]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[6.77, 1.99], [-0.09, -0.06], [-0.91, -5.2], [2.84, -1.3], [-0.15, 0], [-1.07, -1.92], [-0.44, -1.49], [0.07, 0.55], [0.01, 0.04]], "o": [[-0.11, -0.03], [4.64, 3.02], [-1.13, -2.15], [-0.15, 0.07], [2.15, -0.04], [0.76, 1.36], [0.15, 0.5], [0, -0.04], [1.38, -6.39]], "v": [[-53.59, -22.84], [-53.67, -22.66], [-46.37, -9.25], [-52.19, -11.53], [-52.11, -11.23], [-47.47, -8.7], [-45.88, -4.24], [-44.94, -4.38], [-44.96, -4.51]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6.77, -2.17], [-6.66, -4.07], [0, 0]], "o": [[0, 0], [-6.77, 2.17], [6.66, 4.07], [0, 0]], "v": [[-41.11, -15.68], [-54.68, -28.82], [-51.5, -1.66], [-40.96, -3.68]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-44.25, -10.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [-44.25, -10.5], "t": 8, "ti": [-1, 0], "to": [1, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [-38.25, -10.5], "t": 28, "ti": [0, 0], "to": [1, 0]}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [-38.25, -10.5], "t": 84, "ti": [1, 0], "to": [0, 0]}, {"s": [-44.25, -10.5], "t": 103}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.19, -1.55], [0, 0], [-3.92, -0.07], [-0.95, -0.27], [0, 0], [2.83, 0.05]], "o": [[0, 0], [0.49, -0.64], [3.66, 0.07], [0, 0], [-6.44, -1.8], [-2.59, -0.04]], "v": [[-58.2, -17.39], [-59.57, -18.45], [-50.73, -24.64], [-23.66, -18.04], [-24.12, -16.38], [-50.76, -22.9]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [-24.32, -16.93]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [100, 100], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.07, "y": 1}, "s": [81, 100], "t": 28}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0, "y": 1}, "s": [81, 100], "t": 84}, {"s": [100, 100], "t": 103}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-24.32, -16.93]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-23.75, -17.25]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0.33}, "i": {"x": 0.27, "y": 0.27}, "s": [-23.75, -17.25], "t": 8, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.33, "y": 0.33}, "i": {"x": 0.14, "y": 0.14}, "s": [-23.75, -17.25], "t": 28, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0, "y": 0}, "s": [-23.75, -17.25], "t": 84, "ti": [0, 0], "to": [0, 0]}, {"s": [-23.75, -17.25], "t": 103}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.21, -0.11], [0, 0], [-2.51, -3.89], [0, 0]], "o": [[0, 0], [2.23, -1.27], [0, 0], [-2.25, -3.49]], "v": [[3.93, -9.62], [3.07, -11.13], [12.67, -9.89], [11.22, -8.95]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[2.08, 0.63], [0.26, 0.05], [2.17, -2.08], [0.64, -3.56], [-5.11, -0.91], [-1.3, 7.22], [1.36, 2.82]], "o": [[-0.25, -0.07], [-2.4, -0.43], [-2.26, 2.17], [-1.3, 7.22], [5.09, 0.92], [0.64, -3.56], [-1.17, -2.42]], "v": [[25.06, -19.43], [24.31, -19.61], [17.21, -17.05], [12.71, -8.18], [19.59, 6.59], [31.18, -4.85], [30.06, -14.74]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.32, 0.1], [-1.42, 7.89], [-2.57, 2.46], [-3.07, -0.55], [-1.6, -3.32], [0.72, -3.98], [6.21, 1.12]], "o": [[-5.63, -1.71], [0.72, -3.98], [2.66, -2.55], [3.08, 0.55], [1.55, 3.21], [-1.5, 8.34], [-0.33, -0.06]], "v": [[18.25, 8.37], [10.69, -8.54], [15.79, -18.54], [24.67, -21.64], [31.91, -15.63], [33.21, -4.49], [19.23, 8.61]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[5.68, 1.73], [1.13, 0.05], [0.34, -7.33], [-7.33, -0.33], [-0.34, 7.33]], "o": [[-1.04, -0.32], [-7.33, -0.34], [-0.33, 7.33], [7.33, 0.34], [0.28, -6.2]], "v": [[-6.88, -24.74], [-10.14, -25.3], [-24.05, -12.61], [-11.36, 1.29], [2.55, -11.4]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.2, 0.36], [-0.33, 7.16], [-8.46, -0.39], [0.39, -8.46], [0, 0], [8.46, 0.39]], "o": [[-6.56, -1.99], [0.39, -8.46], [8.47, 0.39], [0, 0], [-0.39, 8.46], [-1.31, -0.06]], "v": [[-15.22, 2.7], [-26.1, -12.71], [-10.05, -27.35], [4.6, -11.3], [4.6, -11.3], [-11.45, 3.34]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-24, -16.75]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-24, -16.75]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[0.77, 0.5], [0.38, -2.08], [0.71, 0.09], [0.03, 0.03], [1.69, 3.4], [-0.17, -0.16], [-3.24, -0.64], [-0.32, -0.11], [-0.86, -0.39], [-0.35, 5.33], [-0.02, -0.31], [0.26, -6.32]], "o": [[0, 0], [-0.04, 0.19], [-0.04, 0.01], [-3.77, -0.09], [-0.11, -0.23], [2.37, 2.51], [0.25, -0.55], [1.34, 0.48], [0.19, -2.93], [0.02, -0.33], [0.63, 8.2], [-0.02, 0.49]], "v": [[6.61, 4.93], [6.04, 11.52], [4.77, 11.63], [4.69, 11.62], [-4.23, 6.19], [-3.86, 5.96], [4.68, 10.18], [5.08, 2.47], [9.6, 4.39], [10.28, -12.21], [10.84, -12.24], [11.41, 6.56]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[0.77, 0.5], [0.38, -2.08], [0.71, 0.09], [0.03, 0.03], [1.69, 3.4], [-0.17, -0.16], [-3.24, -0.64], [-0.32, -0.11], [-0.86, -0.39], [-0.35, 5.33], [-0.02, -0.31], [0.26, -6.32]], "o": [[0, 0], [-0.04, 0.19], [-0.04, 0.01], [-3.77, -0.09], [-0.11, -0.23], [2.37, 2.51], [0.25, -0.55], [1.34, 0.48], [0.19, -2.93], [0.02, -0.33], [0.63, 8.2], [-0.02, 0.49]], "v": [[6.61, 4.93], [6.04, 11.52], [4.77, 11.63], [4.69, 11.62], [-4.23, 6.19], [-3.86, 5.96], [4.68, 10.18], [5.08, 2.47], [9.6, 4.39], [10.28, -12.21], [10.84, -12.24], [11.41, 6.56]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[0.77, 0.5], [0.38, -2.08], [0.71, 0.09], [0.03, 0.03], [1.69, 3.4], [-0.17, -0.16], [-3.24, -0.64], [-0.32, -0.11], [-0.86, -0.39], [-0.35, 5.33], [-0.02, -0.31], [0.26, -6.32]], "o": [[0, 0], [-0.04, 0.19], [-0.04, 0.01], [-3.77, -0.09], [-0.11, -0.23], [2.37, 2.51], [0.25, -0.55], [1.34, 0.48], [0.19, -2.93], [0.02, -0.33], [0.63, 8.2], [-0.02, 0.49]], "v": [[6.61, 4.93], [6.04, 11.52], [4.77, 11.63], [4.69, 11.62], [-4.23, 6.19], [-3.86, 5.96], [4.68, 10.18], [5.08, 2.47], [9.6, 4.39], [10.28, -12.21], [10.84, -12.24], [11.41, 6.56]]}], "t": 84}, {"s": [{"c": true, "i": [[0.77, 0.5], [0.38, -2.08], [0.71, 0.09], [0.03, 0.03], [1.69, 3.4], [-0.17, -0.16], [-3.24, -0.64], [-0.32, -0.11], [-0.86, -0.39], [-0.35, 5.33], [-0.02, -0.31], [0.26, -6.32]], "o": [[0, 0], [-0.04, 0.19], [-0.04, 0.01], [-3.77, -0.09], [-0.11, -0.23], [2.37, 2.51], [0.25, -0.55], [1.34, 0.48], [0.19, -2.93], [0.02, -0.33], [0.63, 8.2], [-0.02, 0.49]], "v": [[6.61, 4.93], [6.04, 11.52], [4.77, 11.63], [4.69, 11.62], [-4.23, 6.19], [-3.86, 5.96], [4.68, 10.18], [5.08, 2.47], [9.6, 4.39], [10.28, -12.21], [10.84, -12.24], [11.41, 6.56]]}], "t": 103}]}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[-0.08, -0.12], [-1.03, -0.34], [-1.02, 0.14], [0.08, -0.07], [1.29, 0.56], [0.03, 1.28]], "o": [[0.53, 0.85], [0.99, 0.33], [0.1, -0.01], [-1.03, 0.94], [-1.26, -0.55], [0, -0.18]], "v": [[-7.65, 13.35], [-5.61, 15.65], [-2.3, 15.67], [-2.21, 15.85], [-6.29, 16.54], [-7.96, 13.42]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[-0.08, -0.12], [-1.03, -0.34], [-1.02, 0.14], [0.08, -0.07], [1.29, 0.56], [0.03, 1.28]], "o": [[0.53, 0.85], [0.99, 0.33], [0.1, -0.01], [-1.03, 0.94], [-1.26, -0.55], [0, -0.18]], "v": [[-7.65, 13.35], [-5.61, 15.65], [-2.3, 15.67], [-2.21, 15.85], [-6.29, 16.54], [-7.96, 13.42]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[-0.08, -0.12], [-1.03, -0.34], [-1.02, 0.14], [0.08, -0.07], [1.29, 0.56], [0.03, 1.28]], "o": [[0.53, 0.85], [0.99, 0.33], [0.1, -0.01], [-1.03, 0.94], [-1.26, -0.55], [0, -0.18]], "v": [[-7.65, 13.35], [-5.61, 15.65], [-2.3, 15.67], [-2.21, 15.85], [-6.29, 16.54], [-7.96, 13.42]]}], "t": 84}, {"s": [{"c": true, "i": [[-0.08, -0.12], [-1.03, -0.34], [-1.02, 0.14], [0.08, -0.07], [1.29, 0.56], [0.03, 1.28]], "o": [[0.53, 0.85], [0.99, 0.33], [0.1, -0.01], [-1.03, 0.94], [-1.26, -0.55], [0, -0.18]], "v": [[-7.65, 13.35], [-5.61, 15.65], [-2.3, 15.67], [-2.21, 15.85], [-6.29, 16.54], [-7.96, 13.42]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-1.37, -0.08], [-1.03, 0.05], [-0.98, 0.72], [0.33, 0.55], [1.41, 0], [1.18, -0.96]], "o": [[1.04, 0.06], [1.17, -0.05], [0.53, -0.39], [-0.76, -1.27], [-1.52, 0], [-1.14, 0.93]], "v": [[-7.29, -19.12], [-4.25, -19.22], [-1.17, -19.91], [-0.84, -21.79], [-4.45, -23.35], [-8.33, -21.99]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.31, 0.39], [0.95, 0.4], [0.67, 1.01], [-0.49, 0.4], [-1.32, -0.49], [-0.77, -1.31]], "o": [[-0.99, -0.3], [-1.08, -0.45], [-0.36, -0.54], [1.15, -0.93], [1.43, 0.53], [0.75, 1.27]], "v": [[22.39, -15.3], [19.57, -16.44], [16.92, -18.15], [17.26, -20.03], [21.18, -20.25], [24.35, -17.64]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [9, -20.12]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.55, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [9, -20.12], "t": 8, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.14, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [8, -12.12], "t": 14, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.33, "y": 0.33}, "i": {"x": 0.83, "y": 0.83}, "s": [9, -20.12], "t": 28, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.55, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [9, -20.12], "t": 83, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.14, "y": 0}, "i": {"x": 0.38, "y": 1}, "s": [8, -12.12], "t": 89, "ti": [0, 0], "to": [0, 0]}, {"s": [9, -20.12], "t": 103}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[4.02, 0.77], [-4.44, -0.85]], "o": [[-4.79, -0.91], [4.37, 0.83]], "v": [[-4.15, -13.02], [-5.98, -3.44]]}], "t": 8}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[4.02, 0.77], [-4.44, -0.85]], "o": [[-4.79, -0.91], [4.37, 0.83]], "v": [[-5.53, -5.1], [-5.98, -3.44]]}], "t": 14}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[4.02, 0.77], [-4.44, -0.85]], "o": [[-4.79, -0.91], [4.37, 0.83]], "v": [[-4.15, -13.02], [-5.98, -3.44]]}], "t": 28}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[4.02, 0.77], [-4.44, -0.85]], "o": [[-4.79, -0.91], [4.37, 0.83]], "v": [[-4.15, -13.02], [-5.98, -3.44]]}], "t": 83}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[4.02, 0.77], [-4.44, -0.85]], "o": [[-4.79, -0.91], [4.37, 0.83]], "v": [[-5.53, -5.1], [-5.98, -3.44]]}], "t": 89}, {"s": [{"c": true, "i": [[4.02, 0.77], [-4.44, -0.85]], "o": [[-4.79, -0.91], [4.37, 0.83]], "v": [[-4.15, -13.02], [-5.98, -3.44]]}], "t": 103}]}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[-4.05, -0.61], [4.47, 0.68]], "o": [[4.83, 0.73], [-4.4, -0.67]], "v": [[18.16, -9.81], [16.7, -0.15]]}], "t": 8}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[-4.05, -0.61], [4.47, 0.68]], "o": [[4.83, 0.73], [-4.4, -0.67]], "v": [[17.07, -1.68], [16.7, -0.15]]}], "t": 14}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[-4.05, -0.61], [4.47, 0.68]], "o": [[4.83, 0.73], [-4.4, -0.67]], "v": [[18.16, -9.81], [16.7, -0.15]]}], "t": 28}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[-4.05, -0.61], [4.47, 0.68]], "o": [[4.83, 0.73], [-4.4, -0.67]], "v": [[18.16, -9.81], [16.7, -0.15]]}], "t": 83}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[-4.05, -0.61], [4.47, 0.68]], "o": [[4.83, 0.73], [-4.4, -0.67]], "v": [[17.07, -1.68], [16.7, -0.15]]}], "t": 89}, {"s": [{"c": true, "i": [[-4.05, -0.61], [4.47, 0.68]], "o": [[4.83, 0.73], [-4.4, -0.67]], "v": [[18.16, -9.81], [16.7, -0.15]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [7.63, -6]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [7.63, -6], "t": 36, "ti": [0, 0], "to": [0.17, 0]}, {"o": {"x": 0.79, "y": 0}, "i": {"x": 0, "y": 1}, "s": [8.63, -6], "t": 41, "ti": [0.29, -0.23], "to": [0, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [7.63, -6], "t": 50, "ti": [0.13, -0.23], "to": [-0.29, 0.23]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [6.86, -4.63], "t": 52, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [6.86, -4.63], "t": 61, "ti": [-0.13, 0.04], "to": [0.13, -0.04]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [7.67, -4.86], "t": 63, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [7.67, -4.86], "t": 71, "ti": [-0.01, 0.12], "to": [0.01, -0.12]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [7.7, -5.57], "t": 74, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [7.7, -5.57], "t": 107, "ti": [-0.15, 0.07], "to": [0.15, -0.07]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [8.63, -6], "t": 111, "ti": [0, 0], "to": [0, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [8.63, -6], "t": 119, "ti": [0.17, 0], "to": [-0.17, 0]}, {"s": [7.63, -6], "t": 123}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-24.43, -17.42]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [100, 100], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [109, 100], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [109, 100], "t": 84}, {"s": [100, 100], "t": 103}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-24.43, -17.42]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [6.75, -6.75]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [100, 100], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [100, 100], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [100, 100], "t": 84}, {"s": [100, 100], "t": 103}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [6.75, -6.75], "t": 8, "ti": [1.17, -0.5], "to": [-1.17, 0.5]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [-0.25, -3.75], "t": 28, "ti": [0, 0], "to": [-1.17, 0.5]}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [-0.25, -3.75], "t": 84, "ti": [-1.17, 0.5], "to": [0, 0]}, {"s": [6.75, -6.75], "t": 103}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [0], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [1], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [1], "t": 84}, {"s": [0], "t": 103}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[-3.68, -1.36], [-0.59, 12.87], [-9.43, 26.26], [-14.99, -4.82], [0, 0], [6.13, 10.06], [7.48, 10.87], [0, 0], [12.73, 2.02], [0, 0], [6.24, -10.89], [0, 0], [0.83, -10.61]], "o": [[12.27, 4.55], [0, 0], [0, 0], [0, 0], [3.71, 0.01], [0, 0], [-7.48, -10.87], [0, 0], [-12.73, -2.02], [0, 0], [-6.24, 10.89], [0, 0], [-0.53, 6.83]], "v": [[-54.27, -18.39], [-30.51, -39.73], [10.92, -51.83], [29.05, -33.49], [29.88, -22.54], [33.94, -41.23], [29.79, -65.6], [11.01, -74.08], [-7.62, -84.91], [-28.67, -77.22], [-52.33, -68.01], [-54.82, -47.49], [-61.13, -33.48]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[-3.68, -1.36], [-0.59, 12.87], [-9.43, 26.26], [-14.99, -4.82], [0, 0], [6.13, 10.06], [7.48, 10.87], [0, 0], [12.73, 2.02], [0, 0], [6.61, -10.67], [0, 0], [0.83, -10.61]], "o": [[12.27, 4.55], [0, 0], [0, 0], [0, 0], [3.71, 0.01], [0, 0], [-7.48, -10.87], [0, 0], [-12.73, -2.02], [0, 0], [-6.61, 10.67], [0, 0], [-0.53, 6.83]], "v": [[-54.27, -18.39], [-32.61, -35.23], [8.81, -47.33], [26.94, -28.99], [29.88, -22.54], [35.97, -41.41], [31.82, -65.78], [13.04, -74.25], [-7.62, -84.91], [-26.4, -79.36], [-50.37, -70.96], [-53.56, -50.54], [-61.13, -33.48]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[-3.68, -1.36], [-0.59, 12.87], [-9.43, 26.26], [-14.99, -4.82], [0, 0], [6.13, 10.06], [7.48, 10.87], [0, 0], [12.73, 2.02], [0, 0], [6.61, -10.67], [0, 0], [0.83, -10.61]], "o": [[12.27, 4.55], [0, 0], [0, 0], [0, 0], [3.71, 0.01], [0, 0], [-7.48, -10.87], [0, 0], [-12.73, -2.02], [0, 0], [-6.61, 10.67], [0, 0], [-0.53, 6.83]], "v": [[-54.27, -18.39], [-32.61, -35.23], [8.81, -47.33], [26.94, -28.99], [29.88, -22.54], [35.97, -41.41], [31.82, -65.78], [13.04, -74.25], [-7.62, -84.91], [-26.4, -79.36], [-50.37, -70.96], [-53.56, -50.54], [-61.13, -33.48]]}], "t": 84}, {"s": [{"c": true, "i": [[-3.68, -1.36], [-0.59, 12.87], [-9.43, 26.26], [-14.99, -4.82], [0, 0], [6.13, 10.06], [7.48, 10.87], [0, 0], [12.73, 2.02], [0, 0], [6.24, -10.89], [0, 0], [0.83, -10.61]], "o": [[12.27, 4.55], [0, 0], [0, 0], [0, 0], [3.71, 0.01], [0, 0], [-7.48, -10.87], [0, 0], [-12.73, -2.02], [0, 0], [-6.24, 10.89], [0, 0], [-0.53, 6.83]], "v": [[-54.27, -18.39], [-30.51, -39.73], [10.92, -51.83], [29.05, -33.49], [29.88, -22.54], [33.94, -41.23], [29.79, -65.6], [11.01, -74.08], [-7.62, -84.91], [-28.67, -77.22], [-52.33, -68.01], [-54.82, -47.49], [-61.13, -33.48]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[-0.18, 4.57], [0.87, -2.09], [1.23, -1.85], [4.29, -0.93], [4.36, 0.83], [2.11, 0.82], [2.15, 0.82], [-2.02, -1.11], [-2.15, -0.86], [-4.69, 1.21], [-2.44, 4.02]], "o": [[-0.42, 2.25], [-0.76, 2.13], [-2.53, 3.65], [-4.27, 1.25], [-2.14, -0.7], [-2.1, -0.85], [1.98, 1.14], [2.08, 0.98], [4.45, 1.11], [4.63, -1.13], [2.42, -4.02]], "v": [[10.88, -54.41], [9.04, -47.87], [5.98, -41.9], [-4.54, -34.57], [-17.72, -35.08], [-24.17, -37.15], [-30.51, -39.73], [-24.48, -36.43], [-18.05, -33.89], [-4.16, -33.01], [7.01, -41.23]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[-0.18, 4.57], [0.87, -2.09], [1.23, -1.85], [4.29, -0.93], [4.36, 0.83], [2.11, 0.82], [2.15, 0.82], [-2.02, -1.11], [-2.15, -0.86], [-4.69, 1.21], [-2.44, 4.02]], "o": [[-0.42, 2.25], [-0.76, 2.13], [-2.53, 3.65], [-4.27, 1.25], [-2.14, -0.7], [-2.1, -0.85], [1.98, 1.14], [2.08, 0.98], [4.45, 1.11], [4.63, -1.13], [2.42, -4.02]], "v": [[9.15, -49.41], [7.32, -42.86], [4.26, -36.9], [-6.26, -29.56], [-19.44, -30.07], [-25.9, -32.15], [-32.23, -34.72], [-26.2, -31.42], [-19.77, -28.88], [-5.88, -28], [5.29, -36.22]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[-0.18, 4.57], [0.87, -2.09], [1.23, -1.85], [4.29, -0.93], [4.36, 0.83], [2.11, 0.82], [2.15, 0.82], [-2.02, -1.11], [-2.15, -0.86], [-4.69, 1.21], [-2.44, 4.02]], "o": [[-0.42, 2.25], [-0.76, 2.13], [-2.53, 3.65], [-4.27, 1.25], [-2.14, -0.7], [-2.1, -0.85], [1.98, 1.14], [2.08, 0.98], [4.45, 1.11], [4.63, -1.13], [2.42, -4.02]], "v": [[8.77, -49.92], [6.93, -43.37], [3.88, -37.41], [-6.64, -30.07], [-19.82, -30.58], [-26.28, -32.65], [-32.61, -35.23], [-26.58, -31.93], [-20.15, -29.39], [-6.27, -28.51], [4.9, -36.73]]}], "t": 84}, {"s": [{"c": true, "i": [[-0.18, 4.57], [0.87, -2.09], [1.23, -1.85], [4.29, -0.93], [4.36, 0.83], [2.11, 0.82], [2.15, 0.82], [-2.02, -1.11], [-2.15, -0.86], [-4.69, 1.21], [-2.44, 4.02]], "o": [[-0.42, 2.25], [-0.76, 2.13], [-2.53, 3.65], [-4.27, 1.25], [-2.14, -0.7], [-2.1, -0.85], [1.98, 1.14], [2.08, 0.98], [4.45, 1.11], [4.63, -1.13], [2.42, -4.02]], "v": [[10.88, -54.41], [9.04, -47.87], [5.98, -41.9], [-4.54, -34.57], [-17.72, -35.08], [-24.17, -37.15], [-30.51, -39.73], [-24.48, -36.43], [-18.05, -33.89], [-4.16, -33.01], [7.01, -41.23]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0.02, -1.37]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[-4.31, -0.54], [4.4, -0.59], [3.08, -3.41], [0.82, -2.07], [0.14, -2.23], [-2.75, -3.4], [-0.54, 4.16], [-2.7, 3.09], [-1.85, 0.97], [-2.09, 0.41]], "o": [[-4.19, -1.19], [-4.39, 0.57], [-1.47, 1.76], [-0.88, 2.05], [-0.32, 4.46], [-2.17, -3.76], [0.55, -4.11], [1.35, -1.56], [1.88, -0.9], [4.17, -0.82]], "v": [[-28.67, -77.22], [-41.72, -78.15], [-53.57, -72.22], [-56.98, -66.42], [-58.54, -59.92], [-54.82, -47.49], [-57.31, -59.8], [-52.36, -71.17], [-47.49, -74.96], [-41.52, -76.94]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.14, "y": 1}, "s": [{"c": true, "i": [[-4.31, -0.49], [4.39, -0.62], [3.04, -3.45], [0.8, -2.08], [0.14, -2.23], [-2.78, -3.37], [-0.51, 4.17], [-2.64, 3.14], [-1.84, 0.99], [-2.08, 0.43]], "o": [[-4.21, -1.14], [-4.38, 0.62], [-1.45, 1.78], [-0.86, 2.06], [-0.27, 4.47], [-2.21, -3.73], [0.51, -4.12], [1.33, -1.58], [1.87, -0.92], [4.16, -0.86]], "v": [[-26.98, -80.03], [-40.04, -80.82], [-51.82, -74.76], [-55.17, -68.92], [-56.66, -62.4], [-52.8, -50.01], [-55.43, -62.3], [-50.6, -73.71], [-45.77, -77.56], [-39.82, -79.61]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[-4.31, -0.49], [4.39, -0.62], [3.04, -3.45], [0.8, -2.08], [0.14, -2.23], [-2.78, -3.37], [-0.51, 4.17], [-2.64, 3.14], [-1.84, 0.99], [-2.08, 0.43]], "o": [[-4.21, -1.14], [-4.38, 0.62], [-1.45, 1.78], [-0.86, 2.06], [-0.27, 4.47], [-2.21, -3.73], [0.51, -4.12], [1.33, -1.58], [1.87, -0.92], [4.16, -0.86]], "v": [[-26.98, -80.03], [-40.04, -80.82], [-51.82, -74.76], [-55.17, -68.92], [-56.66, -62.4], [-52.8, -50.01], [-55.43, -62.3], [-50.6, -73.71], [-45.77, -77.56], [-39.82, -79.61]]}], "t": 84}, {"s": [{"c": true, "i": [[-4.31, -0.54], [4.4, -0.59], [3.08, -3.41], [0.82, -2.07], [0.14, -2.23], [-2.75, -3.4], [-0.54, 4.16], [-2.7, 3.09], [-1.85, 0.97], [-2.09, 0.41]], "o": [[-4.19, -1.19], [-4.39, 0.57], [-1.47, 1.76], [-0.88, 2.05], [-0.32, 4.46], [-2.17, -3.76], [0.55, -4.11], [1.35, -1.56], [1.88, -0.9], [4.17, -0.82]], "v": [[-28.67, -77.22], [-41.72, -78.15], [-53.57, -72.22], [-56.98, -66.42], [-58.54, -59.92], [-54.82, -47.49], [-57.31, -59.8], [-52.36, -71.17], [-47.49, -74.96], [-41.52, -76.94]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[3.43, -0.85], [-3.31, -0.89], [-2.39, -2.35], [-0.98, -3.28], [0.05, -3.52], [0.74, 3.53], [2.62, 2.62], [3.55, 0.65]], "o": [[3.51, -0.2], [3.3, 0.9], [2.4, 2.35], [0.95, 3.3], [0.72, -3.47], [-0.74, -3.52], [-2.65, -2.56], [-3.54, -0.68]], "v": [[11.01, -74.08], [21.35, -73.1], [30.13, -68.27], [35.12, -59.56], [36.41, -49.24], [36.31, -59.87], [31.26, -69.41], [21.63, -74.3]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[3.43, -0.85], [-3.31, -0.89], [-2.39, -2.35], [-0.98, -3.28], [0.05, -3.52], [0.74, 3.53], [2.62, 2.62], [3.55, 0.65]], "o": [[3.51, -0.2], [3.3, 0.9], [2.4, 2.35], [0.95, 3.3], [0.72, -3.47], [-0.74, -3.52], [-2.65, -2.56], [-3.54, -0.68]], "v": [[13.04, -74.25], [23.37, -73.28], [32.16, -68.44], [37.14, -59.74], [38.43, -49.42], [38.34, -60.04], [33.28, -69.59], [23.65, -74.48]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[3.43, -0.85], [-3.31, -0.89], [-2.39, -2.35], [-0.98, -3.28], [0.05, -3.52], [0.74, 3.53], [2.62, 2.62], [3.55, 0.65]], "o": [[3.51, -0.2], [3.3, 0.9], [2.4, 2.35], [0.95, 3.3], [0.72, -3.47], [-0.74, -3.52], [-2.65, -2.56], [-3.54, -0.68]], "v": [[13.04, -74.25], [23.37, -73.28], [32.16, -68.44], [37.14, -59.74], [38.43, -49.42], [38.34, -60.04], [33.28, -69.59], [23.65, -74.48]]}], "t": 84}, {"s": [{"c": true, "i": [[3.43, -0.85], [-3.31, -0.89], [-2.39, -2.35], [-0.98, -3.28], [0.05, -3.52], [0.74, 3.53], [2.62, 2.62], [3.55, 0.65]], "o": [[3.51, -0.2], [3.3, 0.9], [2.4, 2.35], [0.95, 3.3], [0.72, -3.47], [-0.74, -3.52], [-2.65, -2.56], [-3.54, -0.68]], "v": [[11.01, -74.08], [21.35, -73.1], [30.13, -68.27], [35.12, -59.56], [36.41, -49.24], [36.31, -59.87], [31.26, -69.41], [21.63, -74.3]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [{"c": true, "i": [[-1.98, -0.63], [2.24, 3.24], [0.7, 1.84], [0.33, 2.05], [-0.55, -2.05], [-1.22, -1.73], [-1.68, -1.25]], "o": [[-3.31, -2.44], [-1.17, -1.59], [-0.75, -1.84], [-0.33, 2.02], [0.46, 2.06], [1.23, 1.73], [1.67, 1.25]], "v": [[23.51, -33.94], [15.01, -42.33], [12.3, -47.54], [10.98, -53.42], [11.11, -47.2], [13.69, -41.42], [18.05, -36.89]]}], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [{"c": true, "i": [[-1.98, -0.63], [2.24, 3.24], [0.7, 1.84], [0.33, 2.05], [-0.55, -2.05], [-1.22, -1.73], [-1.68, -1.25]], "o": [[-3.31, -2.44], [-1.17, -1.59], [-0.75, -1.84], [-0.33, 2.02], [0.46, 2.06], [1.23, 1.73], [1.67, 1.25]], "v": [[21.41, -29.44], [12.9, -37.84], [10.2, -43.04], [8.88, -48.92], [9.01, -42.7], [11.58, -36.92], [15.95, -32.4]]}], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [{"c": true, "i": [[-1.98, -0.63], [2.24, 3.24], [0.7, 1.84], [0.33, 2.05], [-0.55, -2.05], [-1.22, -1.73], [-1.68, -1.25]], "o": [[-3.31, -2.44], [-1.17, -1.59], [-0.75, -1.84], [-0.33, 2.02], [0.46, 2.06], [1.23, 1.73], [1.67, 1.25]], "v": [[21.41, -29.44], [12.9, -37.84], [10.2, -43.04], [8.88, -48.92], [9.01, -42.7], [11.58, -36.92], [15.95, -32.4]]}], "t": 84}, {"s": [{"c": true, "i": [[-1.98, -0.63], [2.24, 3.24], [0.7, 1.84], [0.33, 2.05], [-0.55, -2.05], [-1.22, -1.73], [-1.68, -1.25]], "o": [[-3.31, -2.44], [-1.17, -1.59], [-0.75, -1.84], [-0.33, 2.02], [0.46, 2.06], [1.23, 1.73], [1.67, 1.25]], "v": [[23.51, -33.94], [15.01, -42.33], [12.3, -47.54], [10.98, -53.42], [11.11, -47.2], [13.69, -41.42], [18.05, -36.89]]}], "t": 103}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.63, -43.75], [9.43, 1.01], [-0.09, 44], [-19.7, -1.06]], "o": [[-4.63, 43.75], [-8.56, -0.92], [0.09, -44], [19.7, 1.06]], "v": [[29.32, -16.38], [-11.48, 28.53], [-49, -20.59], [-7.41, -75.2]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-28.5, 10]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-28.5, 10]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [3.61], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [6.44], "t": 28}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.19, "y": 1}, "s": [3.61], "t": 41}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [8.95], "t": 67}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [16.48], "t": 84}, {"s": [3.61], "t": 103}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [1.14, -3.88], [1.19, 8.15], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-13.97, -5.41], [3.68, -10.95], [0, 0]], "v": [[-6.5, 12.92], [-15.28, 31.47], [-15.91, 32.81], [-17.81, 40.12], [-37.15, 14.21], [-29.01, -9.69]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, -1.03], [-10.72, -1.9], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.02, 2.44], [3.49, 0.61], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-42, 29.2], [-23.59, 51.11], [-15.91, 32.81], [-15.28, 31.48], [-6.49, 12.92], [-29.01, -9.7]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-34.5, 33.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-34.5, 33.5]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.27, "y": 1}, "s": [0], "t": 8}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [6], "t": 28}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [6], "t": 73}, {"s": [0], "t": 103}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.05, 0.06], [-13.3, 2.65], [0.26, -0.11], [4.82, -3.82]], "o": [[4.28, -5.54], [0.3, -0.06], [-14.02, 6.03], [-0.06, 0.05]], "v": [[-100.34, 134.72], [-69.81, 116.44], [-69.56, 116.69], [-100.25, 134.83]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.08, 0], [-10.76, -8.56], [0.26, 0.13], [5.99, 1.36]], "o": [[6.07, 0.14], [0.24, 0.19], [-14.24, -7.22], [-0.08, -0.02]], "v": [[-35.85, 140.19], [-2.47, 152.54], [-2.51, 152.88], [-35.87, 140.33]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.08, 0.01], [-10.9, -8.07], [0.26, 0.12], [6.07, 1]], "o": [[6.98, -0.55], [0.25, 0.18], [-13.89, -6.34], [-0.08, -0.01]], "v": [[-34.62, 125.11], [-0.57, 135.44], [-0.59, 135.79], [-34.64, 125.25]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-13.41, -1.48], [1.33, -10.98], [0, 0], [2.47, -7.08], [0, 0], [3.37, -5.8], [10.2, -3.52]], "o": [[7.13, -1.76], [14.89, 1.65], [-0.97, 8.05], [0, 0], [-2.25, 6.45], [0, 0], [-4.05, 6.97], [-2.29, 0.79]], "v": [[-65.99, 113.41], [-30.26, 109.66], [11.55, 129.47], [-0.45, 135.73], [8.13, 148.24], [-2.57, 152.79], [0.2, 163.04], [-26.24, 163.07]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-11.64, 16.6], [-38.28, -9.31], [15.9, 1], [3.72, -0.79], [1.17, -9.88], [2.66, -9.17], [2.72, 0.44], [13.61, -3.64], [11.66, 6.77]], "o": [[8.05, -11.48], [14.67, 3.57], [-11.2, -0.7], [0, 0], [-0.52, 4.42], [0, 0], [0, 0], [0, 0], [-7.31, -4.24]], "v": [[-75.42, 123.14], [-2.14, 98.16], [0.53, 112.58], [-22.08, 111.22], [-10.51, 132.61], [-15.4, 158.22], [-20.27, 157.73], [-35.76, 173.77], [-74.73, 162.9]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[29.14, 174.24], [56.72, 96.16], [100.12, 81.26], [68.49, 163.76]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[19.05, 173.68], [29.14, 174.24], [56.72, 96.16], [46.53, 95.61]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2157, 0.2784, 0.3098]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[19.05, 173.68], [46.53, 95.61], [-22.74, 77.04], [-48.76, 156.74]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-18.16, -10.33], [-7.15, 1.84], [-2.81, 1.16], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.35, -1.37]], "o": [[0, 0], [7.15, -1.84], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.35, 1.37]], "v": [[52.23, 94.23], [67.68, 87.99], [93.33, 82.13], [98.08, 81.96], [67.37, 162.99], [25.29, 171.63], [-44.7, 154.93], [-19.73, 77.85], [-14.91, 77.61], [1.33, 81.4]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7961, 0.7961, 0.7961]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[-18.16, -10.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.35, -1.37]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.35, 1.37]], "v": [[52.23, 94.23], [52.49, 95.51], [53.01, 98.04], [53.24, 99.17], [55.83, 111.96], [67.37, 162.99], [25.29, 171.63], [-44.7, 154.93], [-14.91, 77.61], [1.33, 81.4]]}], "t": 0}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[-18.16, -10.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.35, -1.37]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.35, 1.37]], "v": [[52.23, 94.23], [52.49, 95.51], [53.01, 98.04], [53.24, 99.17], [55.83, 111.96], [67.37, 162.99], [25.29, 171.63], [-44.7, 154.93], [-14.91, 77.61], [1.33, 81.4]]}], "t": 5}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[3.52, -13.98], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-12.33, 1.6]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [9.48, -1.23]], "v": [[52.23, 94.23], [52.25, 94.16], [52.28, 94.01], [52.3, 93.95], [52.45, 93.21], [30.62, 167.99], [25.29, 171.63], [-27.2, 154.43], [-6.9, 77.8], [24.83, 81.9]]}], "t": 9}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[11.32, -11.78], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.23, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [9.47, -0.12]], "v": [[52.23, 94.23], [52.61, 93.92], [53.36, 93.29], [53.69, 93.01], [55.92, 88.34], [36.52, 166.09], [25.29, 171.63], [-22.4, 154.63], [3.97, 78.48], [36.17, 78.75]]}], "t": 11}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[23.02, -8.48], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.58, -2.4]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [9.44, 1.56]], "v": [[52.23, 94.23], [53.15, 93.56], [54.97, 92.21], [55.78, 91.61], [64.95, 84.84], [45.37, 163.24], [25.29, 171.63], [-15.2, 154.93], [20.57, 72.14], [53.19, 74.02]]}], "t": 14}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[17.82, -7.48], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-9.15, -3.63]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [5.93, 2.37]], "v": [[52.23, 94.23], [53.23, 93.47], [55.22, 91.97], [56.1, 91.3], [66.1, 83.71], [49.17, 162.34], [25.29, 171.63], [-2.7, 157.03], [32.54, 72.51], [61.98, 78.3]]}], "t": 16}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[15.22, -6.98], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.44, -4.24]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [4.18, 2.78]], "v": [[52.23, 94.23], [53.38, 93.41], [55.65, 91.78], [56.66, 91.06], [68.11, 82.84], [51.07, 161.89], [25.29, 171.63], [3.55, 158.08], [38.64, 73.71], [66.43, 79.88]]}], "t": 17}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[12.62, -6.48], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.73, -4.86]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.42, 3.19]], "v": [[52.23, 94.23], [53.61, 93.4], [56.35, 91.75], [57.57, 91.02], [71.38, 82.71], [52.97, 161.44], [25.29, 171.63], [9.8, 159.13], [44.74, 74.91], [70.88, 81.45]]}], "t": 18}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[10.02, -5.98], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.02, -5.47]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.67, 3.6]], "v": [[52.23, 94.23], [53.9, 93.42], [57.2, 91.81], [58.67, 91.09], [75.33, 82.96], [54.87, 160.99], [25.29, 171.63], [16.05, 160.18], [50.84, 76.11], [75.33, 83.03]]}], "t": 19}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[10.02, -5.98], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.29, -4.25]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.54, 2.74]], "v": [[52.23, 94.23], [50.13, 101.13], [56.53, 96.8], [57.8, 91.51], [76.29, 85.96], [58.44, 161.56], [25.29, 171.63], [30.63, 159.68], [69.59, 78.4], [76.1, 86.11]]}], "t": 21}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[10.02, -5.98], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.08, -1.19]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.08, 0.6]], "v": [[52.23, 94.23], [53.77, 95.27], [56.82, 97.34], [58.18, 98.26], [73.58, 108.71], [67.37, 162.99], [25.29, 171.63], [67.05, 158.43], [95.09, 78.61], [76.83, 85.9]]}], "t": 24}, {"s": [{"c": true, "i": [[10.17, -4.61], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.08, -1.19]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.08, 0.6]], "v": [[52.23, 94.23], [53.77, 95.27], [56.82, 97.34], [58.18, 98.26], [73.58, 108.71], [67.37, 162.99], [25.29, 171.63], [67.05, 158.43], [97.01, 83.02], [77.24, 86.54]]}], "t": 26}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8824, 0.8824, 0.8824]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [24.5, 170.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [24.5, 170.5]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-65.5, 145]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-65.5, 145]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [10], "t": 10}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 25}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.34, "y": 1}, "s": [0], "t": 43}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [16], "t": 56}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [16], "t": 80}, {"s": [0], "t": 102}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-7.79, -14.86], [-15.93, 3.71], [13.38, 26.32], [3.08, -0.65], [0.22, 0.88]], "o": [[9.87, 18.83], [3.92, -0.91], [-8.44, -16.6], [-6.35, 1.34], [-0.23, -0.91]], "v": [[-127.78, 161.68], [-33.75, 173.32], [-25.68, 132.72], [-57.73, 114.42], [-69.8, 116.22]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-121.5, 156]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-121.5, 156]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-3], "t": 12}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 30}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.34, "y": 1}, "s": [0], "t": 44}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-11], "t": 60}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-11], "t": 78}, {"s": [0], "t": 101}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.19, 0.08], [12.05, 3.77], [-0.17, -0.08], [-11.97, -3.96]], "o": [[-11.58, -4.99], [-0.18, -0.06], [11.51, 5.19], [0.2, 0.07]], "v": [[-54.23, 89.13], [-89.66, 75.68], [-89.77, 75.99], [-54.34, 89.47]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.8, 1.91], [0.06, -0.1], [0.73, -1.38], [-0.64, 2.84], [0.03, -0.09], [2.05, -6.15], [1.98, -6.17], [0.91, 0.16], [-0.19, -0.1], [-0.07, 0.15], [-2.09, 6.13], [-0.86, 3.06]], "o": [[0.05, -0.11], [-0.74, 1.38], [0.76, -2.8], [0.02, -0.09], [-2.15, 6.11], [-2.05, 6.15], [-1.22, -0.25], [-0.21, -0.04], [1.15, 0.58], [2.68, -5.9], [1.03, -3.02], [1.01, -1.81]], "v": [[-41.5, 64.53], [-41.67, 64.45], [-43.86, 68.58], [-41.74, 60.12], [-41.9, 60.06], [-48.21, 78.44], [-54.57, 96.82], [-57.36, 96.23], [-57.51, 96.53], [-54.03, 97.84], [-47.09, 79.19], [-44.27, 70.08]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-5.68, 25.31], [15.68, -16.07]], "o": [[0, 0], [0, 0], [5.68, -25.31], [-15.68, 16.07]], "v": [[-96.93, 81.19], [-54.61, 97.3], [-37.83, 42.65], [-60.97, 36.17]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6863, 0.7882, 0.2078]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[7.53, -8.54], [-7.79, -14.86], [-9.69, 3.48], [0.22, 0.88], [-11.56, 40.4]], "o": [[-35.52, 40.29], [2.99, 5.71], [23.51, -8.44], [-0.23, -0.91], [7.88, -27.54]], "v": [[-54.99, 31.5], [-127.78, 161.68], [-101.06, 166.27], [-69.8, 116.22], [-39.48, 46.89]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-46.5, 33]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-46.5, 33]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.25, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0, "y": 1}, "s": [-5], "t": 15}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 34}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.34, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-7], "t": 65}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-7], "t": 76}, {"s": [0], "t": 100}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-1.75, -2.24], [0, 0], [22.01, 34.98], [15.45, -11.84]], "o": [[0, 0], [1.88, -1.4], [-4.12, -6.55], [-46.63, 35.76]], "v": [[-123.5, 169.7], [26.03, 169.7], [-11.42, 32.01], [-50.73, 27.32]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6863, 0.7882, 0.2078]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [2.26, 22.34], [-5.66, 0.17]], "o": [[0, 0], [0, 0], [-1.44, -14.13], [0, 0]], "v": [[20.18, 89.58], [-12.35, 107.8], [-23.8, 51.33], [-17.58, 27.1]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0, 0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 10}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.18, -0.09], [10.62, -6.82], [-0.17, 0.08], [-10.72, 6.64]], "o": [[-11.26, 5.68], [-0.16, 0.1], [11.36, -5.5], [0.18, -0.11]], "v": [[21.14, 80.14], [-11.91, 98.69], [-11.74, 98.97], [21.33, 80.45]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [15.77, 20.59], [-2.26, -22.34]], "o": [[0, 0], [0, 0], [-15.77, -20.59], [2.26, 22.34]], "v": [[-12.35, 107.8], [27.16, 85.67], [-3.99, 37.74], [-23.8, 51.33]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6863, 0.7882, 0.2078]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.03, -0.01], [-5.48, -3.98], [-2.36, -2.98], [-2.44, -3.42], [0.1, 0.14], [6.53, 6.71], [3.96, 2.11], [4.2, 1.39]], "o": [[6.41, 1.89], [7.26, 5.27], [2.61, 3.3], [0.1, 0.14], [-5.6, -7.49], [-3.12, -3.2], [-3.91, -2.09], [-0.03, -0.01]], "v": [[118.93, 121.48], [136.42, 128.97], [152.77, 145.65], [160.3, 155.73], [160.06, 155.91], [141.82, 134.53], [131.3, 126.36], [118.92, 121.52]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.07, -0.03], [-5.96, -3.7], [-4.79, -5.89], [-2.71, -3.67], [0.12, 0.16], [5.21, 5.77], [6.6, 3.17], [3.84, 1.5]], "o": [[6.56, 2.4], [6.53, 4.05], [2.88, 3.54], [0.11, 0.15], [-4.85, -6.08], [-4.9, -5.43], [-3.72, -1.79], [-0.07, -0.03]], "v": [[110.96, 130.35], [130.21, 138.89], [146.39, 154.95], [154.78, 165.76], [154.56, 165.97], [139.46, 148.17], [122.34, 135.25], [110.93, 130.47]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.07, -0.02], [-4.82, -3.8], [-5.01, -5.5], [0.13, 0.14], [2.6, 2.28], [6.13, 1.69]], "o": [[5.94, 1.45], [6.12, 4.82], [0.13, 0.14], [-2.39, -2.61], [-4.76, -4.18], [-0.07, -0.02]], "v": [[105.29, 140.79], [121.78, 148.79], [136.78, 164.47], [136.56, 164.68], [121.6, 149.88], [105.26, 140.9]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.01, -0.01], [-7.49, -2.87], [-2.97, 5.92], [6.41, 3.33], [-1.22, 2.16], [-5.63, -3.94], [-7.48, 5.22], [4.16, 4.3], [-3.23, 2.07], [0.79, 2.16], [-1.51, 1.35], [2.28, 3.97], [-0.65, 0.86], [8.51, 2.79], [0, 0], [8.2, -15.76]], "o": [[0.84, 0.52], [5.59, 2.14], [2.03, -4.03], [-3.37, -1.75], [2.13, -3.78], [10.89, 7.62], [2.83, -1.97], [6.9, 5.18], [1.85, -1.18], [2.19, 0.92], [2.58, -2.28], [0.84, -0.02], [5.66, -7.23], [-25.04, -8.22], [-4.07, 2.73], [-10.12, 19.45]], "v": [[65.74, 161.16], [85.96, 171.83], [110.78, 173.79], [97.24, 161.93], [91.08, 154], [107.52, 158.86], [140.14, 174.52], [136.07, 163.99], [153.66, 171.5], [154.95, 166.36], [160.65, 165.96], [160.1, 155.95], [162.38, 154.72], [136.26, 120.63], [83.86, 112.34], [64.05, 129.76]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [77, 135]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [77, 135]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.25, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-10], "t": 12}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [-20], "t": 27}, {"o": {"x": 0.23, "y": 0}, "i": {"x": 0.58, "y": 1.49}, "s": [-20], "t": 79}, {"o": {"x": 0.32, "y": 0.05}, "i": {"x": 0.66, "y": 0.93}, "s": [-24.36], "t": 88}, {"o": {"x": 0.32, "y": -0.13}, "i": {"x": 0.65, "y": 1.87}, "s": [2.71], "t": 95}, {"o": {"x": 0.32, "y": 3.36}, "i": {"x": 0.66, "y": 0.42}, "s": [-7.78], "t": 100}, {"o": {"x": 0.33, "y": 0.23}, "i": {"x": 0.67, "y": 0.95}, "s": [-4.27], "t": 107}, {"o": {"x": 0.35, "y": -0.11}, "i": {"x": 0.69, "y": 1}, "s": [5.62], "t": 115}, {"s": [0], "t": 124}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-6.87, -5.81], [-2.99, -0.18], [-8.46, 24.88], [3.25, 1.2], [2.37, 2.88], [15.12, -16.36]], "o": [[9.53, 8.06], [18.08, 1.11], [3.78, -11.1], [-9.37, -3.46], [-1.03, -1.26], [-8.86, 9.59]], "v": [[6.82, 138.69], [71.4, 164.5], [99.31, 138.97], [90.1, 112.47], [32.91, 100.87], [1.38, 105.37]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [18, 116.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [18, 116.5]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [3], "t": 27}, {"o": {"x": 0.23, "y": 0}, "i": {"x": 0.58, "y": 0.97}, "s": [3], "t": 79}, {"o": {"x": 0.32, "y": -0.07}, "i": {"x": 0.66, "y": 0.8}, "s": [-8.54], "t": 88}, {"o": {"x": 0.32, "y": -0.28}, "i": {"x": 0.65, "y": 1.74}, "s": [-5.9], "t": 95}, {"o": {"x": 0.32, "y": 0.37}, "i": {"x": 0.66, "y": 0.71}, "s": [-7.17], "t": 100}, {"o": {"x": 0.33, "y": 0.16}, "i": {"x": 0.67, "y": 0.9}, "s": [-3.9], "t": 107}, {"o": {"x": 0.35, "y": -0.29}, "i": {"x": 0.69, "y": 1}, "s": [2.74], "t": 115}, {"s": [0], "t": 124}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-3.16, -22.13], [-11.82, -9.99], [-6.42, 10.44], [2.37, 2.88], [16.59, 27.6]], "o": [[5.09, 35.67], [9.53, 8.06], [8.6, -14], [-2.46, -3.01], [-11.45, -19.07]], "v": [[-22.63, 47.35], [6.82, 138.69], [36.9, 130.5], [32.91, 100.87], [-0.83, 46.41]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.6784, 0.3882, 0.349]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [-17.5, 41]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-17.5, 41]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 1}, "s": [4], "t": 27}, {"o": {"x": 0.23, "y": 0}, "i": {"x": 0.58, "y": 0.75}, "s": [4], "t": 79}, {"o": {"x": 0.32, "y": -0.19}, "i": {"x": 0.66, "y": 1.17}, "s": [2.27], "t": 88}, {"o": {"x": 0.32, "y": 0.33}, "i": {"x": 0.65, "y": 2.2}, "s": [3.56], "t": 95}, {"o": {"x": 0.32, "y": 1.64}, "i": {"x": 0.66, "y": 1.72}, "s": [3.11], "t": 100}, {"o": {"x": 0.33, "y": 0.04}, "i": {"x": 0.67, "y": 0.91}, "s": [3.53], "t": 107}, {"o": {"x": 0.35, "y": -0.2}, "i": {"x": 0.69, "y": 1}, "s": [-4.29], "t": 115}, {"s": [0], "t": 124}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 4, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[208.68, 170.17], [-22.07, 170.17], [-92.36, 170.17], [-92.7, 170.17], [-93.29, 170.17], [-109.88, 170.17], [-180.58, 170.17], [-208.68, 170.17]]}}}, {"ty": "st", "bm": 0, "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 5, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [111.66, -56.62, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [111.66, -56.62, 0], "t": 0, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [111.66, -61.14, 0], "t": 37, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [107.13, -59.28, 0], "t": 81, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"s": [111.66, -56.62, 0], "t": 125}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [26], "t": 42}, {"s": [0], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[110.45, -129.8], [112.31, -136.96]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[125.64, -121.02], [130.83, -126.29]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[134.39, -105.82], [141.53, -107.79]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[134.38, -88.28], [141.54, -86.42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[77.68, -73.13], [72.49, -67.86]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.92, -88.33], [61.79, -86.36]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.94, -105.87], [61.77, -107.73]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[77.72, -121.05], [72.44, -126.24]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[92.91, -129.81], [90.94, -136.94]]}}}, {"ty": "tm", "bm": 0, "e": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 0}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 9}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 17}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 33}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 42}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 50}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 65}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 74}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 82}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 98}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 107}, {"s": [100], "t": 115}]}, "o": {"a": 0, "k": 0}, "s": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 0}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 9}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 17}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 33}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 42}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 50}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 65}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 74}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 82}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 98}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 107}, {"s": [100], "t": 115}]}, "m": 1}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 0}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 1}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 16}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 17}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 33}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 34}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 49}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 50}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 65}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 66}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 81}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 82}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 98}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 99}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [100], "t": 114}, {"s": [0], "t": 115}]}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[127.86, -45.53], [103.49, -39.28], [102.27, -44.04], [126.64, -50.29]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[126.44, -51.06], [102.07, -44.81], [100.85, -49.57], [125.22, -55.82]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[125.02, -56.59], [100.65, -50.34], [99.43, -55.11], [123.8, -61.35]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [-2.79, 0.71], [3.66, -0.94], [-4.64, 1.19], [3.25, -0.83], [-4.47, 1.15], [0, 0]], "o": [[0, 0], [2.79, -0.71], [-3.66, 0.94], [4.64, -1.19], [-3.25, 0.83], [2.4, -0.61], [0, 0]], "v": [[94.2, -83.69], [99.73, -78.55], [99.12, -88.17], [106.72, -77.32], [107.71, -90.37], [112.41, -81.37], [114.94, -88.59]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.5}, "c": {"a": 0, "k": [1, 1, 1]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[117.02, -57.34], [114.83, -90.26]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [1, 1, 1]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[107.31, -54.85], [93.39, -84.77]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [1, 1, 1]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-1.74, -12.79], [0.74, -5.56], [0, 0], [0, 0], [1.8, 2.45], [7.05, 18.34], [-20.9, 5.36]], "o": [[2.64, 19.47], [-0.39, 3.01], [0, 0], [0, 0], [-3.31, -4.52], [-4.64, -12.05], [20.9, -5.36]], "v": [[131.11, -101.85], [121.75, -70.23], [121.7, -59.91], [101.95, -54.85], [96.95, -63.87], [73.54, -87.09], [94.12, -126.5]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 0.7804, 0.1529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 6, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [174.32, -74.4, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [174.32, -74.4, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-13.57], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [33.43], "t": 62}, {"s": [-13.57], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-0.21, -4.2], [2.44, -3.89], [7.9, -0.39], [4.23, 3.27], [0, 0], [0, 0], [-4.37, -6.79]], "o": [[0.25, 4.94], [-3.89, 6.23], [-5.75, 0.29], [0, 0], [0, 0], [8.63, -0.43], [2.12, 3.31]], "v": [[197.74, -75.45], [194.23, -61.96], [175.66, -51.05], [160.29, -55.85], [174.49, -74.29], [173.33, -97.53], [194.09, -86.85]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-0.21, -4.2], [2.44, -3.89], [7.9, -0.39], [3.28, 7.55], [0, 0], [0, 0], [-4.37, -6.79]], "o": [[0.25, 4.94], [-3.89, 6.23], [-5.75, 0.29], [0, 0], [0, 0], [8.63, -0.43], [2.12, 3.31]], "v": [[197.74, -75.45], [194.23, -61.96], [175.66, -51.05], [154.87, -62.75], [174.49, -74.29], [173.33, -97.53], [194.09, -86.85]]}], "t": 32}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-0.21, -4.2], [2.44, -3.89], [7.9, -0.39], [4.23, 3.27], [0, 0], [0, 0], [-4.37, -6.79]], "o": [[0.25, 4.94], [-3.89, 6.23], [-5.75, 0.29], [0, 0], [0, 0], [8.63, -0.43], [2.12, 3.31]], "v": [[197.74, -75.45], [194.23, -61.96], [175.66, -51.05], [160.29, -55.85], [174.49, -74.29], [173.33, -97.53], [194.09, -86.85]]}], "t": 62}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-0.21, -4.2], [2.44, -3.89], [7.9, -0.39], [3.28, 7.55], [0, 0], [0, 0], [-4.37, -6.79]], "o": [[0.25, 4.94], [-3.89, 6.23], [-5.75, 0.29], [0, 0], [0, 0], [8.63, -0.43], [2.12, 3.31]], "v": [[197.74, -75.45], [194.23, -61.96], [175.66, -51.05], [154.87, -62.75], [174.49, -74.29], [173.33, -97.53], [194.09, -86.85]]}], "t": 95}, {"s": [{"c": true, "i": [[-0.21, -4.2], [2.44, -3.89], [7.9, -0.39], [4.23, 3.27], [0, 0], [0, 0], [-4.37, -6.79]], "o": [[0.25, 4.94], [-3.89, 6.23], [-5.75, 0.29], [0, 0], [0, 0], [8.63, -0.43], [2.12, 3.31]], "v": [[197.74, -75.45], [194.23, -61.96], [175.66, -51.05], [160.29, -55.85], [174.49, -74.29], [173.33, -97.53], [194.09, -86.85]]}], "t": 125}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-12.83, 0.64], [0, 0], [0, 0], [1.57, 3.01], [0.17, 3.47]], "o": [[0, 0], [0, 0], [-2.66, -2.05], [-1.51, -2.88], [-0.64, -12.83]], "v": [[168.48, -99.66], [169.64, -76.42], [155.44, -57.98], [149.01, -65.65], [146.4, -75.25]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-12.83, 0.64], [0, 0], [0, 0], [0.14, 0.09], [0.17, 3.4]], "o": [[0, 0], [0, 0], [0.01, -0.05], [-1.15, -2.98], [-0.64, -12.83]], "v": [[168.48, -99.66], [169.64, -76.42], [149.14, -65.48], [148.92, -65.81], [146.4, -75.25]]}], "t": 32}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-12.83, 0.64], [0, 0], [0, 0], [1.57, 3.01], [0.17, 3.47]], "o": [[0, 0], [0, 0], [-2.66, -2.05], [-1.51, -2.88], [-0.64, -12.83]], "v": [[168.48, -99.66], [169.64, -76.42], [155.44, -57.98], [149.01, -65.65], [146.4, -75.25]]}], "t": 62}, {"o": {"x": 0.17, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-12.83, 0.64], [0, 0], [0, 0], [0.14, 0.09], [0.17, 3.4]], "o": [[0, 0], [0, 0], [0.01, -0.05], [-1.15, -2.98], [-0.64, -12.83]], "v": [[168.48, -99.66], [169.64, -76.42], [149.14, -65.48], [148.92, -65.81], [146.4, -75.25]]}], "t": 95}, {"s": [{"c": true, "i": [[-12.83, 0.64], [0, 0], [0, 0], [1.57, 3.01], [0.17, 3.47]], "o": [[0, 0], [0, 0], [-2.66, -2.05], [-1.51, -2.88], [-0.64, -12.83]], "v": [[168.48, -99.66], [169.64, -76.42], [155.44, -57.98], [149.01, -65.65], [146.4, -75.25]]}], "t": 125}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 7, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [49.62, -149.69, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [49.62, -149.69, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.48, "y": 0}, "i": {"x": 0.47, "y": 1}, "s": [9.39], "t": 0}, {"o": {"x": 0.48, "y": 0}, "i": {"x": 0.47, "y": 1}, "s": [-14], "t": 58}, {"s": [9.39], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [-13.37, 3.46]], "o": [[0, 0], [0, 0]], "v": [[30.55, -151.81], [44.24, -168.14]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[0.42, -3.85], [10.19, 1.12], [-1.11, 10.19], [-1.26, 2.17], [-4.79, -0.36], [-7.21, 0.04]], "o": [[-1.11, 10.19], [-10.19, -1.11], [0.29, -2.66], [2.07, 1.8], [9.39, 0.7], [1.68, 3.18]], "v": [[67.55, -148.54], [47.08, -132.1], [30.64, -152.58], [33.04, -159.87], [44.1, -156.3], [65.51, -159.26]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[0.8, -3.79], [10.06, 2.12], [-2.12, 10.06], [-1.47, 2.04], [-4.57, -1.75], [-4.72, 3.64]], "o": [[-2.12, 10.06], [-10.05, -2.12], [0.55, -2.62], [4.01, -0.39], [8.99, 3.44], [1.36, 3.34]], "v": [[67.26, -146.67], [45.23, -132.29], [30.85, -154.34], [33.96, -161.37], [45.43, -158.2], [66.28, -157.56]]}], "t": 43}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[0.64, -3.82], [10.11, 1.71], [-1.7, 10.11], [-1.39, 2.09], [-4.66, -1.18], [-5.75, 2.15]], "o": [[-1.7, 10.11], [-10.11, -1.7], [0.45, -2.64], [3.21, 0.52], [9.15, 2.31], [1.49, 3.27]], "v": [[67.38, -147.44], [45.99, -132.21], [30.77, -153.61], [33.58, -160.75], [44.89, -157.42], [65.96, -158.26]]}], "t": 64}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.83, "y": 0.83}, "s": [{"c": true, "i": [[0.26, -3.87], [10.25, 0.68], [-0.68, 10.25], [-1.17, 2.22], [-4.89, 0.25], [-8.29, -1.52]], "o": [[-0.68, 10.25], [-10.25, -0.68], [0.18, -2.68], [1.23, 2.75], [9.57, -0.49], [1.82, 3.11]], "v": [[67.68, -149.35], [47.89, -132.01], [30.55, -151.81], [32.64, -159.21], [43.52, -155.47], [65.17, -160]]}], "t": 108}, {"s": [{"c": true, "i": [[0.42, -3.85], [10.19, 1.12], [-1.11, 10.19], [-1.26, 2.17], [-4.79, -0.36], [-7.21, 0.04]], "o": [[-1.11, 10.19], [-10.19, -1.11], [0.29, -2.66], [2.07, 1.8], [9.39, 0.7], [1.68, 3.18]], "v": [[67.55, -148.54], [47.08, -132.1], [30.64, -152.58], [33.04, -159.87], [44.1, -156.3], [65.51, -159.26]]}], "t": 125}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.69, -10.45], [12.49, 0.82], [-0.82, 12.49], [-9.79, 1.96], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-0.82, 12.49], [-12.5, -0.82], [0.69, -10.45], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [9.46, 3.23]], "v": [[71.74, -149.09], [47.63, -127.96], [26.49, -152.08], [44.64, -172.82], [45.93, -190.75], [44.1, -190.86], [44.34, -194.46], [59.84, -193.43], [59.61, -189.84], [57.77, -189.96], [56.47, -172.03]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 70}}]}], "ind": 8, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [2.99, -147.6, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [2.99, -147.6, 0], "t": 0, "ti": [0, 0, 0], "to": [0, 1.51, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [2.99, -138.55, 0], "t": 40, "ti": [0, 2.15, 0], "to": [0, 0, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-1.54, -142.71, 0], "t": 79, "ti": [0, 0.73, 0], "to": [0, -2.02, 0]}, {"s": [2.99, -147.6, 0], "t": 125}]}, "r": {"a": 1, "k": [{"o": {"x": 0.25, "y": 0.35}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.68, "y": 0.82}, "s": [-8.19], "t": 32}, {"o": {"x": 0.24, "y": -0.36}, "i": {"x": 0.65, "y": 0.46}, "s": [5.74], "t": 77}, {"s": [0], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.04, -0.01], [0.04, 0.01]], "o": [[-0.04, -0.01], [0.04, 0]], "v": [[21.4, -113.88], [21.27, -113.9]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.19, 0.61], [0.12, 0.53], [0, 0], [2.45, -1.39]], "o": [[-0.17, -0.53], [0, 0], [-2.05, 2.87], [-0.21, -0.51]], "v": [[-25.58, -105.51], [-26.01, -107.11], [-17.34, -110.24], [-24.96, -103.82]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.14, 1.4], [-0.09, 0.02], [0, 0], [0.42, -0.82], [0.05, -0.02], [0, 0]], "o": [[0.08, 0.02], [0, 0], [-0.19, 0.92], [-0.05, 0], [0, 0], [-0.21, -1.42]], "v": [[-26.34, -112.6], [-26.08, -112.59], [-15.38, -114.55], [-16.3, -111.94], [-16.45, -111.91], [-26.24, -108.37]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.34, 0.94], [-0.08, 0.18], [-0.07, 0.01], [0, 0], [0.03, -0.72], [0, 0]], "o": [[0.07, -0.19], [0.07, 0.02], [0, 0], [0.12, 0.73], [0, 0], [0.17, -0.95]], "v": [[-25.39, -116.7], [-25.17, -117.25], [-24.96, -117.23], [-15.34, -118.04], [-15.21, -115.86], [-26.16, -113.86]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-2.96, 1.21], [-0.36, -1.46], [0, 0]], "o": [[0.61, 1.46], [0, 0], [1.6, -2.38]], "v": [[-17.16, -123.67], [-15.6, -119.28], [-24.45, -118.54]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0.04], [0.43, 1.31], [0, 0], [0.6, -0.2], [1.2, -0.45]], "o": [[-0.59, -1.36], [0, 0], [-0.58, 0.2], [-1.18, 0.4], [-0.02, -0.04]], "v": [[-16.53, -125.43], [-18.13, -129.43], [-11.1, -127.15], [-12.87, -126.55], [-16.47, -125.3]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, 0.01], [0, 0], [-0.43, 1.44], [0, 0], [0.92, -0.36]], "o": [[0, 0], [-0.38, -1.57], [0, 0], [-0.71, 0.42], [-0.03, -0.01]], "v": [[-9.27, -127.88], [-18.55, -130.9], [-18.6, -135.42], [-6.73, -129.01], [-9.19, -127.85]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.24, 0.32], [-0.78, 0.46], [-0.03, -0.02], [0, 0], [0.22, -0.47], [0.38, -0.34], [0, 0]], "o": [[0.62, -0.82], [0.03, 0.03], [0, 0], [-0.09, 0.49], [-0.19, 0.4], [0, 0], [0.18, -0.33]], "v": [[-17.49, -137.58], [-15.38, -139.49], [-15.3, -139.42], [-4.27, -132.38], [-4.73, -130.95], [-5.59, -129.83], [-18.12, -136.6]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3.04, -0.38], [0, -2.07]], "o": [[2.39, -0.85], [0.82, 2.3], [0, 0]], "v": [[-14.04, -140.11], [-5.73, -140.37], [-4.14, -133.8]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.22, 2.57], [0, 0], [2.52, 0.33], [0.28, 0.04]], "o": [[0, 0], [-2.2, 0.18], [-0.27, -0.04], [-0.89, -2.54]], "v": [[-6.3, -149.22], [3.19, -140.93], [-3.98, -141.41], [-4.8, -141.52]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.2, -0.05], [-0.64, 0.99], [0, 0], [1.25, -0.28]], "o": [[-0.16, -0.14], [0.22, -1], [0, 0], [-0.98, 0.86], [0, 0]], "v": [[-5.55, -150.24], [-6.13, -150.37], [-4.88, -153.36], [8.19, -142.83], [4.83, -141.17]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.07, 0.59], [-0.02, -0.02], [0, 0], [-0.02, -0.01], [0.49, -0.82], [0.24, -0.29]], "o": [[0.89, -1.09], [0.02, 0.02], [0, 0], [0.02, 0.02], [-0.2, 0.86], [-0.21, 0.35], [0, 0]], "v": [[-4.13, -154.38], [-1.18, -156.87], [-1.12, -156.81], [10.7, -147.28], [10.76, -147.24], [9.74, -144.72], [9.06, -143.75]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3.02, -0.05], [0.22, -3.06]], "o": [[2.54, -0.99], [1.47, 3.42], [0, 0]], "v": [[0.08, -157.46], [8.54, -158.44], [10.98, -148.68]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.2, 0.45], [0.69, 2.78], [0, 0], [3.21, 0.07], [0.08, 0]], "o": [[-1.21, -2.74], [0, 0], [-2.75, 0.93], [-0.08, 0], [-0.19, -0.44]], "v": [[8.79, -161.02], [5.65, -169.32], [18.68, -160.48], [9.61, -159.68], [9.37, -159.69]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.1, 0.01], [-0.37, 1.71], [0, 0], [1.17, -0.57], [0.11, 0.07], [0, 0]], "o": [[-0.28, -1.73], [0, 0], [-0.98, 1.11], [-0.05, -0.11], [0, 0], [-0.08, -0.06]], "v": [[5.3, -171.01], [5.37, -176.16], [23.22, -163.48], [19.97, -161], [19.74, -161.28], [5.58, -170.9]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.16, 0.95], [0, 0], [0.87, -1.39], [0.16, -0.23]], "o": [[0.52, -1.45], [0, 0], [0.05, 1.8], [-0.15, 0.25], [0, 0]], "v": [[5.73, -177.46], [8.31, -181.02], [25.64, -170.02], [24.48, -165.18], [24, -164.47]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-3.78, 0.68], [-0.62, -4.36], [0, 0]], "o": [[2.61, 4.18], [0, 0], [2.76, -1.74]], "v": [[19.75, -185.1], [25.5, -171.61], [9.42, -181.81]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.33, 0.13], [-1.53, 0.79], [-0.04, -0.05], [-0.28, 0.21], [0.21, 0.28], [0.58, 1.15], [-0.88, 0.6], [-0.75, 3.62], [1.36, 3.22], [-1.15, 0.39], [-1.71, 1.34], [-0.33, 0.71], [-0.1, 0.72], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.04], [0, 0.01], [0.88, 2.5], [-0.06, -0.01], [-3.27, 5.46], [0.67, 3.22], [0.87, 2.05], [-3.89, 6.23], [4.92, 8.55], [0.33, 0.54], [-0.45, 0.07], [-1.25, 3.52], [0.33, 0.12], [0.12, -0.33], [5.43, -0.9], [0.63, -0.11], [0.04, 0.04], [0.27, -0.23], [-0.23, -0.26], [-2.99, -4.59], [1.73, -5.8], [0, -0.01], [0, -0.01], [-2.43, -5.48], [-0.11, -0.26], [3.51, -4.85], [-1.39, -4.04], [2.97, -3.93], [0.23, -1.09], [-1.47, -3.41], [-0.01, -0.03], [1.43, -3.93], [0.17, -0.83], [-0.37, -2.01], [0, -0.01], [-0.01, -0.04], [0, 0], [-0.21, -0.67], [-0.24, -0.56], [0.03, -0.01], [0.04, -0.2], [-0.04, -0.12]], "o": [[0.07, -0.03], [0.62, 1.22], [0.21, 0.28], [0.28, -0.21], [-0.01, -0.01], [0.79, -0.44], [4.32, -2.96], [0.69, -3.3], [1.17, -0.44], [2.97, -0.99], [0.68, -0.53], [0.32, -0.7], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.04], [0, -0.01], [0.3, -2.44], [0.06, 0.01], [5.75, 0.77], [1.43, -2.38], [-0.41, -1.98], [5.79, 0.12], [2.62, -4.18], [-0.32, -0.56], [0.45, -0.08], [6.06, -1.01], [0.12, -0.33], [-0.33, -0.12], [-1, 2.82], [-0.62, 0.1], [-3.15, -4.92], [-0.23, -0.26], [-0.26, 0.23], [0.07, 0.08], [-6.47, 1.19], [0, 0.01], [0, 0.01], [-1.69, 5.71], [0.12, 0.27], [-5.03, -0.06], [-3.16, 4.36], [-4.72, -0.55], [-0.78, 1.04], [-0.69, 3.3], [0.01, 0.03], [-3.92, 1.59], [-0.31, 0.87], [-0.55, 2.66], [0, 0.01], [0.01, 0.04], [0, 0], [0.16, 0.83], [0.22, 0.68], [-1.5, 0.78], [-0.21, 0.08], [-0.02, 0.12], [0.12, 0.33]], "v": [[-28.21, -100.83], [-25.54, -102.06], [-24.37, -100.09], [-23.48, -99.96], [-23.35, -100.85], [-24.42, -102.67], [-21.9, -104.23], [-14.14, -114.3], [-15.98, -124.13], [-12.47, -125.35], [-5.12, -128.58], [-3.58, -130.42], [-2.96, -132.54], [-2.96, -132.54], [-2.96, -132.54], [-2.96, -132.55], [-2.96, -132.55], [-2.94, -132.68], [-2.94, -132.7], [-4.33, -140.19], [-4.14, -140.16], [10.82, -144.07], [11.95, -152.39], [9.92, -158.42], [25.55, -164.51], [22.08, -183.69], [21.1, -185.34], [22.45, -185.57], [35, -190.97], [34.62, -191.77], [33.81, -191.39], [22.25, -186.81], [20.37, -186.5], [14.03, -195.14], [13.14, -195.21], [13.08, -194.32], [19.02, -186.26], [4.39, -177.41], [4.38, -177.39], [4.38, -177.37], [7.63, -160.51], [7.98, -159.71], [-5.58, -154.56], [-6.2, -141.7], [-18.5, -138.34], [-19.97, -135.15], [-17.69, -124.93], [-17.65, -124.84], [-26.58, -117.13], [-27.3, -114.59], [-27.37, -107.48], [-27.36, -107.46], [-27.34, -107.34], [-27.34, -107.34], [-26.77, -105.08], [-26.07, -103.22], [-28.66, -102.01], [-29.05, -101.55], [-29.02, -101.2]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 9, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [51.27, -44.82, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [30.27, -87.32, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-51], "t": 60}, {"s": [0], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.79, -2.12], [2.12, -0.79], [0.79, 2.12], [-2.12, 0.79]], "o": [[0.79, 2.12], [-2.12, 0.79], [-0.79, -2.12], [2.12, -0.79]], "v": [[55.11, -46.25], [52.71, -40.98], [47.43, -43.38], [49.84, -48.65]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[3.24, -0.16], [0.5, -3], [-3.64, -2.19], [-5.82, -0.97], [-4.37, 0.93], [-0.35, 2.08], [3.64, 2.19], [5.82, 0.97]], "o": [[-6.76, 0.34], [-0.35, 2.08], [3.83, 2.31], [5.82, 0.97], [4.16, -0.89], [0.35, -2.08], [-3.83, -2.31], [-3.78, -0.63]], "v": [[41.96, -53.69], [29.76, -48.41], [34.94, -41.7], [49.9, -36.62], [65.7, -36.56], [72.79, -41.22], [67.6, -47.93], [52.64, -53.02]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.87, -0.09], [3.82, 0.64], [0, 0], [3.94, 2.37], [-0.42, 2.54], [-12.62, -2.11], [-3.93, -2.37], [0.42, -2.54], [4.65, -0.99]], "o": [[-3.38, 0.17], [0, 0], [-5.94, -0.99], [-4.07, -2.45], [0.86, -5.15], [5.94, 0.99], [4.07, 2.45], [-0.42, 2.54], [-1.6, 0.34]], "v": [[60.68, -34.93], [49.74, -35.63], [49.74, -35.63], [34.42, -40.84], [28.77, -48.58], [52.81, -54], [68.12, -48.79], [73.77, -41.06], [65.91, -35.58]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.16, -0.06], [2.13, -1.18], [3.75, -4.55], [1.38, -4.25], [-1.62, -1.34], [-7.52, 9.13], [-1.38, 4.25], [1.62, 1.34]], "o": [[-1.55, 0.08], [-3.91, 2.16], [-3.75, 4.55], [-1.31, 4.04], [3.48, 2.86], [3.75, -4.55], [1.31, -4.04], [-0.69, -0.57]], "v": [[62.34, -62.42], [56.74, -60.52], [44.86, -50.1], [36.9, -36.45], [37.4, -27.98], [57.69, -39.53], [65.64, -53.19], [65.14, -61.65]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.77, -0.24], [0.91, 0.75], [-1.46, 4.52], [-3.83, 4.65], [-4.02, 2.22], [-1.98, -1.64], [1.46, -4.52], [3.83, -4.65]], "o": [[-1.39, 0.07], [-1.99, -1.64], [1.42, -4.37], [3.83, -4.65], [4.16, -2.3], [1.99, 1.64], [-1.41, 4.37], [-6.3, 7.64]], "v": [[40.26, -26.21], [36.76, -27.21], [35.95, -36.76], [44.08, -50.74], [56.25, -61.39], [65.78, -62.42], [66.59, -52.88], [58.46, -38.9]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.27, -0.21], [0.3, -0.11], [-4.14, -11.07], [-4.22, 1.58], [-0.08, 4.25], [2.07, 5.53]], "o": [[-0.33, 0.02], [-4.22, 1.58], [4.14, 11.08], [1.97, -0.74], [0.08, -4.47], [-3.84, -10.28]], "v": [[44.58, -65.44], [43.63, -65.25], [43.49, -41.9], [58.91, -24.39], [62.13, -32.23], [59.06, -47.73]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.43, -0.02], [4.12, 11], [-4.89, 1.83], [-4.48, -11.98], [0.08, -4.59], [2.41, -0.9]], "o": [[-4.83, 0.24], [-4.48, -11.98], [4.89, -1.83], [2.11, 5.64], [-0.09, 4.75], [-0.4, 0.15]], "v": [[58.01, -23.19], [42.55, -41.55], [43.28, -66.18], [59.99, -48.08], [63.14, -32.21], [59.26, -23.45]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 10, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [52.95, 2.5, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [39.95, -19.5, 0], "t": 0, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [43.12, -24.25, 0], "t": 40, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [36.69, -21.73, 0], "t": 85, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"s": [39.95, -19.5, 0], "t": 125}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [-10], "t": 64}, {"s": [0], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-7.95, 1.5], [0, 0], [0, 0], [6.39, -1.34]], "o": [[0, 0], [0, 0], [7.95, -1.5], [0, 0], [0, 0], [-6.39, 1.34]], "v": [[35.53, 13.49], [32.47, 3.63], [49.76, -4.53], [68.15, -3.93], [69.55, 7.06], [51.21, 8.36]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[52.19, 7.23], [17.04, 4.86], [48.06, -14.6], [83.16, -7.78]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0, 0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 10}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[52.19, 7.23], [17.04, 4.86], [48.06, -14.6], [83.16, -7.78]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7176, 0.1255, 0.3529]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0.66, 0.71], [0, 0]], "o": [[0, 0], [-0.66, -0.71], [0, 0]], "v": [[94.47, -26.68], [92.23, -30.99], [89.96, -34.02]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.75}, "c": {"a": 0, "k": [1, 0.7804, 0.1529]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0.7, 0.77], [0, 0]], "o": [[0, 0], [-0.7, -0.77], [0, 0]], "v": [[94.36, -26.56], [90.83, -28.95], [87.62, -32.52]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.75}, "c": {"a": 0, "k": [1, 0.7804, 0.1529]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0.16, 1.22], [0, 0]], "o": [[0, 0], [-0.16, -1.22], [0, 0]], "v": [[94.51, -26.82], [94.59, -29.88], [91.77, -34.34]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.75}, "c": {"a": 0, "k": [1, 0.7804, 0.1529]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [1.44, 0.77], [0, 0]], "o": [[0, 0], [-1.44, -0.77], [0, 0]], "v": [[94.23, -26.39], [91.35, -27.09], [87.46, -30.6]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.75}, "c": {"a": 0, "k": [1, 0.7804, 0.1529]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [-5.27, 1.68], [-3.99, 1.3], [2.15, 2.78]], "o": [[0, 0], [5.08, -1.62], [4, -1.3], [0, 0]], "v": [[68.2, -6.23], [76.48, -18.27], [91.41, -16.42], [94.26, -26.66]]}}}, {"ty": "st", "bm": 0, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.75}, "c": {"a": 0, "k": [0.149, 0.1961, 0.2196]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 11, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [144.17, -34.03, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [144.17, -34.03, 0], "t": 0, "ti": [0.1, 0.62, 0], "to": [0.2, -1.27, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [139.65, -36.29, 0], "t": 31, "ti": [0.05, -0.77, 0], "to": [-0.1, -0.62, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [143.55, -37.73, 0], "t": 65, "ti": [-0.1, -0.62, 0], "to": [-0.05, 0.77, 0]}, {"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [139.36, -31.68, 0], "t": 97, "ti": [-0.08, 1.16, 0], "to": [0.1, 0.62, 0]}, {"s": [144.17, -34.03, 0], "t": 125}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[170.69, -41.56], [170.71, -41.6], [170.68, -41.59]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0], [0.01, -0.03], [0, -0.03], [0, -0.02], [-0.01, 0], [-0.01, 0.03], [0, 0.03], [0, 0.02]], "o": [[-0.01, 0], [-0.01, 0.03], [0, 0.03], [0, 0.02], [0.01, 0], [0.01, -0.03], [0, -0.03], [0, -0.02]], "v": [[169.89, -44.06], [169.87, -44.01], [169.86, -43.93], [169.86, -43.86], [169.88, -43.83], [169.9, -43.87], [169.92, -43.95], [169.91, -44.03]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[170.42, -41.63], [170.45, -41.67], [170.41, -41.66]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0], [-0.01, -0.02], [-0.02, 0], [0.01, 0.02]], "o": [[-0.02, 0], [0.01, 0.02], [0.01, 0], [-0.01, -0.02]], "v": [[170.37, -41.72], [170.36, -41.68], [170.39, -41.65], [170.39, -41.69]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [0, -0.02], [-0.02, 0.01], [0.01, 0.02]], "o": [[-0.02, 0.01], [0.01, 0.02], [0.02, 0], [0, -0.02]], "v": [[170.33, -41.75], [170.31, -41.71], [170.35, -41.68], [170.36, -41.72]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [0, -0.01], [-0.01, 0]], "o": [[-0.01, 0], [0, 0.02], [0.01, -0.04]], "v": [[170.28, -41.78], [170.27, -41.75], [170.29, -41.74]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0.01], [0, 0], [0.03, -0.04], [0.02, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.02], [0, 0], [0, -0.02], [-0.03, 0.04], [0, 0]], "v": [[169.74, -43.33], [169.79, -43.25], [169.82, -43.24], [169.84, -43.26], [169.84, -43.28], [169.84, -43.33], [169.84, -43.45], [169.79, -43.43], [169.72, -43.37]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, 0], [-0.02, 0], [-0.02, -0.01], [-0.02, -0.05], [0, 0.03], [-0.01, 0.01], [-0.01, 0], [0, 0], [0, -0.02], [-0.04, 0.01], [0.02, 0.07], [0.04, 0.01], [0.01, -0.01], [0.01, 0], [0, 0], [0, 0]], "o": [[0.01, 0.03], [0.01, 0], [0.02, 0], [0.02, 0.01], [0, -0.01], [0, -0.03], [0, 0], [0.01, 0.01], [0.01, 0.03], [0.01, 0.02], [-0.01, -0.04], [-0.02, -0.06], [-0.01, 0], [-0.01, 0.01], [0, 0], [0, 0], [0, 0]], "v": [[170.05, -41.53], [170.08, -41.49], [170.13, -41.49], [170.2, -41.47], [170.27, -41.38], [170.27, -41.44], [170.29, -41.49], [170.31, -41.49], [170.33, -41.48], [170.35, -41.42], [170.42, -41.41], [170.37, -41.56], [170.27, -41.66], [170.24, -41.65], [170.2, -41.63], [170.17, -41.75], [170.06, -41.54]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[169.81, -41.4], [169.84, -41.45], [169.79, -41.47]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.03, -0.01], [0, 0]], "o": [[0.01, 0.03], [0, 0], [0, 0]], "v": [[169.26, -43.24], [169.32, -43.19], [169.3, -43.25]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.03], [0, -0.03], [0, -0.02], [0, 0], [0, 0], [-0.02, -0.02], [-0.02, -0.01], [-0.01, 0.02], [0.02, 0.09], [-0.01, 0.01], [0, 0], [0.01, 0.02], [0.01, 0.03], [0.01, 0.02], [0, 0.01]], "o": [[0, 0.05], [0, 0.03], [0, 0.03], [0, 0], [0, 0], [0.01, 0], [0.02, 0.02], [0.02, 0.01], [0.01, -0.02], [0, 0], [0, 0], [0, 0], [-0.01, -0.02], [-0.01, -0.02], [-0.01, -0.02], [0, 0]], "v": [[169.47, -42.06], [169.47, -41.95], [169.48, -41.85], [169.48, -41.78], [169.49, -41.75], [169.5, -41.72], [169.54, -41.69], [169.6, -41.65], [169.64, -41.66], [169.62, -41.82], [169.63, -41.84], [169.64, -41.86], [169.63, -41.9], [169.59, -41.97], [169.56, -42.04], [169.55, -42.08]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.05, 0.01], [-0.04, 0.01], [0.05, -0.01]], "o": [[0.01, 0.05], [0.05, -0.01], [-0.01, -0.04], [-0.05, 0.01]], "v": [[169.64, -41.04], [169.72, -40.99], [169.86, -41.02], [169.77, -41.07]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.03], [0, -0.03], [0, -0.02], [0, 0.03]], "o": [[-0.03, 0.01], [0, 0.03], [0.04, -0.01], [0, -0.02]], "v": [[169.35, -41.97], [169.3, -41.91], [169.3, -41.84], [169.36, -41.89]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.01, 0.04], [0, 0]], "o": [[0.02, -0.02], [0, 0], [0, 0]], "v": [[168.89, -42.13], [168.91, -42.21], [168.87, -42.2]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.02], [-0.02, 0.01], [0.01, 0.02]], "o": [[-0.02, 0.01], [0.01, 0.02], [0.02, 0], [-0.01, -0.02]], "v": [[168.81, -42.42], [168.79, -42.38], [168.83, -42.36], [168.84, -42.4]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0.02], [0.01, 0.03], [0.01, 0.02], [0.03, 0], [0, 0]], "o": [[0.01, -0.03], [0, -0.02], [0, -0.01], [0, -0.02], [0, 0], [0, 0]], "v": [[168.92, -41.84], [168.94, -41.91], [168.93, -41.97], [168.91, -42.02], [168.86, -42.06], [168.84, -42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.02, -0.02], [-0.01, 0], [0, 0.02], [0, 0.01], [0, 0], [0, 0], [0.01, 0.01], [0.01, 0], [0, 0], [0.01, 0], [0, 0], [0, -0.02], [0, 0]], "o": [[0.01, 0.01], [0.02, 0.02], [0, -0.01], [0, -0.02], [0, 0], [0, 0], [0, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0.01], [0, 0], [0, 0]], "v": [[169.06, -41.14], [169.11, -41.1], [169.16, -41.06], [169.17, -41.1], [169.17, -41.14], [169.17, -41.15], [169.16, -41.17], [169.14, -41.2], [169.12, -41.22], [169.11, -41.22], [169.09, -41.22], [169.08, -41.21], [169.06, -41.17], [169.05, -41.16]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.02, 0], [-0.02, 0.01], [0, 0], [0.02, -0.01], [0, -0.01]], "o": [[0.01, 0.03], [0.03, 0], [0, 0], [-0.01, 0], [-0.02, 0.01], [0, 0]], "v": [[167.96, -45.42], [168.01, -45.37], [168.09, -45.37], [168.06, -45.49], [168.01, -45.46], [167.97, -45.43]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[168.59, -43], [168.56, -42.97], [168.57, -42.95], [168.57, -42.94], [168.6, -42.97], [168.59, -42.98]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.02], [0.02, 0], [-0.01, -0.01], [-0.01, 0]], "o": [[0.01, -0.01], [-0.02, 0.01], [0.01, 0.01], [0.01, 0]], "v": [[169, -41.09], [168.98, -41.11], [168.97, -41.09], [169, -41.07]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, -0.01], [0, 0], [-0.01, -0.03], [-0.01, -0.04], [-0.01, -0.03], [0, -0.01], [0, 0.01], [0, 0.03], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0.04], [0, 0.01], [0.02, 0], [0.02, 0], [0.02, 0.01], [0.01, 0.03], [0, -0.03], [0, -0.01]], "o": [[0, 0.01], [0, 0], [0, 0.01], [0.01, 0.03], [0.01, 0.04], [0.01, 0.03], [0.02, 0], [0.01, -0.01], [0, -0.03], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, -0.04], [0, -0.02], [-0.02, 0], [-0.02, 0], [-0.02, -0.01], [-0.01, 0.01], [0, 0.03], [0, 0]], "v": [[168.53, -42.71], [168.56, -42.69], [168.56, -42.68], [168.58, -42.62], [168.61, -42.52], [168.64, -42.43], [168.65, -42.37], [168.67, -42.39], [168.68, -42.44], [168.68, -42.5], [168.71, -42.52], [168.74, -42.55], [168.72, -42.61], [168.72, -42.68], [168.73, -42.75], [168.69, -42.79], [168.63, -42.79], [168.56, -42.79], [168.52, -42.84], [168.51, -42.79], [168.51, -42.73]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.03, -0.03], [-0.04, 0.01], [0, 0], [0, 0.01], [0, 0.01], [-0.02, 0.04], [0, 0], [0.01, 0], [0.04, 0]], "o": [[0.01, 0.04], [0.03, 0.03], [0, 0], [0.01, 0], [0, -0.01], [0.01, -0.01], [0, 0], [0, 0], [-0.04, 0], [-0.04, 0]], "v": [[168.66, -41.84], [168.71, -41.73], [168.82, -41.69], [168.83, -41.7], [168.85, -41.71], [168.86, -41.74], [168.89, -41.82], [168.89, -41.84], [168.88, -41.86], [168.77, -41.86]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [0, -0.02], [-0.01, 0], [0.01, 0.02]], "o": [[-0.01, 0], [0.01, 0.02], [0.02, 0], [0, -0.02]], "v": [[167.78, -44.45], [167.77, -44.41], [167.8, -44.39], [167.82, -44.43]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.02], [-0.02, 0], [0.01, 0.02]], "o": [[-0.02, 0], [0.01, 0.02], [0.02, 0], [-0.01, -0.02]], "v": [[168.6, -41.02], [168.58, -40.98], [168.62, -40.95], [168.63, -41]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -0.02], [-0.03, 0], [-0.04, 0.01], [0.02, 0], [0.03, -0.01], [0.02, -0.01]], "o": [[0.04, 0], [0.03, 0], [-0.01, -0.04], [-0.02, 0], [-0.01, 0], [-0.02, 0.01]], "v": [[168.22, -42.38], [168.32, -42.37], [168.43, -42.38], [168.38, -42.44], [168.3, -42.43], [168.25, -42.42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[168.23, -42.2], [168.26, -42.21], [168.22, -42.23]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.02], [-0.02, 0.01], [0.01, 0.02]], "o": [[-0.02, 0.01], [0.01, 0.02], [0.02, 0], [-0.01, -0.02]], "v": [[168.23, -42.03], [168.21, -41.99], [168.25, -41.96], [168.27, -42.01]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.02], [-0.02, 0.01], [0.01, 0.02]], "o": [[-0.02, 0.01], [0.01, 0.02], [0.02, 0], [-0.01, -0.02]], "v": [[168.11, -42.14], [168.09, -42.1], [168.13, -42.07], [168.14, -42.11]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0.04], [-0.02, 0.02], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, -0.01], [0, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[168.03, -41.84], [168.04, -41.84], [168.04, -41.91], [168.06, -41.99], [168.04, -42], [168.03, -42.01], [168, -42.01], [167.99, -42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.06, 0.01], [0.01, 0.03], [0.02, 0.01], [0.04, 0], [0.01, 0], [0.02, 0], [0.01, 0.01], [0.04, 0.02], [0.01, 0.01], [0.03, 0], [0.03, 0], [0.03, 0], [0.01, 0], [0.05, 0.03], [0.07, 0.03], [0, 0], [0, -0.04], [0, -0.01], [0.02, 0.03], [0.04, 0.01], [0, 0], [0, 0], [-0.01, -0.04], [-0.01, -0.01], [0.02, 0], [0.01, 0.01], [-0.01, 0.03], [0.04, 0], [0, 0], [0.01, 0], [0.01, 0], [0, 0], [0.02, -0.04], [0.03, -0.01], [0, 0], [0.01, -0.01], [0, -0.01], [0, 0], [0.05, -0.01], [0.02, 0.01], [0.02, 0.02], [0.01, 0.01], [0.03, 0.09], [0.02, 0.08], [0.01, 0.09], [0, 0.1], [-0.01, 0.09], [-0.02, 0.06], [-0.05, 0.03], [-0.01, 0.05], [-0.01, 0.05], [0, 0.01], [-0.01, 0.03], [0, 0], [0, 0.01], [-0.01, 0.02], [0, 0.01], [0.01, 0.01], [0, 0.01], [-0.01, 0.02], [-0.01, 0.01], [0, -0.01], [-0.03, 0.01], [-0.01, 0.03], [-0.01, 0.03], [0, 0.03], [0, 0.01], [-0.03, 0.01], [-0.03, 0.02], [-0.02, 0.02], [0, 0.03], [0, 0], [0, 0.02], [-0.01, 0.01], [-0.03, 0.01], [-0.01, 0.01], [-0.03, 0.04], [-0.03, 0.04], [0, 0], [0.01, 0.02], [-0.01, 0.01], [-0.02, 0.02], [-0.01, 0.01], [0, 0.02], [0, 0.01], [-0.01, 0.02], [0, 0.01], [-0.01, 0.06], [0, 0.01], [0, 0.03], [0.01, 0.03], [0, 0.03], [0, 0.01], [-0.01, 0.02], [0, 0.01], [0, 0.03], [0, 0.01], [0.01, 0], [0.01, 0], [0.07, 0], [0.02, 0], [0, 0], [0.03, -0.02], [0.01, 0], [0.03, 0.03], [0.01, 0], [0.08, 0.01], [0.02, 0], [0, 0], [0.12, -0.07], [0.04, -0.12], [0, 0], [-0.02, -0.01], [0, 0], [-0.08, -0.04], [-0.07, -0.05], [-0.01, -0.01], [0, 0], [0.01, -0.06], [0.01, 0], [0.02, 0.03], [0.01, 0.02], [0.08, -0.02], [-0.03, 0.01], [0.01, 0.03], [0, 0], [0.04, -0.02], [0.05, -0.01], [0, 0], [0.03, 0], [0.03, 0.01], [0.03, 0.05], [0.05, 0.03], [0.04, 0], [0.04, 0], [0.03, 0], [0, 0.01], [0, 0], [0.01, 0.02], [0, 0.01], [-0.02, 0.05], [-0.02, 0.05], [-0.03, 0.01], [-0.04, 0.02], [-0.03, 0.01], [-0.01, 0.01], [-0.02, 0.02], [-0.01, 0], [-0.02, 0.01], [-0.02, 0.05], [0, 0], [0, 0.01], [0.04, 0.03], [0, 0], [0, 0], [-0.09, -0.02], [-0.07, -0.1], [0, 0], [0.01, 0.04], [-0.04, 0.01], [-0.01, 0], [0, 0], [-0.04, 0], [-0.01, 0], [-0.03, 0.01], [-0.03, 0.01], [-0.03, 0], [-0.01, -0.02], [-0.02, -0.05], [0, -0.01], [-0.09, -0.04], [-0.02, -0.05], [0, -0.03], [0, -0.01], [-0.02, 0.02], [-0.02, 0.01], [-0.01, 0], [0, 0], [-0.02, 0], [0, 0], [0, -0.03], [-0.01, 0], [-0.04, -0.03], [-0.01, -0.01], [-0.02, -0.06], [-0.01, -0.04], [-0.01, -0.06], [-0.02, -0.07], [-0.01, -0.05], [-0.04, -0.05], [0.04, -0.04], [0.01, -0.01], [0.05, 0.02], [0.01, 0], [-0.01, -0.02], [-0.01, -0.02], [-0.01, -0.02], [0, 0], [-0.01, -0.01], [0, 0], [0, 0], [0.01, -0.03], [0.04, -0.01], [0.02, 0.03], [0.03, -0.01], [0, 0], [0.01, -0.04], [0.02, -0.04], [0.01, -0.04], [-0.01, -0.05], [0.02, 0.02], [0.01, 0], [0, 0], [0.01, -0.01], [0.04, -0.01], [0, 0], [0.01, -0.01], [0.01, 0], [0.04, -0.01], [0.01, 0], [0, 0], [0.01, 0.01], [0, 0.01], [0, 0.01], [-0.01, 0.01], [0, 0], [0, 0], [0.02, 0.02], [0, 0], [0, -0.01], [0.01, -0.02], [0, -0.01], [0, 0], [0, 0], [-0.02, -0.02], [0, 0], [0.03, -0.04], [0.02, -0.03], [0.01, 0], [0, 0], [0, 0], [0.02, -0.06], [0.01, -0.02], [-0.04, 0.03], [-0.01, 0], [0, 0], [0, 0.02], [0, 0.01], [-0.02, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0], [0.01, 0.01], [0, 0], [-0.05, 0.01], [-0.03, 0], [-0.01, -0.01], [0, 0], [-0.05, 0.02], [-0.05, 0.01], [-0.01, -0.01], [0, 0], [-0.01, 0], [0.04, -0.02], [0.01, -0.01], [0, 0], [-0.02, 0.01], [0.01, 0.05], [0, 0.06], [-0.01, 0.05], [-0.04, 0], [0, 0], [-0.01, -0.02], [-0.03, 0.01], [-0.01, 0.02], [-0.02, 0.01], [-0.02, -0.01], [-0.02, -0.02], [0, 0], [0.03, 0.01], [0.01, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0], [-0.05, 0], [-0.01, 0], [-0.06, 0.02], [-0.01, 0], [-0.02, 0], [-0.01, 0], [-0.01, -0.03], [-0.01, 0], [-0.02, 0.02], [0, 0], [-0.01, 0], [0.01, 0], [0.02, 0.02], [0, 0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0], [0, 0], [0, 0], [0.03, -0.03], [0.02, -0.04], [0, 0], [0.02, -0.02], [0.02, -0.01], [0.02, 0], [0.02, 0], [0, 0], [0.01, -0.01], [0.01, 0], [0.02, 0.02], [0.01, 0.02], [0.01, 0.02], [0.03, -0.01], [-0.01, -0.03], [0, -0.04], [0.03, -0.02], [0.1, 0.03], [0, 0], [0.01, 0], [0, 0], [0, -0.02], [0, 0], [-0.02, -0.07], [-0.01, -0.02], [0, 0], [0, 0.02], [0, 0.01], [-0.03, -0.02], [-0.01, -0.02], [0.01, -0.01], [-0.01, -0.03], [0, 0.01], [0, 0.02], [0, 0.02], [-0.02, 0.01], [0, 0], [-0.02, 0.02], [0, -0.01], [0, 0], [0.05, -0.06], [0.09, -0.02], [0.03, -0.01]], "o": [[0, -0.01], [-0.01, -0.03], [-0.01, 0], [-0.04, 0], [0, 0], [-0.02, 0], [-0.01, 0], [-0.04, -0.02], [0, 0], [-0.03, 0], [-0.03, 0], [-0.03, 0], [-0.08, -0.01], [-0.05, -0.03], [0, 0], [-0.03, 0.01], [0, 0.04], [-0.05, 0], [-0.01, -0.03], [0, 0], [0, 0], [-0.01, 0.01], [0.01, 0.04], [-0.01, 0], [-0.02, 0], [-0.02, -0.02], [0.01, -0.03], [0, 0], [0, 0], [0, 0.01], [0, 0], [-0.03, 0.02], [-0.02, 0.04], [0, 0], [0, 0.01], [0, 0.02], [0, 0], [-0.04, 0.01], [-0.05, 0.01], [-0.01, 0], [-0.02, -0.02], [-0.07, -0.06], [-0.03, -0.09], [-0.02, -0.06], [-0.01, -0.09], [0, -0.1], [0.01, -0.09], [0.01, -0.05], [0.05, -0.03], [0.01, -0.01], [0.01, -0.05], [0.01, 0], [0.01, 0], [0.01, 0], [0.01, -0.01], [0.01, -0.02], [0.01, -0.01], [-0.01, -0.01], [0, -0.01], [0.01, -0.02], [0.01, 0.02], [0.01, 0.01], [0.02, 0], [0.01, -0.03], [0.01, -0.04], [0, -0.03], [0.02, 0], [0.03, -0.01], [0.03, -0.02], [0.02, -0.02], [0, 0], [0, 0], [0, -0.02], [0.01, 0], [0.03, -0.01], [0.06, -0.07], [0.03, -0.04], [0.03, -0.04], [0, -0.01], [-0.01, -0.02], [0, 0], [0.02, -0.02], [0, -0.01], [0, -0.02], [0, -0.01], [0.01, -0.02], [0.01, -0.02], [0.01, -0.06], [0, 0], [0, -0.03], [0, -0.03], [0, -0.03], [0, 0], [0.01, -0.02], [0.01, -0.02], [0, -0.03], [0, 0], [-0.01, -0.01], [-0.02, 0], [-0.07, 0], [0, 0], [-0.01, 0], [-0.03, 0.02], [-0.01, -0.01], [-0.03, -0.02], [-0.03, 0], [-0.07, 0], [0, 0], [-0.11, 0.03], [-0.12, 0.07], [0, 0], [0.01, 0.05], [0, 0], [0.08, 0.02], [0.08, 0.04], [0, 0.01], [0.01, 0.01], [0, 0.02], [-0.01, 0.06], [-0.03, 0.01], [-0.02, -0.03], [-0.04, 0.04], [-0.01, -0.04], [0.03, -0.01], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [0, 0], [-0.01, -0.05], [-0.03, 0.01], [-0.07, -0.02], [-0.03, -0.05], [-0.01, -0.01], [-0.04, 0], [-0.04, 0], [-0.03, 0], [0, 0], [-0.02, 0], [-0.01, -0.02], [0.06, -0.01], [0.02, -0.05], [0.02, 0], [0.03, -0.01], [0.04, -0.01], [0.03, -0.01], [0.01, 0], [0.02, -0.02], [0.04, -0.04], [0.03, -0.01], [0, 0], [-0.01, -0.04], [0.01, -0.01], [0, 0], [0, 0], [0.11, 0.01], [0.1, 0.02], [0, 0], [0.03, -0.01], [0, -0.04], [0.01, 0], [0.01, 0], [0.01, 0], [0.04, 0], [0.01, 0], [0.03, -0.01], [0.03, -0.01], [0.03, 0], [0.01, 0.01], [0.02, 0.05], [0.05, 0], [0.09, 0.04], [0, 0.01], [0, 0.03], [0.03, -0.01], [0.02, -0.01], [0.01, 0], [0.01, 0], [0.02, 0], [0.03, 0], [0.01, 0], [0, 0.03], [0.01, 0.01], [0.04, 0.03], [0.02, 0.03], [0.02, 0.06], [0.02, 0.06], [0.01, 0.06], [0.02, 0.06], [0.01, 0.05], [-0.01, 0.01], [-0.04, 0.04], [0, 0.01], [-0.05, -0.02], [0, 0.01], [0.01, 0.02], [0.01, 0.02], [0.01, 0.02], [0, 0.01], [0.01, 0.01], [0, 0], [0.01, 0.04], [-0.01, 0.03], [-0.04, 0.01], [-0.02, -0.03], [0, 0], [0.01, 0.05], [-0.01, 0.04], [-0.01, 0.04], [-0.01, 0.04], [0, -0.01], [-0.02, -0.02], [0, 0], [-0.02, 0.01], [-0.01, 0.01], [0, 0], [0, 0.01], [-0.01, 0.01], [-0.01, 0.01], [-0.04, 0.01], [0, 0], [-0.01, 0], [-0.01, -0.01], [0.01, -0.01], [0, -0.01], [0, 0], [0, 0], [-0.02, 0.01], [0, 0], [0, 0], [0, 0.01], [-0.01, 0.02], [0, 0], [0, 0], [0, 0.01], [0.02, 0.02], [-0.04, 0.05], [-0.03, 0.04], [-0.02, 0.03], [0, 0], [0, 0], [-0.01, 0.01], [-0.02, 0.06], [0.02, 0.02], [0.04, -0.03], [0, 0], [0.01, -0.01], [0, -0.02], [0, 0], [0.02, 0], [0, 0], [0, 0], [0.02, 0], [0.01, 0], [0, -0.01], [0.03, 0.04], [0.05, -0.01], [0.01, 0.01], [0, 0], [0.06, 0.03], [0.05, -0.02], [0, 0.01], [0, 0], [0, 0.01], [-0.01, 0.01], [-0.04, 0.02], [0, 0], [0.01, 0.03], [0.01, 0], [-0.01, -0.05], [0, -0.06], [0.01, -0.05], [0, 0], [0, 0.02], [0.01, 0.02], [0.03, -0.01], [0.01, -0.01], [0.04, -0.01], [0.02, 0.02], [0, 0], [-0.01, 0], [-0.03, -0.01], [0, 0], [0, 0], [0, -0.01], [0, -0.01], [0.02, 0.01], [0.06, 0], [0.01, 0], [0.06, -0.02], [0.01, 0], [0.02, 0], [0, 0.01], [0.02, 0.03], [0.01, 0.01], [0.02, -0.02], [0, -0.01], [0.01, 0], [0, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [0, 0], [0.02, 0], [0.03, 0], [0, 0], [0.02, 0.06], [-0.03, 0.03], [0, 0], [0, 0.04], [-0.02, 0.02], [-0.02, 0.01], [-0.02, 0], [0, 0], [0.01, 0], [0, 0.01], [-0.04, 0.01], [-0.01, -0.02], [-0.01, -0.02], [-0.01, -0.02], [-0.01, 0], [0.01, 0.03], [0, 0.04], [-0.03, 0.02], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0.02], [0, 0.02], [0.03, 0.07], [0, 0], [0, -0.01], [0, -0.02], [0.03, 0.01], [0.03, 0.02], [0.01, 0.03], [-0.01, 0.01], [0.01, 0.02], [0, -0.01], [0, -0.02], [0.01, -0.02], [0, 0], [0.02, -0.02], [0.04, -0.03], [0, 0], [-0.11, 0.03], [-0.05, 0.06], [-0.05, 0.01], [-0.03, 0.01]], "v": [[170.36, -40.73], [170.35, -40.8], [170.3, -40.86], [170.22, -40.86], [170.16, -40.85], [170.13, -40.86], [170.09, -40.87], [170.01, -40.9], [169.93, -40.94], [169.87, -40.94], [169.78, -40.94], [169.69, -40.94], [169.64, -40.95], [169.44, -41], [169.25, -41.08], [169.24, -41.08], [169.2, -41.01], [169.19, -40.93], [169.1, -40.97], [169.02, -41.03], [169.01, -41.01], [169, -40.99], [169, -40.91], [169.03, -40.85], [168.99, -40.85], [168.95, -40.87], [168.93, -40.95], [168.88, -40.99], [168.87, -40.98], [168.86, -40.98], [168.84, -40.96], [168.89, -40.76], [168.82, -40.67], [168.74, -40.59], [168.55, -40.55], [168.55, -40.51], [168.53, -40.48], [168.48, -40.46], [168.36, -40.44], [168.25, -40.44], [168.21, -40.48], [168.16, -40.52], [168.03, -40.76], [167.95, -41.01], [167.92, -41.24], [167.91, -41.51], [167.92, -41.79], [167.95, -42.02], [168.04, -42.15], [168.13, -42.28], [168.15, -42.37], [168.16, -42.46], [168.18, -42.51], [168.2, -42.51], [168.21, -42.52], [168.24, -42.56], [168.26, -42.6], [168.26, -42.63], [168.24, -42.66], [168.26, -42.7], [168.29, -42.73], [168.31, -42.69], [168.36, -42.69], [168.4, -42.74], [168.43, -42.84], [168.44, -42.94], [168.44, -43], [168.52, -43.02], [168.61, -43.06], [168.68, -43.12], [168.71, -43.21], [168.87, -43.25], [168.87, -43.28], [168.89, -43.33], [168.95, -43.34], [169.02, -43.36], [169.15, -43.53], [169.25, -43.65], [169.3, -43.71], [169.28, -43.75], [169.28, -43.79], [169.31, -43.83], [169.35, -43.87], [169.36, -43.91], [169.37, -43.94], [169.39, -43.99], [169.42, -44.03], [169.44, -44.15], [169.46, -44.25], [169.46, -44.3], [169.45, -44.38], [169.43, -44.47], [169.43, -44.53], [169.44, -44.56], [169.44, -44.6], [169.46, -44.67], [169.46, -44.71], [169.45, -44.72], [169.41, -44.73], [169.27, -44.74], [169.13, -44.74], [169.11, -44.82], [169.05, -44.79], [168.99, -44.76], [168.92, -44.8], [168.87, -44.85], [168.71, -44.86], [168.57, -44.87], [168.47, -44.85], [168.13, -44.7], [167.9, -44.42], [167.91, -44.38], [167.96, -44.29], [168.12, -44.26], [168.36, -44.16], [168.57, -44.02], [168.58, -44], [168.59, -43.97], [168.58, -43.86], [168.55, -43.77], [168.48, -43.81], [168.43, -43.89], [168.26, -43.79], [168.29, -43.86], [168.32, -43.92], [168.31, -43.95], [168.2, -43.87], [168.07, -43.82], [167.99, -43.8], [167.93, -43.86], [167.83, -43.87], [167.69, -43.99], [167.56, -44.11], [167.48, -44.13], [167.36, -44.13], [167.24, -44.14], [167.19, -44.16], [167.04, -44.76], [166.99, -44.79], [166.97, -44.83], [167.08, -44.92], [167.13, -45.06], [167.2, -45.08], [167.3, -45.12], [167.4, -45.17], [167.46, -45.2], [167.5, -45.24], [167.54, -45.28], [167.64, -45.34], [167.72, -45.43], [168.01, -45.5], [168, -45.58], [167.95, -45.63], [167.96, -45.64], [167.98, -45.64], [168.29, -45.6], [168.53, -45.42], [168.55, -45.43], [168.59, -45.5], [168.63, -45.57], [168.66, -45.57], [168.68, -45.57], [168.75, -45.57], [168.82, -45.57], [168.87, -45.58], [168.95, -45.6], [169.03, -45.61], [169.09, -45.57], [169.13, -45.48], [169.17, -45.38], [169.38, -45.32], [169.54, -45.19], [169.55, -45.13], [169.56, -45.07], [169.63, -45.1], [169.7, -45.13], [169.71, -45.13], [169.73, -45.13], [169.79, -45.13], [169.84, -45.13], [169.85, -45.08], [169.87, -45.02], [169.93, -44.97], [170, -44.91], [170.06, -44.78], [170.1, -44.63], [170.14, -44.45], [170.17, -44.25], [170.21, -44.09], [170.28, -43.94], [170.22, -43.86], [170.15, -43.79], [170.07, -43.81], [169.98, -43.84], [170, -43.8], [170.03, -43.74], [170.06, -43.67], [170.08, -43.63], [170.11, -43.6], [170.13, -43.57], [170.15, -43.51], [170.15, -43.41], [170.09, -43.35], [170.01, -43.38], [169.94, -43.42], [169.94, -43.42], [169.93, -43.29], [169.89, -43.18], [169.85, -43.06], [169.84, -42.93], [169.81, -42.98], [169.77, -43.02], [169.76, -43.03], [169.7, -43], [169.62, -42.96], [169.66, -42.78], [169.65, -42.76], [169.61, -42.74], [169.54, -42.72], [169.47, -42.7], [169.43, -42.64], [169.4, -42.65], [169.37, -42.68], [169.39, -42.71], [169.4, -42.75], [169.4, -42.76], [169.4, -42.78], [169.34, -42.81], [169.32, -42.81], [169.31, -42.8], [169.29, -42.76], [169.27, -42.71], [169.27, -42.7], [169.27, -42.68], [169.3, -42.65], [169.32, -42.61], [169.22, -42.49], [169.14, -42.4], [169.1, -42.35], [169.14, -42.33], [169.1, -42.17], [169.05, -42.06], [169.01, -41.94], [169.08, -41.96], [169.16, -42.01], [169.18, -42.01], [169.2, -42.05], [169.2, -42.09], [169.23, -42.1], [169.26, -42.09], [169.26, -42.15], [169.29, -42.16], [169.33, -42.16], [169.33, -42.17], [169.32, -42.17], [169.44, -42.14], [169.56, -42.15], [169.59, -42.13], [169.63, -42.1], [169.79, -42.08], [169.95, -42.13], [169.96, -42.09], [169.97, -42.06], [169.98, -42.05], [169.92, -41.99], [169.85, -41.94], [169.94, -41.58], [169.98, -41.55], [169.99, -41.63], [169.97, -41.79], [169.97, -41.95], [170.04, -42.03], [170.16, -41.92], [170.19, -41.86], [170.24, -41.84], [170.3, -41.87], [170.35, -41.9], [170.43, -41.89], [170.48, -41.83], [170.64, -41.87], [170.58, -41.88], [170.53, -41.91], [170.51, -41.92], [170.52, -41.94], [170.53, -41.97], [170.54, -41.99], [170.66, -41.98], [170.76, -41.97], [170.86, -41.99], [170.96, -42.02], [171, -42.01], [171.04, -42.01], [171.07, -41.95], [171.1, -41.91], [171.14, -41.93], [171.17, -41.96], [171.18, -41.97], [171.18, -41.96], [171.15, -42], [171.13, -42.03], [171.12, -42.05], [171.12, -42.06], [171.15, -42.07], [171.22, -42.07], [171.26, -42.07], [171.27, -42.05], [171.26, -41.91], [171.19, -41.79], [171.2, -41.76], [171.18, -41.66], [171.12, -41.63], [171.05, -41.62], [171, -41.61], [171.09, -41.52], [171.09, -41.5], [171.07, -41.49], [170.99, -41.5], [170.95, -41.56], [170.93, -41.62], [170.87, -41.64], [170.87, -41.58], [170.89, -41.48], [170.85, -41.4], [170.65, -41.41], [170.6, -41.62], [170.58, -41.62], [170.56, -41.63], [170.55, -41.59], [170.55, -41.56], [170.59, -41.42], [170.64, -41.28], [170.66, -41.27], [170.67, -41.31], [170.67, -41.35], [170.76, -41.3], [170.81, -41.22], [170.8, -41.17], [170.8, -41.11], [170.81, -41.09], [170.81, -41.15], [170.82, -41.22], [170.86, -41.26], [170.93, -41.34], [170.98, -41.39], [171.04, -41.42], [171.08, -41.02], [170.84, -40.88], [170.62, -40.76], [170.5, -40.73]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.04], [-0.03, 0.01], [0.01, 0.04]], "o": [[-0.03, 0.01], [0.01, 0.04], [0.02, 0], [-0.01, -0.04]], "v": [[167.32, -36.27], [167.29, -36.19], [167.35, -36.14], [167.36, -36.21]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.02, 0], [0, -0.02], [-0.02, 0]], "o": [[0, -0.02], [-0.02, 0.01], [0.02, 0.02], [0.02, 0]], "v": [[165.95, -40.36], [165.91, -40.38], [165.89, -40.34], [165.94, -40.32]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0.07], [-0.03, -0.1], [0, 0]], "o": [[-0.08, 0.02], [0, 0], [0.07, -0.07]], "v": [[165.77, -40.88], [165.68, -40.69], [165.69, -40.66]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.04], [-0.03, 0.01], [0.01, 0.04]], "o": [[-0.03, 0.01], [0.01, 0.04], [0.02, 0], [-0.01, -0.04]], "v": [[165.48, -41.47], [165.45, -41.41], [165.51, -41.36], [165.53, -41.43]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.01, -0.06], [0.01, -0.04], [0, -0.03], [0, -0.01], [0.02, 0.04], [-0.01, 0.07], [0, 0], [0, 0.01], [0, 0.01]], "o": [[-0.01, 0], [-0.01, 0.06], [0.01, 0.02], [0, 0.03], [0.08, -0.02], [-0.02, -0.04], [0, 0], [0, -0.01], [0, -0.01], [0, 0]], "v": [[166.72, -35.85], [166.7, -35.75], [166.67, -35.6], [166.68, -35.53], [166.69, -35.47], [166.78, -35.57], [166.75, -35.74], [166.8, -35.8], [166.8, -35.83], [166.8, -35.87]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[164.41, -41.51], [164.49, -41.53], [164.45, -41.66]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[164.45, -41.09], [164.51, -41.11], [164.43, -41.15]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0.01], [-0.01, -0.05], [0, 0.02], [0.01, 0.03]], "o": [[-0.01, -0.01], [0.01, 0.05], [0.02, -0.02], [-0.01, -0.02]], "v": [[166.13, -34.53], [166.13, -34.46], [166.16, -34.42], [166.17, -34.48]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.01, -0.05], [-0.03, -0.03], [-0.04, -0.04], [0.01, 0.03], [0.02, 0.09], [0.01, 0.03], [0.04, -0.03]], "o": [[0.02, 0.06], [0.03, 0.03], [0.03, -0.01], [0.01, -0.02], [-0.02, -0.1], [-0.04, 0.01], [-0.04, 0.03]], "v": [[163.97, -40.35], [164.04, -40.21], [164.15, -40.1], [164.19, -40.16], [164.17, -40.33], [164.13, -40.52], [164.01, -40.47]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.03], [0.01, -0.01], [0.01, -0.02], [0, -0.01], [0, 0], [-0.01, -0.09], [-0.01, -0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0.03], [-0.02, 0.02], [-0.06, 0.03], [-0.01, 0], [-0.04, 0.02], [-0.01, 0.01], [0.07, 0.06], [0.05, 0.05], [0, 0]], "o": [[-0.01, 0.01], [0, 0.04], [-0.02, 0.02], [-0.01, 0.03], [0, 0], [0, 0.03], [0.01, 0.09], [0, 0], [0, 0], [0, 0], [0, -0.01], [0.01, -0.02], [0.02, -0.01], [0.07, -0.03], [0.02, -0.02], [0.04, -0.02], [-0.07, -0.04], [-0.07, -0.05], [0, 0], [0, 0]], "v": [[163.98, -41.22], [163.97, -41.15], [163.95, -41.08], [163.92, -41.02], [163.9, -40.97], [163.89, -40.95], [163.89, -40.76], [163.91, -40.59], [163.94, -40.59], [163.97, -40.6], [163.99, -40.62], [164, -40.68], [164.04, -40.74], [164.16, -40.8], [164.28, -40.85], [164.36, -40.9], [164.43, -40.95], [164.23, -41.09], [164.06, -41.24], [164.01, -41.23]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, -0.01], [-0.01, -0.04], [-0.03, 0.01], [0.01, 0.04]], "o": [[-0.03, 0.01], [0.01, 0.04], [0.03, -0.01], [-0.01, -0.04]], "v": [[163.91, -40.15], [163.87, -40.09], [163.93, -40.04], [163.97, -40.11]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.07, 0.05], [-0.08, 0.02], [0.02, 0.04], [0.03, 0.05], [0.03, 0.04], [0.01, 0], [0, 0], [0, 0], [0, -0.01], [0.01, -0.02], [0.01, 0], [0.03, -0.02], [0.02, 0]], "o": [[0.08, -0.02], [0.07, -0.05], [0, -0.01], [-0.02, -0.04], [-0.03, -0.05], [-0.03, -0.04], [0, 0], [0, 0], [0, 0.03], [-0.01, 0.01], [-0.01, 0.02], [-0.02, 0.02], [-0.03, 0.02], [0, 0]], "v": [[165.4, -34.08], [165.62, -34.18], [165.84, -34.29], [165.8, -34.37], [165.73, -34.51], [165.63, -34.64], [165.57, -34.7], [165.54, -34.68], [165.53, -34.61], [165.52, -34.56], [165.5, -34.51], [165.48, -34.48], [165.4, -34.43], [165.32, -34.39]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.02, -0.06], [0, 0]], "o": [[-0.04, 0.02], [0, 0], [0, 0]], "v": [[163.69, -41.01], [163.65, -40.89], [163.71, -40.9]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.03, -0.01], [-0.02, -0.02], [-0.01, 0]], "o": [[0.01, -0.02], [-0.02, 0.01], [0.01, 0.02], [0.02, 0]], "v": [[163.28, -41.09], [163.25, -41.12], [163.24, -41.08], [163.27, -41.05]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.01], [0, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0.01]], "o": [[-0.01, 0.01], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0]], "v": [[163.32, -40.83], [163.31, -40.8], [163.3, -40.78], [163.31, -40.75], [163.32, -40.74], [163.35, -40.81], [163.36, -40.84], [163.34, -40.87]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.02, 0.06], [0, 0]], "o": [[0.03, -0.04], [0, 0], [0, 0]], "v": [[163.5, -39.78], [163.53, -39.94], [163.47, -39.92]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[163.3, -39.55], [163.28, -39.61], [163.23, -39.6]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.04], [-0.03, 0.01], [0.01, 0.04]], "o": [[-0.03, 0.01], [0.01, 0.04], [0.02, 0], [-0.01, -0.04]], "v": [[163.1, -40.13], [163.07, -40.05], [163.13, -40], [163.14, -40.07]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[163.02, -39.72], [163.06, -39.82], [163, -39.8]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, -0.08], [-0.02, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [0, 0.05], [-0.01, 0], [-0.06, 0.05], [-0.02, 0.02], [0, 0], [0, 0], [0.05, 0.03], [0.02, 0.01], [0.07, -0.09]], "o": [[0, 0.01], [0.02, 0.01], [0, 0], [0, 0], [0.01, -0.02], [0, -0.05], [0.02, -0.03], [0.06, -0.05], [0, 0], [0, 0], [-0.03, -0.02], [-0.05, -0.03], [-0.01, 0.08], [-0.06, 0.09]], "v": [[162.82, -39.99], [162.85, -39.96], [162.89, -39.94], [162.94, -39.95], [162.96, -39.96], [162.96, -40.07], [162.98, -40.15], [163.1, -40.26], [163.22, -40.36], [163.22, -40.37], [163.21, -40.41], [163.1, -40.47], [163, -40.52], [162.88, -40.26]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, -0.08], [-0.1, -0.05], [0, 0.09], [0, 0], [0.01, 0.04], [0.05, 0.06], [0.05, -0.01], [0.08, -0.03]], "o": [[0.03, 0.12], [0.04, -0.05], [0, 0], [0.03, -0.01], [-0.02, -0.06], [-0.05, -0.06], [-0.07, 0.02], [-0.08, 0.03]], "v": [[162.63, -40.72], [162.82, -40.47], [162.89, -40.69], [163.15, -40.63], [163.18, -40.7], [163.09, -40.88], [162.94, -40.95], [162.71, -40.89]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.08, 0], [0.01, -0.03], [0, -0.02], [-0.03, -0.18], [-0.01, -0.05], [0, 0], [-0.02, 0.09], [0.03, 0.12], [0.02, 0.04]], "o": [[0, 0.01], [-0.01, 0.03], [0, 0.06], [0.03, 0.18], [0, 0], [0.02, -0.1], [0.01, -0.09], [-0.01, -0.05], [-0.01, -0.04]], "v": [[162.5, -40.52], [162.49, -40.46], [162.48, -40.39], [162.53, -40.04], [162.59, -39.7], [162.65, -39.71], [162.7, -40.01], [162.68, -40.32], [162.64, -40.46]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.03, 0.01], [-0.03, 0.02], [-0.01, 0.01]], "o": [[0.01, 0.04], [0.02, 0], [0.03, -0.02], [0, 0]], "v": [[162.12, -40.56], [162.18, -40.51], [162.25, -40.55], [162.31, -40.61]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.02, 0.09], [0, 0]], "o": [[0.08, -0.02], [0, 0], [0, 0]], "v": [[161.98, -39.94], [162.07, -40.11], [161.94, -40.08]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.02, -0.04], [-0.04, -0.01], [0, 0.05], [0.01, 0.02]], "o": [[0.01, 0.02], [0.02, 0.04], [0.03, -0.03], [0.01, -0.05], [0, 0]], "v": [[161.33, -38.01], [161.37, -37.92], [161.45, -37.84], [161.5, -37.95], [161.5, -38.05]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[160.92, -38.94], [160.89, -38.88], [161.06, -38.92], [161.02, -38.93], [160.98, -38.95], [160.95, -38.96]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.01, -0.02], [0.01, 0], [0, 0], [0, 0], [-0.01, 0.01], [0, 0.01], [0, 0]], "o": [[-0.01, 0], [-0.01, 0.02], [0, 0], [0, 0], [0.01, -0.01], [0.01, -0.01], [0, 0], [0, 0]], "v": [[161.83, -34.41], [161.81, -34.38], [161.78, -34.35], [161.79, -34.32], [161.8, -34.29], [161.83, -34.33], [161.84, -34.36], [161.84, -34.38]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, -0.07], [-0.01, -0.02], [0, -0.01], [-0.04, -0.02], [-0.01, -0.01], [-0.04, -0.05], [-0.02, 0], [-0.03, 0], [0, -0.01], [-0.02, -0.03], [0, -0.01], [-0.01, 0.05], [0.01, 0.05], [0.07, 0.12], [0.13, -0.03], [0.06, -0.03]], "o": [[0, 0.01], [0.01, 0.02], [0, 0.01], [0.04, 0.02], [0.01, 0.01], [0.04, 0.06], [0.01, 0.01], [0.03, 0], [0.01, 0], [0.02, 0.03], [0.06, -0.03], [0.01, -0.05], [-0.02, -0.09], [-0.07, -0.12], [-0.07, 0.02], [-0.06, 0.03]], "v": [[160.92, -37.44], [160.94, -37.4], [160.96, -37.35], [161.03, -37.3], [161.1, -37.25], [161.19, -37.16], [161.29, -37.07], [161.35, -37.05], [161.41, -37.03], [161.46, -36.99], [161.5, -36.92], [161.62, -37.04], [161.62, -37.2], [161.48, -37.52], [161.18, -37.66], [160.98, -37.59]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0.02], [0.01, -0.03], [-0.02, 0.01], [-0.01, 0.02]], "o": [[-0.02, -0.02], [-0.01, 0.03], [0.02, 0.02], [0.01, -0.02]], "v": [[160.69, -38.4], [160.65, -38.38], [160.66, -38.34], [160.7, -38.34]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.04], [0.01, -0.01], [0, 0], [0, 0], [-0.01, 0.01], [0, 0.01], [0.01, 0.07], [0.01, 0.02], [0.03, -0.02], [-0.01, -0.04], [0, 0]], "o": [[0.01, 0.02], [0, 0.05], [0, 0], [0, 0], [0, -0.01], [0.01, -0.01], [0, -0.02], [-0.01, -0.07], [-0.03, 0.01], [-0.03, 0.02], [0, 0], [0, 0]], "v": [[160.75, -37.98], [160.75, -37.88], [160.73, -37.79], [160.76, -37.8], [160.79, -37.81], [160.81, -37.84], [160.83, -37.88], [160.8, -38.02], [160.77, -38.17], [160.68, -38.13], [160.65, -38.04], [160.68, -38.01]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[160.24, -39.45], [160.3, -39.46], [160.28, -39.54]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[160.74, -36.98], [160.82, -37], [160.8, -37.06], [160.73, -37.04]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.04], [-0.03, 0.01], [0.01, 0.04]], "o": [[-0.03, 0.01], [0.01, 0.04], [0.04, -0.01], [-0.01, -0.04]], "v": [[160.67, -37.27], [160.63, -37.2], [160.7, -37.15], [160.74, -37.23]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.03], [-0.03, 0.01], [0.01, 0.03]], "o": [[-0.03, 0.01], [0.01, 0.03], [0.02, 0], [-0.01, -0.03]], "v": [[161.18, -34.79], [161.15, -34.73], [161.21, -34.7], [161.23, -34.76]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, 0.03], [0.01, 0.02], [0.04, -0.01]], "o": [[0.01, 0], [0.01, -0.03], [-0.01, -0.04], [0, 0]], "v": [[160.43, -37.03], [160.46, -37.08], [160.46, -37.16], [160.39, -37.2]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [0.04, -0.07], [-0.01, -0.05], [-0.09, 0.05], [0.02, 0.07], [0, 0]], "o": [[-0.05, 0.01], [-0.04, 0.07], [0.05, -0.01], [0.09, -0.04], [0, 0], [-0.04, -0.02]], "v": [[160.74, -36.54], [160.59, -36.42], [160.55, -36.23], [160.76, -36.32], [160.87, -36.49], [160.86, -36.52]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.03, -0.01], [0, -0.01], [-0.02, 0.01]], "o": [[0.01, -0.01], [-0.03, 0.01], [0.01, 0.02], [0.02, 0]], "v": [[160.77, -35.45], [160.74, -35.46], [160.7, -35.44], [160.74, -35.41]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[160.03, -37.61], [160.08, -37.62], [160.05, -37.73], [160, -37.72]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.05, -0.01], [-0.03, -0.05], [-0.06, 0.02], [-0.03, 0.01], [0.01, 0.04], [0.07, 0]], "o": [[0.01, 0.05], [0.03, 0.05], [0.02, 0], [0.03, -0.01], [-0.02, -0.07], [-0.07, 0]], "v": [[159.85, -38.17], [159.92, -38.01], [160.06, -37.97], [160.13, -37.99], [160.16, -38.08], [160.03, -38.19]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.04], [0, 0], [0, 0], [-0.03, 0.05]], "o": [[-0.06, 0.02], [0, 0], [0, 0], [0.04, -0.01], [0, 0]], "v": [[160.41, -35.86], [160.32, -35.77], [160.33, -35.77], [160.37, -35.78], [160.47, -35.88]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, 0.05], [0, 0]], "o": [[0.05, -0.01], [0, 0], [0, 0]], "v": [[160.03, -35.91], [160.13, -36], [160.02, -35.98]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.03], [-0.02, 0.02], [0, 0.02]], "o": [[-0.02, 0], [0.01, 0.03], [0.02, 0], [0, -0.02]], "v": [[159.55, -36.36], [159.53, -36.33], [159.56, -36.31], [159.59, -36.34]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, -0.01], [-0.01, -0.04], [-0.04, 0.01], [0.01, 0.04]], "o": [[-0.04, 0.01], [0.01, 0.04], [0.03, -0.01], [-0.01, -0.04]], "v": [[159.68, -35.84], [159.64, -35.77], [159.71, -35.73], [159.75, -35.8]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.07, -0.03], [0.03, 0.08], [0.02, 0.02], [0.04, -0.01], [0.04, 0], [0.04, 0.01], [0, 0.06], [0.05, -0.06], [0.04, -0.05], [0, 0], [0.03, -0.05], [0.01, -0.06], [0, -0.04], [0.01, 0], [0.02, 0.09], [-0.02, 0.06], [0.02, 0.08], [0.14, -0.04], [0.01, -0.01], [0.02, -0.04], [0.01, -0.01], [0.1, -0.02], [0.02, -0.02], [0.02, -0.04], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0.02, 0], [0.01, 0], [0.05, -0.04], [0.07, -0.02], [0.02, 0.02], [0.06, -0.02], [0, 0], [0.03, -0.07], [0.01, -0.02], [0.03, 0.04], [0.01, 0.02], [-0.01, 0.07], [0.01, 0.02], [0, 0], [0.06, -0.02], [-0.01, -0.05], [-0.01, -0.04], [0.01, 0.1], [0.03, 0.02], [0.03, 0], [0.04, 0], [0.03, 0.01], [0.01, 0.04], [0, 0.04], [0.01, 0.04], [0, 0], [0.03, 0], [0.01, 0], [0.02, -0.03], [0, -0.03], [0.01, -0.03], [0.06, -0.02], [0, 0], [0.03, 0.03], [0, 0.01], [-0.03, 0.05], [0, 0], [0.02, 0.04], [0.01, 0.02], [0.03, -0.02], [0.02, 0], [0.04, 0.04], [0.01, 0], [0, 0], [0, 0], [0.01, -0.04], [-0.02, -0.02], [-0.03, 0], [-0.04, 0.01], [-0.02, 0], [-0.01, -0.05], [0, 0], [0.05, 0.05], [0.05, 0.02], [0.04, 0.02], [0.01, 0.05], [0, 0], [-0.01, 0.01], [-0.01, 0], [0, 0], [0.05, 0.01], [0.01, 0.05], [0, 0.02], [0, 0.01], [-0.01, 0.03], [0, 0.01], [0.06, 0.01], [0.01, 0.02], [0, 0.01], [0, 0], [-0.05, 0.04], [-0.06, 0.03], [-0.06, 0.03], [-0.01, 0.06], [0.05, -0.01], [0, -0.01], [0.01, 0], [0, 0], [0.04, -0.05], [0.01, 0], [0, 0], [0, 0], [0, 0], [0.03, -0.02], [0, -0.01], [0.01, 0.04], [0, 0.01], [-0.02, 0.08], [-0.04, 0.08], [-0.03, 0.06], [0.01, 0.03], [0.06, 0.05], [0.02, 0.07], [-0.05, 0.09], [0.01, 0.12], [0, 0], [-0.02, 0.01], [-0.02, 0.02], [-0.01, 0.04], [0.02, 0.08], [0, 0], [0, 0], [-0.04, 0.05], [-0.02, 0.01], [-0.05, -0.01], [-0.05, -0.01], [-0.01, 0], [-0.01, 0.01], [0, 0], [0, 0.01], [0.1, 0.05], [0.07, -0.02], [0, 0], [-0.03, 0], [-0.01, -0.01], [0, 0], [0, 0], [-0.01, 0.04], [0, 0.01], [0, 0], [0, 0], [-0.04, -0.06], [-0.08, -0.02], [-0.02, 0.02], [-0.01, 0], [0, 0], [0.12, 0.04], [0.01, 0.02], [0, 0], [-0.03, 0.07], [-0.13, 0.04], [0, 0], [-0.02, -0.02], [0, -0.01], [-0.02, 0.08], [-0.02, 0.09], [-0.03, 0.07], [-0.03, 0.01], [-0.02, -0.09], [-0.02, -0.1], [-0.02, -0.09], [0, -0.01], [0.05, 0.01], [0.01, 0], [0.01, -0.05], [0.01, -0.01], [-0.02, 0], [-0.01, -0.01], [0, 0], [0.01, -0.02], [0.01, 0], [-0.04, -0.01], [0.04, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.04, -0.02], [0, 0.01], [0.04, 0.03], [0.05, 0.04], [0.04, 0.03], [0.01, 0], [-0.04, -0.01], [-0.03, -0.01], [-0.02, 0.03], [0.03, 0.12], [0.04, 0.05], [0.03, 0.11], [-0.07, -0.01], [0, 0.01], [-0.03, 0], [-0.01, -0.01], [0, 0], [0, 0], [0, 0.03], [-0.01, 0], [-0.06, 0.09], [-0.04, 0.02], [-0.04, 0.03], [-0.02, 0.02], [-0.01, 0], [-0.03, 0], [-0.04, 0.01], [-0.09, 0.01], [0.01, -0.03], [0.02, -0.04], [0.02, -0.03], [-0.01, 0], [-0.05, 0.06], [-0.02, 0.06], [-0.03, 0.05], [-0.05, 0.01], [-0.04, -0.03], [-0.05, -0.04], [-0.05, -0.02], [-0.03, 0.01], [-0.01, 0.01], [0, 0.05], [0.01, 0.02], [0.02, 0], [0.03, 0], [0.03, 0.01], [0.01, 0.05], [-0.05, 0.01], [-0.05, 0], [-0.02, 0], [0.06, 0.01], [0, 0], [-0.02, 0.03], [0, 0.03], [0, 0.04], [0, 0.01], [0, 0], [-0.08, 0.01], [-0.02, 0], [-0.04, -0.09], [-0.02, -0.02], [-0.02, -0.01], [-0.01, -0.01], [-0.01, 0.03], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, -0.02], [0.03, 0.05], [-0.08, 0.02], [-0.01, 0], [0, 0], [-0.04, -0.05], [-0.02, -0.04], [0, 0], [0.03, -0.06], [0.03, -0.04], [-0.02, -0.01], [-0.02, -0.03], [-0.02, -0.03], [-0.02, 0.01], [-0.03, 0.03], [0, 0.01], [-0.02, -0.01], [-0.04, -0.01], [-0.04, -0.01], [-0.02, -0.02], [-0.04, -0.07], [-0.01, -0.02], [0, 0], [-0.03, 0], [-0.03, 0], [-0.03, 0], [-0.03, -0.03], [0.01, -0.02], [0.01, -0.01], [0, 0], [0, 0], [0.13, 0.03], [0.04, 0.01], [-0.01, -0.05], [-0.01, -0.03], [0.09, 0.02], [0.07, 0.04], [0.01, 0.04], [0.01, 0.02], [0.01, 0.03], [0, 0.01], [0.06, 0.02], [0.03, 0.02], [0, 0], [0.04, -0.06], [0.05, -0.07], [0, 0], [0.01, 0.01], [0.04, 0.03], [0.01, 0], [0.08, -0.02], [0.02, 0], [0.04, 0.02], [0.01, 0], [0, 0], [0.02, -0.04], [0.02, -0.06], [0.02, -0.06], [0.05, -0.02], [0.07, -0.03], [0.09, -0.03], [0.07, -0.03], [0.02, 0], [0.06, -0.04], [0.01, 0], [0, 0], [0.02, -0.02], [0.05, -0.01], [0.02, 0.06], [0.08, -0.02], [0.02, -0.03], [0.05, -0.01], [0.03, 0.03], [0.05, -0.01], [-0.04, -0.04], [-0.07, -0.07], [-0.01, -0.01], [-0.01, -0.01], [0, -0.01], [0.03, -0.04], [0.05, -0.03], [0.04, -0.02], [0.03, -0.01], [0, 0], [-0.02, -0.02], [-0.06, 0.03], [0.03, -0.07], [-0.01, -0.12], [0, 0], [0, -0.08], [0, -0.09], [0.01, -0.12], [0, 0], [0, -0.02], [0, -0.01], [-0.03, -0.05], [0, -0.01], [0, 0], [0, 0], [0, -0.06], [0, -0.01], [-0.04, -0.04], [-0.01, 0], [0, 0], [0.01, 0], [-0.04, 0.05], [-0.08, 0.02], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.01], [-0.03, -0.11], [0, 0], [-0.09, -0.06], [-0.04, -0.03], [-0.04, -0.05], [-0.01, 0], [-0.08, 0.03], [-0.02, 0.01], [-0.06, -0.03], [-0.01, 0], [-0.07, 0.02], [-0.01, 0], [-0.09, 0.01], [-0.01, 0], [-0.11, 0.03], [-0.12, 0.03], [-0.11, 0.03], [-0.04, 0.01], [0, 0], [0.02, 0.08], [-0.07, 0.07], [-0.16, 0.07], [-0.16, 0.1], [-0.06, 0.05], [-0.01, 0], [0, 0], [0, 0.01], [-0.01, 0], [-0.01, 0], [0, 0], [0, 0], [-0.02, 0], [-0.02, 0.01], [0, 0], [-0.1, -0.02], [-0.02, 0], [-0.05, -0.08], [0, -0.02], [0, 0], [0, -0.01], [0.01, 0], [0.02, -0.03], [0.01, -0.01], [-0.01, -0.03], [0.01, -0.01], [0.05, -0.05], [0.05, -0.01], [0, 0], [-0.01, 0.01], [-0.03, 0.06], [-0.02, 0.02], [0, 0], [0.09, 0.02], [0, -0.05], [0, -0.03], [0.12, -0.04], [0.03, -0.02], [0.04, -0.03], [0.01, 0], [0, 0], [-0.02, -0.04], [-0.06, -0.01], [0.05, -0.03], [0.02, -0.03], [-0.03, -0.06], [0.01, -0.01], [0.07, -0.04], [-0.03, -0.11], [0, 0], [0.04, 0.07], [0.01, 0.02], [0.04, 0.02], [0.02, -0.01], [0.01, 0], [0, 0], [0, -0.06], [0.01, -0.02], [0, 0], [0.06, -0.05], [0.02, 0], [0.04, 0], [0.02, 0], [0.08, -0.05], [0.02, 0], [0.01, 0], [0, 0], [0.06, 0.01]], "o": [[-0.02, -0.02], [-0.03, -0.08], [-0.02, -0.04], [-0.04, 0.01], [-0.04, 0], [-0.04, -0.01], [-0.04, 0.07], [-0.05, 0.06], [0, 0], [-0.05, 0.01], [-0.03, 0.05], [-0.01, 0.06], [0, 0.05], [-0.06, 0.02], [-0.02, -0.07], [0.02, -0.06], [-0.04, -0.17], [-0.01, 0], [-0.01, 0], [-0.02, 0.04], [-0.02, 0.01], [-0.1, 0.02], [-0.01, 0], [-0.02, 0.04], [0, 0], [0, 0], [0, 0], [-0.02, 0.01], [-0.02, 0], [-0.07, 0.02], [-0.04, 0.04], [-0.05, 0.01], [-0.02, -0.02], [0, 0], [-0.02, 0.02], [-0.03, 0.07], [-0.01, -0.01], [-0.03, -0.04], [0.01, -0.01], [0.02, -0.07], [0, 0], [-0.02, -0.08], [-0.05, 0.01], [0.01, 0.05], [-0.07, 0.02], [-0.01, -0.1], [-0.01, -0.01], [-0.03, 0], [-0.04, 0.01], [-0.03, -0.01], [-0.01, -0.05], [0.01, -0.04], [0, 0], [0, -0.01], [-0.03, 0], [-0.07, 0.02], [-0.01, 0.03], [0, 0.03], [-0.01, 0.03], [0, 0], [0, -0.01], [-0.03, -0.03], [-0.01, -0.04], [0, 0], [0.01, -0.02], [-0.02, -0.04], [-0.02, 0.01], [-0.03, 0.02], [0, -0.01], [-0.04, -0.03], [0, 0], [0, 0], [-0.01, 0.01], [-0.01, 0.04], [0.02, 0.05], [0.03, 0], [0.04, -0.01], [0.02, 0], [0, 0], [-0.03, -0.11], [-0.05, -0.05], [-0.05, -0.02], [-0.04, -0.02], [0, 0], [0.01, 0], [0.02, -0.03], [0, 0], [-0.01, -0.05], [-0.05, -0.01], [0, -0.02], [0, -0.02], [0, -0.01], [0.01, -0.03], [0, -0.02], [-0.06, -0.01], [-0.01, 0], [0, 0], [-0.02, -0.07], [0.05, -0.04], [0.07, -0.03], [0.06, -0.03], [-0.03, -0.05], [-0.01, 0], [0, 0.01], [0, 0], [-0.01, 0], [-0.04, 0.05], [0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.03, 0.02], [-0.01, -0.01], [-0.01, -0.04], [-0.02, -0.06], [0.02, -0.08], [0.04, -0.08], [0.03, -0.06], [-0.02, -0.08], [-0.06, -0.05], [-0.02, -0.08], [0.05, -0.08], [0, 0], [0, -0.03], [0.02, 0], [0.02, -0.02], [0.01, -0.04], [0, 0], [0, 0], [0.02, -0.03], [0.04, -0.05], [0.09, 0], [0.05, 0.01], [0.05, 0.01], [0.01, 0], [0, 0], [0.01, -0.01], [-0.05, -0.02], [-0.1, -0.05], [0, 0], [0.02, 0.01], [0.03, 0], [0, 0], [0, 0], [0, -0.01], [0.01, -0.04], [0, 0], [0, 0], [0.02, 0.06], [0.04, 0.06], [0.01, 0], [0.02, -0.02], [0, 0], [-0.03, 0], [-0.12, -0.04], [0, 0], [0.03, -0.12], [0.03, -0.07], [0, 0], [0.02, 0.01], [0.02, 0.02], [0.02, -0.02], [0.02, -0.08], [0.02, -0.09], [0.03, -0.07], [0, 0.01], [0.02, 0.09], [0.02, 0.1], [0.02, 0.09], [0.01, 0.02], [-0.05, -0.01], [-0.02, 0.03], [-0.01, 0.05], [-0.02, 0.03], [0, 0.01], [0, 0], [0, 0.01], [-0.01, 0.03], [-0.01, 0.03], [0.04, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.05, 0.01], [0.04, 0.02], [-0.01, -0.01], [-0.04, -0.03], [-0.05, -0.04], [-0.04, -0.03], [0, -0.04], [0.04, 0.01], [0.03, 0.01], [0.02, -0.03], [-0.02, -0.07], [-0.04, -0.04], [0, -0.01], [0.07, 0.01], [0.01, 0.01], [0.03, 0], [0, 0], [0, 0], [0.02, -0.01], [0, -0.03], [0.01, -0.01], [0.06, -0.09], [0.05, -0.02], [0.04, -0.02], [0.02, -0.02], [0.01, 0], [0.07, 0.01], [0.07, 0.01], [0.01, 0], [-0.01, 0.03], [-0.02, 0.04], [-0.01, 0.03], [0.07, -0.02], [0.05, -0.06], [0.02, -0.06], [0.03, -0.05], [0.03, -0.01], [0.05, 0.03], [0.05, 0.04], [0.05, 0.03], [0.01, 0], [0.01, -0.02], [0.01, -0.05], [0, -0.02], [-0.02, 0], [-0.03, 0], [-0.02, -0.01], [-0.01, -0.03], [0.05, -0.01], [0.05, 0], [0.02, 0.01], [0, 0], [0.02, 0.02], [0.02, -0.03], [0.01, -0.02], [0, -0.04], [0, 0], [0.02, 0.01], [0.08, -0.01], [0.01, 0.02], [0.03, 0.09], [0, 0.01], [0.02, 0.01], [0.01, -0.01], [0.01, -0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.05], [-0.07, 0.02], [0, -0.05], [0.08, -0.01], [0, 0], [0.01, 0.01], [0.04, 0.05], [0, 0], [0.01, 0.05], [-0.02, 0.06], [0.03, -0.04], [0.03, 0.01], [0.02, 0.03], [0.02, 0.03], [0.01, 0], [0.03, -0.02], [-0.02, 0.03], [0.02, 0.01], [0.04, 0.01], [0.04, 0.01], [0.01, 0.02], [0.04, 0.07], [0, 0], [0.01, 0.05], [0.03, 0], [0.03, 0], [0.03, 0], [0, 0.01], [-0.01, 0.02], [0, 0], [0, 0], [-0.06, -0.01], [-0.13, -0.03], [-0.05, 0.02], [0.01, 0.05], [-0.07, 0.02], [-0.09, -0.02], [0.05, -0.03], [-0.01, -0.04], [0, -0.02], [-0.01, -0.02], [0, -0.01], [-0.06, -0.02], [0, 0], [-0.08, 0.02], [-0.04, 0.07], [0, 0], [-0.01, 0], [-0.01, -0.01], [-0.04, -0.03], [-0.02, 0], [-0.08, 0.03], [-0.01, -0.01], [-0.04, -0.02], [0, 0], [-0.03, 0.01], [-0.02, 0.05], [-0.02, 0.06], [-0.02, 0.06], [-0.02, 0.01], [-0.07, 0.04], [-0.09, 0.03], [-0.07, 0.03], [-0.01, 0], [-0.06, 0.04], [0, 0], [-0.01, 0.06], [-0.02, 0.02], [-0.08, 0.02], [-0.02, -0.06], [-0.05, 0.01], [-0.02, 0.03], [-0.05, 0.01], [-0.03, -0.03], [0.03, 0.1], [0.04, 0.05], [0.01, 0.01], [0.01, 0.01], [0.02, 0.02], [0.01, 0.05], [-0.03, 0.04], [-0.05, 0.03], [-0.04, 0.02], [0, 0], [0.01, 0.03], [0.02, 0.02], [-0.02, 0.13], [-0.03, 0.07], [0, 0], [0.01, 0.03], [0, 0.08], [0.01, 0.09], [0, 0], [-0.01, -0.01], [-0.01, 0.02], [0, 0.01], [0.03, 0.05], [0, 0], [0, 0], [0, 0.01], [0, 0.06], [0.01, 0.02], [0.05, 0.05], [0, 0], [0, 0.01], [0.07, -0.02], [0.05, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, -0.03], [-0.01, 0.01], [0, 0], [0.02, -0.01], [0.09, 0.06], [0.01, 0.01], [0.04, 0.05], [0.03, 0.01], [0.08, -0.02], [0.01, 0], [0.06, 0.03], [0.01, 0.01], [0.07, -0.02], [0.02, 0.01], [0.09, 0], [0.04, -0.01], [0.12, -0.03], [0.13, -0.03], [0.11, -0.03], [0, 0], [0.08, -0.04], [-0.02, -0.08], [0.16, -0.13], [0.16, -0.06], [0.02, -0.02], [0.06, -0.05], [0, 0], [0.01, 0], [0, -0.01], [0, 0.01], [0, 0], [0, 0], [0.01, 0.01], [0.02, 0.01], [0, 0], [0.04, 0], [0.1, 0.02], [0.03, 0.02], [0.05, 0.08], [0, 0], [-0.01, 0.01], [0, 0.01], [0, 0.01], [-0.02, 0.03], [0, 0.01], [0.01, 0.03], [-0.01, 0.06], [-0.05, 0.05], [0, 0], [0, -0.01], [0.01, -0.01], [0.03, -0.06], [0, 0], [-0.02, -0.06], [-0.04, 0.03], [0, 0.05], [-0.03, 0.02], [-0.12, 0.04], [-0.02, 0.01], [-0.04, 0.03], [0, 0], [0.01, 0.05], [0.02, 0.04], [-0.02, 0], [-0.05, 0.03], [-0.01, 0], [0.03, 0.07], [-0.01, 0.06], [-0.07, 0.04], [0, 0], [-0.01, -0.03], [-0.04, -0.07], [-0.05, 0.01], [-0.01, 0], [-0.01, 0.01], [0, 0], [-0.01, 0.02], [0, 0.06], [0, 0], [-0.01, 0.01], [-0.06, 0.05], [-0.01, 0.01], [-0.04, 0.01], [-0.01, 0.01], [-0.08, 0.05], [-0.01, 0.01], [0, 0], [-0.07, 0.02], [-0.06, -0.01]], "v": [[165.3, -33.93], [165.23, -34.08], [165.16, -34.22], [165.07, -34.26], [164.93, -34.26], [164.81, -34.27], [164.76, -34.36], [164.62, -34.17], [164.49, -33.99], [164.43, -34.19], [164.32, -34.1], [164.27, -33.94], [164.25, -33.78], [164.23, -33.71], [164.1, -33.83], [164.1, -34.03], [164.11, -34.24], [163.84, -34.44], [163.81, -34.42], [163.77, -34.36], [163.73, -34.28], [163.55, -34.24], [163.37, -34.19], [163.33, -34.12], [163.29, -34.04], [163.1, -33.87], [163, -33.96], [162.95, -33.95], [162.89, -33.94], [162.85, -33.94], [162.67, -33.85], [162.5, -33.76], [162.39, -33.77], [162.26, -33.77], [162.2, -33.76], [162.13, -33.63], [162.07, -33.49], [162.01, -33.56], [161.96, -33.65], [162, -33.77], [162.01, -33.91], [162.01, -33.96], [161.89, -34.06], [161.82, -33.97], [161.85, -33.83], [161.73, -33.95], [161.67, -34.12], [161.6, -34.13], [161.5, -34.12], [161.4, -34.12], [161.34, -34.2], [161.33, -34.34], [161.32, -34.46], [161.3, -34.54], [161.25, -34.56], [161.18, -34.56], [161.05, -34.48], [161.02, -34.39], [161.01, -34.29], [160.9, -34.22], [160.87, -34.35], [160.82, -34.4], [160.77, -34.46], [160.8, -34.6], [160.84, -34.65], [160.81, -34.76], [160.77, -34.85], [160.7, -34.81], [160.63, -34.77], [160.56, -34.83], [160.48, -34.88], [160.42, -34.86], [160.35, -34.85], [160.33, -34.78], [160.34, -34.69], [160.43, -34.62], [160.54, -34.63], [160.62, -34.64], [160.68, -34.56], [160.29, -34.46], [160.17, -34.71], [160.03, -34.81], [159.9, -34.87], [159.82, -34.98], [159.81, -35.01], [159.84, -35.03], [159.88, -35.08], [159.87, -35.12], [159.77, -35.21], [159.67, -35.3], [159.67, -35.36], [159.66, -35.4], [159.67, -35.46], [159.68, -35.52], [159.59, -35.57], [159.49, -35.62], [159.47, -35.64], [159.46, -35.67], [159.5, -35.84], [159.67, -35.94], [159.86, -36.03], [159.97, -36.16], [159.84, -36.21], [159.82, -36.2], [159.8, -36.18], [159.78, -36.18], [159.71, -36.1], [159.64, -36.03], [159.59, -36.02], [159.56, -36.01], [159.5, -36.04], [159.44, -36.01], [159.4, -35.97], [159.37, -36.03], [159.35, -36.1], [159.36, -36.32], [159.46, -36.55], [159.57, -36.76], [159.6, -36.9], [159.48, -37.09], [159.37, -37.27], [159.41, -37.53], [159.47, -37.83], [159.41, -38.07], [159.45, -38.13], [159.51, -38.16], [159.56, -38.24], [159.54, -38.42], [159.65, -38.44], [159.73, -38.15], [159.8, -38.27], [159.89, -38.36], [160.1, -38.34], [160.25, -38.32], [160.34, -38.3], [160.36, -38.33], [160.37, -38.35], [160.38, -38.38], [160.16, -38.48], [159.91, -38.53], [159.86, -38.73], [159.94, -38.72], [160, -38.7], [160.03, -38.71], [160.06, -38.72], [160.07, -38.79], [160.08, -38.85], [160.11, -38.86], [160.14, -38.87], [160.23, -38.69], [160.42, -38.56], [160.47, -38.59], [160.52, -38.62], [160.39, -39.13], [160.16, -39.2], [159.97, -39.29], [159.96, -39.32], [160.04, -39.61], [160.29, -39.77], [160.38, -39.8], [160.45, -39.76], [160.48, -39.7], [160.53, -39.84], [160.59, -40.09], [160.66, -40.33], [160.75, -40.44], [160.78, -40.29], [160.84, -40.01], [160.9, -39.72], [160.93, -39.57], [160.85, -39.56], [160.76, -39.58], [160.72, -39.47], [160.69, -39.38], [160.7, -39.33], [160.73, -39.3], [161.06, -39.39], [161.04, -39.34], [161.01, -39.29], [161.04, -39.24], [161.03, -39.22], [161.25, -39.22], [161.23, -39.31], [161.13, -39.69], [161.33, -39.74], [161.51, -39.69], [161.64, -39.65], [161.69, -39.63], [161.61, -39.69], [161.48, -39.79], [161.34, -39.89], [161.27, -39.93], [161.32, -39.97], [161.42, -39.94], [161.49, -39.97], [161.47, -40.18], [161.38, -40.35], [161.28, -40.58], [161.39, -40.58], [161.5, -40.58], [161.56, -40.57], [161.62, -40.55], [161.66, -40.56], [161.67, -40.56], [161.69, -40.63], [161.7, -40.69], [161.81, -40.84], [161.96, -41], [162.09, -41.08], [162.18, -41.14], [162.23, -41.17], [162.3, -41.17], [162.46, -41.18], [162.69, -41.19], [162.69, -41.15], [162.64, -41.04], [162.59, -40.95], [162.58, -40.91], [162.76, -41.02], [162.86, -41.2], [162.94, -41.37], [163.05, -41.47], [163.17, -41.44], [163.32, -41.34], [163.48, -41.25], [163.6, -41.22], [163.63, -41.25], [163.64, -41.35], [163.64, -41.45], [163.6, -41.48], [163.52, -41.48], [163.45, -41.49], [163.39, -41.58], [163.45, -41.65], [163.59, -41.67], [163.7, -41.66], [163.65, -41.67], [164.06, -41.42], [164.12, -41.44], [164.16, -41.52], [164.16, -41.62], [164.16, -41.7], [164.24, -41.72], [164.39, -41.73], [164.55, -41.73], [164.61, -41.56], [164.68, -41.4], [164.72, -41.38], [164.76, -41.35], [164.79, -41.42], [164.8, -41.48], [164.65, -42.07], [164.71, -42.02], [165.1, -41.26], [165.38, -41.63], [165.38, -41.66], [165.25, -41.71], [165.09, -41.76], [165.21, -41.86], [165.34, -41.89], [165.48, -41.92], [165.56, -41.83], [165.66, -41.7], [165.66, -41.68], [165.64, -41.52], [165.56, -41.37], [165.64, -41.42], [165.72, -41.36], [165.78, -41.28], [165.84, -41.25], [165.9, -41.29], [165.95, -41.34], [165.95, -41.28], [166.05, -41.25], [166.16, -41.22], [166.24, -41.18], [166.3, -41.04], [166.36, -40.9], [166.48, -40.43], [166.54, -40.35], [166.64, -40.34], [166.74, -40.33], [166.83, -40.29], [166.82, -40.24], [166.79, -40.19], [166.78, -40.19], [166.75, -40.18], [166.47, -40.23], [166.22, -40.3], [166.15, -40.19], [166.17, -40.07], [165.93, -40.07], [165.69, -40.16], [165.74, -40.27], [165.72, -40.37], [165.69, -40.43], [165.67, -40.49], [165.57, -40.54], [165.44, -40.59], [165.43, -40.59], [165.24, -40.46], [165.11, -40.26], [165.08, -40.24], [165.05, -40.25], [164.97, -40.3], [164.91, -40.34], [164.76, -40.31], [164.61, -40.27], [164.53, -40.31], [164.46, -40.34], [164.44, -40.34], [164.36, -40.26], [164.29, -40.11], [164.22, -39.94], [164.11, -39.82], [163.97, -39.76], [163.74, -39.66], [163.51, -39.57], [163.37, -39.52], [163.27, -39.46], [163.17, -39.4], [162.76, -39.29], [162.72, -39.17], [162.61, -39.12], [162.46, -39.18], [162.31, -39.24], [162.21, -39.18], [162.1, -39.12], [161.98, -39.14], [161.86, -39.16], [161.96, -38.94], [162.13, -38.76], [162.16, -38.74], [162.19, -38.72], [162.22, -38.67], [162.2, -38.53], [162.08, -38.43], [161.95, -38.35], [161.83, -38.31], [161.83, -38.26], [161.88, -38.17], [162, -38.18], [161.92, -37.88], [161.88, -37.59], [161.97, -37.24], [161.98, -37.07], [161.99, -36.81], [161.98, -36.5], [161.5, -36.69], [161.47, -36.66], [161.47, -36.61], [161.51, -36.53], [161.56, -36.44], [161.57, -36.41], [161.58, -36.38], [161.58, -36.27], [161.58, -36.16], [161.66, -36.06], [161.74, -36], [161.75, -35.97], [161.74, -35.95], [161.91, -36.05], [162.1, -36.15], [162.2, -36.17], [162.16, -35.86], [162.27, -35.89], [162.35, -35.58], [162.51, -35.6], [162.54, -35.41], [162.62, -35.43], [162.79, -35.35], [162.99, -35.21], [163.07, -35.13], [163.15, -35.06], [163.3, -35.09], [163.44, -35.14], [163.54, -35.1], [163.65, -35.06], [163.77, -35.08], [163.88, -35.12], [164.05, -35.12], [164.19, -35.13], [164.43, -35.19], [164.79, -35.28], [165.15, -35.36], [165.38, -35.42], [165.44, -35.22], [165.53, -35.41], [165.61, -35.65], [166.09, -35.93], [166.57, -36.18], [166.69, -36.27], [166.8, -36.35], [166.83, -36.36], [166.85, -36.37], [166.87, -36.38], [166.89, -36.37], [166.92, -36.38], [166.97, -36.39], [167.02, -36.37], [167.09, -36.37], [167.32, -36.43], [167.54, -36.41], [167.72, -36.37], [167.83, -36.23], [167.91, -36.09], [167.99, -36.11], [167.98, -36.07], [167.97, -36.05], [167.94, -35.98], [167.89, -35.91], [167.91, -35.85], [167.92, -35.79], [167.84, -35.63], [167.68, -35.54], [167.67, -35.56], [167.67, -35.59], [167.73, -35.7], [167.8, -35.82], [167.78, -35.89], [167.61, -36.01], [167.56, -35.89], [167.56, -35.76], [167.34, -35.67], [167.11, -35.58], [167.02, -35.52], [166.95, -35.47], [166.98, -35.36], [167.03, -35.23], [167.15, -35.16], [167.04, -35.12], [166.93, -35.04], [166.96, -34.93], [166.99, -34.82], [166.88, -34.67], [166.82, -34.44], [166.8, -34.44], [166.74, -34.59], [166.66, -34.73], [166.52, -34.75], [166.49, -34.73], [166.45, -34.71], [166.47, -34.68], [166.47, -34.56], [166.47, -34.43], [166.42, -34.42], [166.33, -34.32], [166.21, -34.23], [166.13, -34.21], [166.03, -34.19], [165.91, -34.09], [165.77, -34], [165.74, -33.98], [165.71, -33.97], [165.5, -33.96]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[158.77, -32.64], [158.75, -32.72], [158.68, -32.7]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -0.02], [-0.04, -0.04], [-0.02, 0.01], [0, 0], [0.02, -0.04]], "o": [[0, 0.01], [0.04, 0.04], [0, 0], [-0.01, 0.03], [-0.02, 0.04]], "v": [[157.61, -35.36], [157.67, -35.28], [157.76, -35.23], [157.68, -35.56], [157.63, -35.46]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0], [-0.06, 0.05], [0.01, 0.05], [0.07, 0.03], [0.14, -0.01], [-0.02, -0.04], [-0.01, -0.05], [-0.01, -0.04]], "o": [[0.13, -0.02], [0.06, -0.05], [-0.01, -0.05], [-0.07, -0.03], [-0.02, 0.01], [0.02, 0.04], [0.01, 0.05], [0.01, 0.04]], "v": [[156.95, -37.24], [157.23, -37.34], [157.31, -37.49], [157.19, -37.6], [156.88, -37.62], [156.88, -37.55], [156.92, -37.42], [156.95, -37.3]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0], [0.05, -0.02], [0.04, -0.02], [0.03, -0.02], [-0.02, 0.01], [-0.04, 0.02], [-0.05, 0.02], [-0.03, 0.02]], "o": [[-0.02, 0.01], [-0.05, 0.03], [-0.04, 0.02], [-0.03, 0.02], [0.01, 0], [0.04, -0.02], [0.05, -0.02], [0.03, -0.02]], "v": [[155.63, -36.72], [155.52, -36.67], [155.37, -36.6], [155.26, -36.54], [155.25, -36.52], [155.33, -36.56], [155.47, -36.62], [155.6, -36.69]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.05], [-0.04, 0.01], [0.01, 0.05]], "o": [[-0.04, 0.01], [0.01, 0.05], [0.04, -0.01], [-0.01, -0.05]], "v": [[154.91, -36.5], [154.86, -36.4], [154.95, -36.34], [154.99, -36.43]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.05, -0.01], [-0.02, -0.04], [-0.03, 0.01]], "o": [[0, -0.04], [-0.05, 0.01], [0.02, 0.02], [0.03, -0.01]], "v": [[154.1, -35.28], [154.03, -35.33], [153.99, -35.25], [154.07, -35.23]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.05, -0.04], [-0.02, -0.04], [-0.04, -0.05], [-0.05, -0.03], [-0.02, 0.01], [0.01, 0.07], [0.04, 0.04], [0.07, 0.01]], "o": [[-0.02, 0.01], [0.02, 0.04], [0.04, 0.05], [0.05, 0.03], [0.07, -0.07], [-0.01, -0.06], [-0.04, -0.04], [-0.07, 0]], "v": [[153.53, -36.81], [153.53, -36.74], [153.62, -36.61], [153.75, -36.48], [153.85, -36.44], [153.94, -36.65], [153.86, -36.8], [153.7, -36.87]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, -0.01], [-0.01, -0.04], [-0.01, 0.01], [0, 0.02]], "o": [[-0.02, -0.02], [0.01, 0.04], [0.03, -0.02], [0, -0.01]], "v": [[153.64, -35.52], [153.63, -35.5], [153.66, -35.46], [153.69, -35.51]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.05, -0.01], [-0.01, -0.06], [-0.03, 0.01], [0.02, 0.06]], "o": [[-0.03, 0.01], [0.02, 0.06], [0.05, -0.01], [-0.01, -0.06]], "v": [[153.26, -36.36], [153.24, -36.26], [153.3, -36.19], [153.36, -36.29]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.04, 0.01], [-0.01, -0.05], [0.05, -0.01], [0.01, 0.05]], "o": [[0.05, -0.01], [0.01, 0.05], [-0.04, 0.01], [-0.01, -0.05]], "v": [[152.26, -34.47], [152.36, -34.41], [152.3, -34.31], [152.22, -34.38]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, 0.05], [-0.01, -0.04], [-0.02, 0.01]], "o": [[-0.02, 0], [0.01, 0.04], [0.02, -0.03]], "v": [[151.61, -34.75], [151.59, -34.7], [151.64, -34.64]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.01, -0.03], [0.02, -0.09], [0, -0.03], [-0.08, 0], [-0.14, 0], [0, 0], [0.03, -0.03]], "o": [[0, 0.01], [-0.01, 0.05], [0.14, 0], [0.08, 0], [0, 0], [-0.03, 0.01], [-0.03, 0.03]], "v": [[151.86, -33.89], [151.84, -33.73], [151.82, -33.61], [152.15, -33.62], [152.48, -33.63], [151.99, -34.03], [151.9, -33.98]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[148.26, -36.46], [148.35, -36.48], [148.32, -36.59], [148.23, -36.57]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, -0.01], [-0.01, -0.03], [-0.03, -0.03], [-0.03, -0.02], [-0.03, 0.01], [0.08, 0.08]], "o": [[-0.03, 0.01], [0.01, 0.03], [0.03, 0.03], [0.03, 0.02], [0.03, -0.01], [-0.08, -0.08]], "v": [[148.22, -36.33], [148.2, -36.27], [148.26, -36.19], [148.35, -36.12], [148.45, -36.09], [148.38, -36.22]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.01, -0.04], [-0.02, -0.04], [-0.07, -0.04], [-0.02, 0.03], [0, 0.01], [0, 0], [0, 0], [0.05, 0.09], [0.01, 0.03], [-0.01, -0.05]], "o": [[0.02, 0.06], [0.02, 0.04], [0, -0.01], [0.02, -0.03], [0, 0], [0, 0], [0, -0.02], [-0.05, -0.09], [-0.06, 0.04], [0.01, 0.05]], "v": [[147.54, -36.14], [147.6, -35.98], [147.73, -35.86], [147.76, -35.92], [147.78, -35.98], [147.78, -36], [147.76, -36.06], [147.68, -36.23], [147.59, -36.4], [147.51, -36.27]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.08, -0.02], [-0.08, -0.13], [-0.09, -0.03], [-0.08, 0.1], [0, 0.01], [0.02, 0], [0, 0.01], [0.11, 0.07]], "o": [[0.03, 0.11], [0.08, 0.14], [0.01, -0.01], [0.07, -0.1], [-0.03, 0], [-0.05, 0.01], [-0.02, -0.08], [-0.11, -0.07]], "v": [[147.29, -35.29], [147.45, -34.94], [147.7, -34.68], [147.82, -34.85], [147.93, -35.01], [147.85, -35], [147.76, -35], [147.57, -35.22]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[147.66, -32.99], [147.76, -33.02], [147.73, -33.14], [147.63, -33.12]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -0.01], [-0.06, -0.07], [-0.09, 0.02], [0.01, 0.1], [0.01, 0.02], [0.02, 0.06], [0.01, 0.02], [0.06, -0.03], [0.02, -0.02], [0.01, -0.03]], "o": [[0.02, 0.06], [0.06, 0.07], [0.05, -0.01], [-0.01, -0.1], [0, -0.01], [-0.02, -0.05], [-0.03, 0.03], [-0.06, 0.03], [-0.02, 0.01], [-0.01, 0.03]], "v": [[147.13, -30.29], [147.24, -30.09], [147.48, -30.02], [147.54, -30.18], [147.51, -30.35], [147.47, -30.46], [147.44, -30.57], [147.31, -30.48], [147.2, -30.41], [147.14, -30.35]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, 0], [-0.02, -0.07], [-0.01, 0.03], [0.01, 0.04]], "o": [[-0.03, -0.01], [0.02, 0.07], [0.03, -0.03], [-0.01, -0.04]], "v": [[147.07, -29.81], [147.05, -29.72], [147.11, -29.65], [147.13, -29.75]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.01, -0.03], [-0.05, -0.2], [-0.03, 0.01], [0.03, 0.11], [0.05, 0.12], [0.04, 0.09]], "o": [[0.06, 0.23], [0.06, 0.2], [0.03, -0.01], [-0.03, -0.11], [-0.05, -0.12], [-0.04, -0.09]], "v": [[145.62, -34.85], [145.79, -34.21], [145.93, -33.93], [145.92, -34.1], [145.8, -34.45], [145.67, -34.76]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.01, 0.1], [0.01, 0.02], [0.08, 0.04], [0.01, 0.02], [-0.05, 0.06], [0, 0], [0, 0], [0.18, 0.21], [0.1, 0.25], [0.12, 0.33], [0.1, 0.38], [0, 0], [-0.01, 0.13], [-0.05, 0.09], [-0.11, -0.11], [-0.08, 0.04], [-0.06, 0.06], [-0.02, 0.02], [0, 0], [-0.08, -0.03], [-0.12, -0.06], [-0.04, -0.13], [-0.05, -0.15], [-0.07, -0.13], [-0.08, -0.06], [-0.01, 0], [0, 0], [-0.04, 0.02], [0.02, 0.06], [0.12, 0.09], [0.03, 0.11], [-0.03, 0.07], [-0.04, 0.08], [-0.04, 0.06], [-0.02, 0.01], [0, 0], [0, 0], [-0.02, -0.02], [-0.01, 0], [0, 0], [-0.01, 0.05], [0.01, 0.06], [0.02, 0.06], [-0.01, 0.04], [-0.07, 0.08], [-0.04, 0.04], [0, 0], [-0.02, -0.07], [-0.12, 0.03], [-0.07, 0.13], [-0.04, 0.14], [-0.03, 0.12], [-0.05, 0.01], [-0.09, -0.02], [-0.1, -0.02], [-0.09, -0.03], [-0.06, 0.01], [-0.12, 0.04], [-0.02, 0.01], [-0.08, -0.05], [-0.1, -0.05], [-0.08, -0.05], [-0.01, -0.01], [0, 0], [0, 0], [-0.07, -0.05], [-0.04, -0.13], [0.02, -0.04], [-0.04, -0.13], [-0.06, -0.1], [-0.03, -0.11], [0, 0], [0.02, -0.04], [0.01, -0.01], [-0.03, -0.04], [-0.02, 0], [-0.04, 0.04], [0.02, 0.09], [0.04, 0.02], [0.01, 0.05], [-0.06, 0.1], [-0.07, 0.12], [-0.07, 0.11], [-0.01, 0.04], [-0.08, 0.17], [-0.11, 0.17], [-0.11, 0.14], [-0.09, 0.02], [0, 0], [-0.04, -0.02], [-0.02, 0.01], [-0.06, 0.07], [-0.04, 0.08], [-0.03, 0.1], [0.02, 0.07], [-0.1, -0.01], [-0.04, 0], [-0.03, -0.01], [0, 0], [-0.02, 0.03], [0, 0.02], [0, 0], [0, 0], [0, 0], [-0.05, 0.11], [-0.25, 0.13], [-0.1, -0.06], [-0.09, -0.07], [-0.08, -0.05], [-0.06, 0.02], [-0.03, 0.04], [0, 0.01], [0, 0], [-0.06, -0.07], [-0.06, -0.11], [-0.04, -0.1], [-0.01, -0.04], [0.01, -0.29], [-0.07, -0.27], [-0.08, -0.01], [0, 0], [0.02, -0.07], [0.01, -0.02], [0, 0], [-0.01, 0], [-0.01, 0], [-0.05, -0.03], [-0.02, 0.01], [0.01, -0.05], [0, -0.02], [0, 0], [-0.02, -0.08], [-0.03, -0.08], [-0.04, -0.08], [-0.07, -0.11], [-0.07, -0.05], [0, -0.01], [0.01, -0.06], [0.05, -0.01], [-0.01, -0.08], [-0.01, -0.04], [-0.07, -0.06], [-0.07, -0.05], [-0.06, -0.04], [-0.02, -0.08], [0, 0], [0.1, 0.02], [0.03, -0.01], [0.08, -0.06], [0.01, -0.02], [0, 0], [0.2, 0.19], [0.19, 0.32], [0.17, 0.38], [0.15, 0.34], [0.11, 0.22], [0.06, -0.02], [0, 0], [0.09, -0.13], [0.04, -0.06], [0.06, -0.12], [0.01, -0.02], [0, 0], [0.03, 0.02], [0.09, -0.02], [-0.04, -0.09], [-0.02, -0.08], [0.01, -0.09], [0.07, -0.13], [0, 0], [0, 0], [0.04, 0.06], [0.02, 0.03], [0, 0], [0.04, -0.01], [0.03, -0.03], [0, -0.02], [0, 0], [0, 0], [0.01, -0.02], [0.02, 0], [0.05, 0.02], [0.04, 0.01], [0.06, -0.06], [-0.01, -0.05], [-0.01, -0.01], [-0.01, 0], [-0.04, 0], [-0.03, -0.01], [0, 0], [0, 0], [0.13, -0.07], [0.16, -0.05], [0.05, 0.07], [0.07, -0.02], [0.03, -0.15], [-0.02, -0.08], [-0.1, -0.05], [-0.04, -0.13], [0.09, -0.11], [0.15, -0.08], [0.14, -0.09], [0.03, -0.2], [0, 0], [0, 0], [0.06, 0.03], [0.02, 0.01], [0, 0], [0.03, -0.03], [0, -0.02], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0.07], [-0.01, 0.04], [0, 0.02], [0.01, 0.02], [0.06, 0.2], [0.07, 0.2], [0.08, 0.18], [0.09, 0.11], [0.06, 0.06], [0.05, 0.07], [0.04, 0.08], [0.04, 0.15], [0, 0], [-0.02, 0.05], [0, 0.01], [0.07, 0.1], [0.01, 0], [0, 0], [0.01, 0.05], [0.02, 0.06], [0, 0], [0.03, -0.04], [0.02, 0], [0.03, 0.04], [0.04, 0.06], [0.04, 0.05], [0.06, 0], [0.07, -0.12], [0.05, -0.16], [0.05, -0.15], [0.02, -0.05], [0.05, -0.08], [0, -0.01], [0.06, -0.04], [0.02, -0.03], [0, 0], [0, 0], [-0.04, -0.02], [-0.01, -0.01], [0, 0], [-0.05, 0], [-0.02, -0.06], [0.04, -0.04], [-0.02, -0.08], [-0.05, 0.02], [-0.02, -0.06], [0, 0], [0.1, -0.1], [-0.03, -0.11], [-0.04, -0.04], [-0.02, -0.06], [0.12, -0.17], [0.27, -0.07], [-0.03, -0.09], [-0.02, -0.09], [-0.03, -0.08], [-0.01, -0.03], [-0.01, -0.04], [0, -0.06], [0.02, -0.04], [0.04, -0.01], [0, 0], [0.05, 0], [0.01, 0], [0, 0], [0.07, -0.08], [0.05, -0.03], [0, 0], [0.1, 0.1], [0.05, 0.2]], "o": [[0, -0.02], [0, -0.1], [0, -0.02], [-0.08, -0.04], [0, -0.02], [0, 0], [0, 0], [-0.06, -0.25], [-0.18, -0.21], [-0.14, -0.35], [-0.12, -0.33], [0, 0], [-0.02, -0.12], [0.01, -0.13], [0.08, 0.04], [0.1, 0.11], [0.01, 0], [0.06, -0.06], [0, 0], [0.11, -0.03], [0.08, 0.03], [0.02, 0.05], [0.04, 0.13], [0.05, 0.15], [0.07, 0.13], [0.01, 0.01], [0, 0], [0.02, 0], [0.04, -0.02], [-0.03, -0.1], [-0.12, -0.09], [-0.01, -0.03], [0.03, -0.07], [0.04, -0.07], [0.04, -0.05], [0, 0], [0, 0], [0.01, 0], [0.02, 0.02], [0, 0], [0.06, -0.02], [0.01, -0.05], [-0.01, -0.06], [-0.01, -0.06], [0.02, -0.03], [0.07, -0.08], [0, 0], [0.06, -0.02], [0.02, 0.07], [0.14, -0.04], [0.07, -0.13], [0.04, -0.14], [0.03, -0.12], [0.07, -0.02], [0.09, 0.02], [0.1, 0.03], [0.09, 0.03], [0.04, 0], [0.12, -0.04], [0.01, 0], [0.08, 0.05], [0.1, 0.05], [0.08, 0.05], [0, 0], [0, 0], [0.14, 0.08], [0.07, 0.05], [0.03, 0.11], [-0.02, 0.05], [0.03, 0.13], [0.06, 0.1], [0, 0], [0, 0.01], [-0.02, 0.02], [0.01, 0.02], [0.03, 0.04], [0.1, -0.05], [0.05, -0.04], [-0.02, -0.06], [-0.04, -0.02], [0, -0.02], [0.06, -0.1], [0.08, -0.12], [0.07, -0.11], [0.03, -0.07], [0.08, -0.17], [0.11, -0.17], [0.11, -0.14], [0, 0], [0.01, 0.01], [0.05, 0.02], [0.06, -0.02], [0.06, -0.07], [0.05, -0.08], [0.03, -0.09], [0.03, 0.02], [0.1, 0.01], [0.01, 0.03], [0, 0], [0.02, 0], [0.02, -0.03], [0, 0], [0, 0], [0, 0], [-0.07, -0.27], [0.05, -0.11], [0.09, 0.01], [0.1, 0.06], [0.1, 0.07], [0.09, 0.05], [0.02, 0], [0.03, -0.04], [0, 0], [0.06, -0.02], [0.06, 0.07], [0.06, 0.11], [0.04, 0.11], [0.07, 0.27], [-0.01, 0.29], [0.01, 0.04], [0, 0], [-0.01, 0.03], [-0.02, 0.07], [0, 0], [0.01, 0.02], [0, 0.01], [0.02, 0.02], [0.05, 0.03], [0.01, 0.02], [-0.01, 0.05], [0, 0], [0.03, 0.11], [0.02, 0.08], [0.03, 0.08], [0.04, 0.08], [0.01, 0.02], [0.07, 0.06], [0.01, 0.02], [-0.01, 0.06], [0.01, 0.02], [0.01, 0.08], [0.03, 0.13], [0.07, 0.07], [0.07, 0.05], [0.07, 0.04], [0, 0], [-0.03, -0.01], [-0.1, -0.02], [-0.02, 0.01], [-0.08, 0.07], [0, 0], [-0.19, 0.05], [-0.19, -0.19], [-0.19, -0.32], [-0.17, -0.38], [-0.15, -0.33], [-0.11, -0.22], [0, 0], [-0.02, 0.05], [-0.09, 0.14], [-0.02, 0.02], [-0.06, 0.12], [0, 0], [-0.07, 0.02], [-0.03, -0.02], [0.03, 0.12], [0.04, 0.09], [0.02, 0.08], [-0.01, 0.09], [0, 0], [0, 0], [-0.02, -0.02], [-0.04, -0.06], [0, 0], [-0.01, -0.05], [-0.02, 0.01], [-0.03, 0.03], [0, 0], [0, 0], [0.01, 0.02], [-0.01, 0.03], [-0.04, 0.01], [-0.04, 0], [-0.05, 0.01], [-0.06, 0.06], [0, 0.01], [0, 0.01], [0.03, 0], [0.08, 0], [0, 0], [0, 0], [0.06, 0.22], [-0.13, 0.07], [-0.02, -0.08], [-0.05, -0.07], [-0.11, 0.03], [-0.03, 0.15], [0.03, 0.1], [0.1, 0.05], [0.06, 0.25], [-0.09, 0.11], [-0.15, 0.08], [-0.14, 0.09], [0, 0], [0, 0], [-0.01, -0.01], [-0.06, -0.04], [0, 0], [0, 0.01], [-0.03, 0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, -0.03], [0, -0.01], [0.01, -0.02], [-0.04, -0.15], [-0.06, -0.2], [-0.07, -0.2], [-0.08, -0.18], [-0.08, -0.09], [-0.06, -0.06], [-0.05, -0.06], [-0.04, -0.08], [0, 0], [0, -0.01], [0.02, -0.05], [-0.02, -0.06], [-0.07, -0.1], [0, 0], [0.03, -0.06], [-0.01, -0.05], [0, 0], [-0.02, 0.02], [-0.03, 0.04], [-0.04, 0.01], [-0.03, -0.04], [-0.04, -0.06], [-0.04, -0.05], [-0.07, 0], [-0.07, 0.12], [-0.05, 0.16], [-0.04, 0.15], [-0.01, 0.04], [-0.05, 0.08], [-0.02, 0.02], [-0.06, 0.04], [0, 0], [0, 0], [0.02, 0.01], [0.04, 0.02], [0, 0], [0.05, -0.02], [0.05, 0], [0.02, 0.08], [-0.04, 0.04], [0.02, 0.08], [0.05, -0.02], [0, 0], [-0.02, 0.06], [-0.1, 0.1], [0.02, 0.06], [0.04, 0.04], [0.08, 0.3], [-0.12, 0.17], [0.01, 0.03], [0.03, 0.09], [0.02, 0.09], [0.03, 0.08], [0.01, 0.02], [0.01, 0.04], [0, 0.06], [-0.02, 0.04], [0, 0], [-0.02, 0.01], [-0.05, 0], [0, 0], [-0.08, 0.02], [-0.07, 0.08], [0, 0], [-0.2, 0.05], [-0.1, -0.1], [0, 0]], "v": [[146.7, -30], [146.69, -30.19], [146.67, -30.37], [146.55, -30.47], [146.42, -30.56], [146.49, -30.68], [146.57, -30.76], [146.53, -30.92], [146.16, -31.62], [145.74, -32.32], [145.34, -33.34], [145.02, -34.4], [144.76, -35.44], [144.74, -35.81], [144.82, -36.14], [145.1, -35.93], [145.37, -35.83], [145.47, -35.93], [145.58, -36.05], [145.69, -36.08], [145.98, -36.08], [146.27, -35.95], [146.37, -35.67], [146.5, -35.25], [146.68, -34.83], [146.9, -34.54], [146.94, -34.54], [147.01, -34.56], [147.11, -34.6], [147.15, -34.73], [146.93, -35.01], [146.7, -35.31], [146.73, -35.46], [146.83, -35.67], [146.94, -35.87], [147.04, -35.96], [147.07, -35.95], [147.09, -35.96], [147.13, -35.94], [147.18, -35.91], [147.21, -35.92], [147.32, -36.02], [147.32, -36.18], [147.29, -36.35], [147.28, -36.49], [147.41, -36.66], [147.56, -36.84], [147.61, -36.86], [147.74, -36.77], [147.96, -36.71], [148.26, -36.96], [148.42, -37.36], [148.51, -37.74], [148.63, -37.93], [148.87, -37.93], [149.15, -37.86], [149.43, -37.78], [149.66, -37.75], [149.89, -37.8], [150.1, -37.86], [150.24, -37.8], [150.51, -37.65], [150.78, -37.5], [150.92, -37.42], [151.24, -36.39], [151.22, -36.28], [151.53, -36.1], [151.68, -35.82], [151.69, -35.6], [151.71, -35.33], [151.85, -34.99], [151.98, -34.66], [152, -34.59], [151.97, -34.51], [151.93, -34.47], [151.99, -34.37], [152.07, -34.31], [152.29, -34.44], [152.32, -34.64], [152.23, -34.75], [152.15, -34.86], [152.22, -35.04], [152.42, -35.38], [152.63, -35.72], [152.75, -35.94], [152.91, -36.31], [153.2, -36.83], [153.53, -37.29], [153.84, -37.54], [153.93, -37.56], [154.02, -37.52], [154.12, -37.49], [154.3, -37.62], [154.46, -37.84], [154.57, -38.11], [154.58, -38.36], [154.77, -38.32], [154.99, -38.3], [155.04, -38.23], [155.08, -38.21], [155.15, -38.27], [155.2, -38.35], [155.19, -38.38], [155.17, -38.4], [155.09, -38.37], [155.06, -38.94], [155.52, -39.3], [155.81, -39.19], [156.1, -38.99], [156.37, -38.81], [156.59, -38.76], [156.66, -38.83], [156.7, -38.9], [156.78, -38.92], [156.97, -38.84], [157.15, -38.57], [157.31, -38.25], [157.39, -38.03], [157.48, -37.19], [157.57, -36.35], [157.71, -36.27], [157.85, -36.24], [157.8, -36.1], [157.76, -35.96], [157.73, -35.92], [157.76, -35.9], [157.78, -35.88], [157.87, -35.81], [157.97, -35.77], [157.96, -35.66], [157.95, -35.55], [158.06, -35.11], [158.14, -34.81], [158.21, -34.57], [158.32, -34.33], [158.5, -34.04], [158.61, -33.93], [158.72, -33.83], [158.71, -33.71], [158.61, -33.6], [158.63, -33.45], [158.66, -33.27], [158.82, -32.98], [159.04, -32.81], [159.25, -32.68], [159.38, -32.5], [159.27, -32.47], [159.08, -32.52], [158.88, -32.54], [158.74, -32.44], [158.61, -32.3], [158.52, -32.28], [157.94, -32.5], [157.37, -33.27], [156.83, -34.34], [156.36, -35.41], [155.97, -36.24], [155.71, -36.54], [155.66, -36.52], [155.49, -36.25], [155.3, -35.95], [155.17, -35.74], [155.07, -35.52], [154.99, -35.5], [154.84, -35.51], [154.65, -35.5], [154.76, -35.18], [154.86, -34.92], [154.88, -34.66], [154.76, -34.33], [154.73, -34.32], [154.67, -34.31], [154.59, -34.43], [154.5, -34.56], [154.48, -34.66], [154.4, -34.72], [154.32, -34.67], [154.28, -34.6], [154.38, -34.2], [154.55, -34.12], [154.54, -34.06], [154.49, -34.01], [154.36, -34.03], [154.24, -34.04], [154.07, -33.93], [154.01, -33.75], [154.04, -33.73], [154.05, -33.72], [154.17, -33.71], [154.33, -33.68], [154.37, -33.66], [154.41, -33.64], [154.29, -33.2], [153.85, -33.01], [153.74, -33.24], [153.56, -33.32], [153.34, -33.06], [153.33, -32.71], [153.52, -32.49], [153.72, -32.22], [153.68, -31.67], [153.32, -31.39], [152.89, -31.14], [152.64, -30.71], [152.6, -30.69], [152.55, -30.68], [152.44, -30.75], [152.32, -30.81], [152.27, -30.79], [152.23, -30.73], [152.19, -30.66], [152.16, -30.65], [152.11, -30.64], [152.04, -30.72], [152, -30.77], [152, -30.89], [152.02, -31], [152.03, -31.05], [152.03, -31.11], [151.88, -31.63], [151.69, -32.24], [151.45, -32.82], [151.19, -33.26], [150.98, -33.48], [150.82, -33.67], [150.68, -33.88], [150.56, -34.22], [150.45, -34.64], [150.48, -34.74], [150.5, -34.84], [150.37, -35.09], [150.25, -35.24], [150.02, -35.18], [150.04, -35.35], [150, -35.52], [149.95, -35.69], [149.88, -35.62], [149.8, -35.55], [149.7, -35.6], [149.6, -35.75], [149.49, -35.91], [149.34, -35.99], [149.14, -35.82], [148.96, -35.41], [148.82, -34.94], [148.72, -34.65], [148.63, -34.47], [148.57, -34.34], [148.45, -34.26], [148.33, -34.16], [148.33, -34.15], [148.35, -34.1], [148.45, -34.07], [148.53, -34.03], [148.3, -33.64], [148.45, -33.67], [148.55, -33.57], [148.52, -33.39], [148.49, -33.2], [148.59, -33.11], [148.69, -33.04], [148.69, -33.01], [148.52, -32.77], [148.42, -32.45], [148.51, -32.3], [148.6, -32.15], [148.53, -31.44], [147.94, -31.08], [147.99, -30.9], [148.07, -30.63], [148.15, -30.37], [148.2, -30.2], [148.21, -30.1], [148.22, -29.95], [148.19, -29.81], [148.1, -29.73], [148.07, -29.72], [147.96, -29.71], [147.87, -29.72], [147.85, -29.71], [147.63, -29.55], [147.46, -29.38], [147.41, -29.37], [146.96, -29.43], [146.73, -29.87]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[140.22, -28.9], [140.21, -28.97], [140.16, -28.95]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.64, -30.34], [139.66, -30.27], [139.61, -30.26], [139.59, -30.32]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.03, -0.01], [0, -0.02], [-0.02, 0]], "o": [[0.01, -0.02], [-0.03, 0.01], [0.02, 0.02], [0.02, 0]], "v": [[139.8, -28.93], [139.77, -28.95], [139.74, -28.91], [139.79, -28.89]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.03, -0.01], [0, -0.01], [0.02, -0.02], [0.01, 0.03]], "o": [[0.02, 0], [0, 0.01], [-0.02, 0.01], [-0.01, -0.03]], "v": [[138.28, -34.07], [138.32, -34.06], [138.3, -34.02], [138.26, -34.05]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.03, -0.01], [-0.01, -0.02], [-0.02, 0]], "o": [[0.01, -0.02], [-0.03, 0.01], [0.02, 0.02], [0.02, 0]], "v": [[137.7, -33.87], [137.67, -33.89], [137.64, -33.85], [137.69, -33.83]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.05], [-0.02, 0.01], [0.01, 0.05]], "o": [[-0.02, 0.01], [0.01, 0.05], [0.04, -0.01], [-0.01, -0.05]], "v": [[138.44, -29.63], [138.43, -29.55], [138.48, -29.49], [138.52, -29.57]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0], [-0.01, -0.03], [-0.02, 0.02], [0.01, 0.02]], "o": [[-0.02, 0], [0.01, 0.03], [0.02, 0], [0, -0.02]], "v": [[137.24, -33.27], [137.22, -33.23], [137.26, -33.2], [137.28, -33.24]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[137.99, -30.06], [138.01, -30], [137.93, -30.05]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.05, -0.09], [0.02, 0], [0.02, -0.04], [0.01, 0], [0, 0], [0, 0.02], [-0.04, 0.06], [-0.01, 0.03], [0, 0]], "o": [[-0.01, 0.03], [-0.05, 0.09], [0, -0.01], [-0.01, 0.01], [0, 0], [0, -0.01], [0.02, -0.03], [0.04, -0.06], [0, 0], [0, 0]], "v": [[138.04, -30.42], [137.94, -30.25], [137.83, -30.11], [137.79, -30.06], [137.77, -30.04], [137.76, -30.09], [137.76, -30.14], [137.84, -30.26], [137.91, -30.39], [137.97, -30.41]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, -0.01], [-0.01, -0.04], [-0.04, 0.01], [0.01, 0.04]], "o": [[-0.04, 0.01], [0.01, 0.04], [0.03, -0.01], [-0.01, -0.04]], "v": [[135.63, -29.31], [135.58, -29.23], [135.66, -29.18], [135.69, -29.26]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.04], [-0.03, 0.01], [0.01, 0.04]], "o": [[-0.03, 0.01], [0.01, 0.04], [0.04, -0.01], [-0.01, -0.04]], "v": [[135.49, -28.72], [135.46, -28.65], [135.52, -28.6], [135.57, -28.68]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.02, 0.01], [-0.01, -0.03], [-0.03, 0.02], [0, 0.02]], "o": [[-0.03, 0], [0.01, 0.03], [0.02, 0], [0, -0.02]], "v": [[135.07, -28.83], [135.03, -28.79], [135.08, -28.77], [135.11, -28.8]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -0.02], [-0.01, -0.01], [-0.01, 0], [0, 0], [-0.01, 0.01], [-0.01, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.02, 0.01], [0, 0.01], [0, 0], [0, 0], [0.01, -0.03]], "o": [[0.01, 0], [0.03, 0.02], [0, 0], [0.01, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [-0.02, 0], [0, 0], [0, 0], [-0.02, 0.03], [-0.01, 0.03]], "v": [[135.11, -28.34], [135.14, -28.33], [135.2, -28.31], [135.22, -28.3], [135.24, -28.33], [135.27, -28.35], [135.29, -28.39], [135.3, -28.43], [135.28, -28.45], [135.27, -28.47], [135.24, -28.49], [135.2, -28.51], [135.16, -28.51], [135.15, -28.5], [135.11, -28.42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[134.34, -28.36], [134.38, -28.44], [134.32, -28.42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, 0.03], [0, -0.03], [-0.01, -0.04], [-0.01, -0.02], [-0.01, 0.01], [0.02, 0.06]], "o": [[-0.01, -0.01], [0, 0.03], [0.01, 0.04], [0.01, 0.03], [0.02, -0.03], [-0.02, -0.06]], "v": [[132.83, -31.59], [132.81, -31.55], [132.83, -31.44], [132.87, -31.34], [132.9, -31.32], [132.9, -31.45]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[132.21, -31.01], [132.27, -31.03], [132.26, -31.07], [132.2, -31.06]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.93, -30.99], [132.01, -31.01], [131.99, -31.09], [131.91, -31.07]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.01, 0.06], [0.01, 0.07], [0.01, 0.06], [0.01, 0.03], [0.02, 0.03], [0.03, 0.04], [0.03, 0.04], [0, 0.01], [0, 0.05], [-0.02, 0.03], [0, 0], [0, 0], [-0.04, 0.05], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [-0.09, 0], [-0.04, -0.01], [0, 0], [-0.01, 0.01], [-0.02, 0.06], [-0.01, 0.01], [-0.06, -0.01], [-0.02, 0.01], [0.01, 0.02], [0.01, 0.03], [0.01, 0.04], [-0.02, 0.01], [0, 0], [-0.01, 0], [-0.01, 0], [0, 0], [-0.04, -0.03], [-0.02, 0.01], [-0.01, 0], [0, 0], [0, 0], [-0.02, 0.02], [-0.02, 0.02], [-0.01, 0], [-0.01, 0.04], [-0.01, 0.01], [0.01, 0.04], [0, 0], [0, 0], [-0.02, -0.03], [-0.01, -0.01], [-0.04, -0.02], [-0.02, 0.04], [0, 0.02], [-0.01, 0.04], [-0.01, 0.01], [-0.02, -0.03], [-0.01, 0], [0, 0], [0, 0], [0, 0.01], [0.01, 0.04], [0, 0], [0, 0], [0.04, -0.01], [-0.05, 0.05], [-0.02, 0.02], [0, 0], [0, 0], [-0.02, -0.03], [-0.01, -0.01], [-0.02, -0.01], [-0.01, 0], [0, 0], [0, 0], [-0.11, 0.06], [-0.02, 0.02], [-0.03, 0.02], [-0.01, 0.01], [0, 0], [0, 0], [-0.04, 0.08], [-0.01, 0.03], [-0.03, -0.01], [-0.02, 0.01], [-0.03, 0.03], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.04, 0.04], [-0.05, 0.04], [-0.04, 0.04], [-0.01, 0.01], [-0.09, 0.04], [-0.05, 0.02], [0, 0], [0, 0], [-0.06, -0.01], [0, 0], [-0.12, 0.03], [-0.03, 0.02], [-0.02, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [0, 0.04], [0, 0.01], [-0.02, 0.03], [-0.01, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, 0], [-0.03, -0.03], [0, -0.01], [0, 0], [0, 0], [-0.01, 0.03], [-0.01, 0.01], [0, 0], [0.01, -0.05], [0, -0.02], [0, 0], [0, 0], [-0.04, -0.03], [-0.01, -0.01], [-0.04, 0], [0, -0.02], [-0.03, -0.05], [-0.02, -0.02], [0, 0], [-0.02, 0.01], [-0.02, -0.02], [0, 0], [0, 0], [-0.02, -0.02], [-0.03, -0.01], [-0.03, -0.01], [-0.02, 0], [0.01, -0.07], [0.01, -0.02], [0, 0], [-0.01, -0.01], [0, 0], [-0.03, 0.02], [-0.02, 0.03], [0, 0], [-0.02, 0.02], [-0.02, 0.01], [0, 0], [0, 0], [0.07, -0.03], [0.02, -0.02], [0, 0], [0, -0.04], [0.01, -0.01], [0.07, -0.01], [0, -0.01], [0.04, -0.03], [0.02, -0.02], [0.01, -0.04], [0, -0.01], [0, 0], [0, 0], [0, 0], [0.03, 0], [0.01, 0.01], [0.11, -0.01], [0.02, 0.01], [0.06, 0.04], [0.03, 0.02], [0.01, -0.02], [0, -0.01], [0, 0], [0, 0], [0.05, 0.01], [0.02, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0.02, 0], [0.01, 0], [0.04, 0.03], [0.02, 0], [0.02, 0], [0.02, 0], [0, 0], [0.04, -0.05], [0.02, 0], [0.05, -0.01], [0.05, -0.01], [0.04, -0.01], [0.01, 0], [0.08, -0.03], [0.08, -0.03], [0.08, -0.03], [0.02, 0], [0, 0], [0.06, -0.03], [0.02, -0.02], [0.03, -0.03], [0.01, 0], [0.06, -0.01], [0.01, 0], [0.05, -0.03], [0.02, 0], [0.11, -0.02], [0.02, 0], [0.04, -0.05], [0.01, 0], [0.06, -0.02], [0.07, -0.02], [0.07, -0.02], [0.03, -0.01], [0, 0], [0.03, -0.03], [0.01, 0], [0.08, -0.02], [0.03, -0.01], [0, 0], [0.02, 0.01]], "o": [[0.01, -0.02], [-0.01, -0.06], [-0.01, -0.07], [-0.01, -0.06], [0, -0.01], [-0.03, -0.03], [-0.03, -0.04], [-0.03, -0.04], [0, -0.02], [0, -0.05], [0, 0], [0, 0], [0.01, 0], [0.04, -0.05], [0, 0], [0, 0], [0, 0], [0.02, 0.02], [0.1, 0], [0, 0], [0.01, 0], [0.01, -0.02], [0.02, -0.06], [0.02, -0.02], [0.06, 0.01], [0, -0.01], [-0.01, -0.02], [-0.02, -0.03], [0.01, -0.06], [0, 0], [0.01, 0], [0.01, 0], [0, 0], [0.01, 0], [0.04, 0.03], [0.01, 0.01], [0, 0], [0, 0], [0.01, 0], [0.02, 0], [0.02, -0.02], [0.01, -0.02], [0.01, -0.04], [0.03, -0.01], [0, 0], [0, 0], [0.01, 0.01], [0.02, 0.03], [0.01, 0.02], [0.01, -0.02], [0.02, -0.04], [0.01, 0], [0.01, -0.04], [0, 0.01], [0.03, 0.03], [0, 0], [0, 0], [0.01, 0], [0.04, -0.01], [0, 0], [0, 0], [-0.01, -0.03], [0.02, -0.03], [0.05, -0.05], [0, 0], [0, 0], [0.01, 0.01], [0.02, 0.03], [0.01, 0], [0.02, 0.01], [0, 0], [0, 0], [0.04, -0.02], [0.11, -0.06], [0.01, -0.01], [0.03, -0.02], [0, 0], [0, 0], [0.02, -0.03], [0.05, -0.08], [0.01, 0.01], [0.03, 0.01], [0.01, 0], [0.03, -0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0.05, -0.04], [0.05, -0.04], [0.05, -0.04], [0.02, -0.02], [0.09, -0.04], [0, 0], [0, 0], [0, 0.01], [0, 0], [0.03, 0], [0.12, -0.03], [0, 0.01], [0.02, 0.01], [0, 0], [0, 0], [0.01, -0.01], [0, -0.04], [0.01, 0], [0.01, -0.01], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0.01], [0.03, 0.03], [0, 0], [0, 0], [0.01, -0.01], [0.01, -0.02], [0, 0], [0, 0.01], [-0.01, 0.05], [0, 0], [0, 0], [0, 0.01], [0.05, 0.03], [0.01, 0], [0.04, 0], [0, 0.01], [0.03, 0.05], [0, 0], [0.01, 0.01], [0.01, 0.02], [0, 0], [0, 0], [0.01, 0.02], [0.02, 0.02], [0.01, 0.01], [0.03, 0.01], [-0.02, 0.02], [-0.01, 0.07], [0, 0], [0, 0.01], [0, 0], [0.02, 0], [0.03, -0.02], [0, 0], [0.02, -0.02], [0.02, -0.02], [0, 0], [0, 0], [-0.02, 0.01], [-0.06, 0.03], [0, 0], [0, 0.01], [0, 0.04], [-0.02, 0.01], [-0.07, 0.01], [-0.03, 0.01], [-0.04, 0.03], [-0.01, 0], [-0.01, 0.04], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [-0.04, 0], [-0.02, 0], [-0.11, 0.01], [-0.02, 0], [-0.06, -0.04], [-0.01, 0.01], [-0.01, 0.02], [0, 0], [0, 0], [-0.02, 0], [-0.05, -0.01], [0, 0], [0, 0.01], [0, 0], [0, 0], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.02], [0, 0], [-0.02, 0.01], [-0.02, 0], [-0.01, 0], [-0.04, -0.03], [-0.01, 0], [-0.02, 0], [0, 0], [-0.02, 0.01], [-0.04, 0.05], [-0.01, 0], [-0.05, 0.01], [-0.05, 0.01], [-0.04, 0.01], [-0.02, 0.01], [-0.08, 0.03], [-0.08, 0.03], [-0.08, 0.03], [0, 0], [-0.02, 0.02], [-0.06, 0.03], [-0.01, 0], [-0.03, 0.03], [-0.01, 0.01], [-0.06, 0.01], [-0.01, 0.01], [-0.05, 0.03], [-0.02, 0.02], [-0.11, 0.02], [-0.02, 0.02], [-0.04, 0.06], [-0.01, 0], [-0.06, 0.02], [-0.07, 0.02], [-0.07, 0.02], [0, 0], [-0.01, 0], [-0.02, 0.03], [-0.01, 0], [-0.08, 0.03], [0, 0], [-0.03, 0.01], [0, 0]], "v": [[131.6, -29.89], [131.59, -30.01], [131.56, -30.2], [131.52, -30.4], [131.5, -30.55], [131.46, -30.61], [131.38, -30.71], [131.3, -30.83], [131.26, -30.9], [131.25, -31.01], [131.28, -31.12], [131.34, -31.17], [131.39, -31.22], [131.47, -31.3], [131.57, -31.38], [131.59, -31.4], [131.63, -31.4], [131.78, -31.32], [131.95, -31.29], [132.15, -31.26], [132.18, -31.25], [132.21, -31.28], [132.24, -31.4], [132.28, -31.51], [132.4, -31.52], [132.53, -31.51], [132.51, -31.55], [132.48, -31.63], [132.44, -31.74], [132.48, -31.83], [132.54, -31.86], [132.57, -31.86], [132.59, -31.86], [132.61, -31.86], [132.69, -31.82], [132.78, -31.77], [132.82, -31.77], [132.86, -31.78], [132.92, -31.79], [132.97, -31.82], [133.03, -31.86], [133.08, -31.9], [133.1, -32], [133.12, -32.08], [133.15, -32.15], [133.18, -32.16], [133.23, -32.17], [133.28, -32.11], [133.34, -32.05], [133.41, -31.98], [133.45, -32.08], [133.49, -32.17], [133.52, -32.24], [133.54, -32.32], [133.58, -32.25], [133.63, -32.21], [133.65, -32.2], [133.68, -32.21], [133.7, -32.22], [133.74, -32.3], [133.73, -32.33], [133.73, -32.37], [133.65, -32.4], [133.76, -32.51], [133.87, -32.6], [133.91, -32.61], [133.94, -32.62], [133.99, -32.56], [134.04, -32.5], [134.09, -32.48], [134.13, -32.47], [134.15, -32.47], [134.18, -32.48], [134.41, -32.61], [134.6, -32.72], [134.66, -32.77], [134.72, -32.82], [134.79, -32.84], [134.87, -32.86], [134.96, -33.02], [135.05, -33.19], [135.11, -33.16], [135.19, -33.16], [135.24, -33.2], [135.31, -33.26], [135.33, -33.26], [135.38, -33.27], [135.45, -33.26], [135.52, -33.24], [135.56, -33.25], [135.58, -33.26], [135.66, -33.32], [135.81, -33.44], [135.95, -33.56], [136.03, -33.64], [136.19, -33.72], [136.4, -33.82], [136.48, -33.84], [136.53, -33.85], [136.62, -33.82], [136.69, -33.81], [136.92, -33.85], [137.14, -33.92], [137.17, -33.9], [137.22, -33.88], [137.26, -33.89], [137.29, -33.9], [137.31, -33.97], [137.31, -34.05], [137.35, -34.09], [137.37, -34.12], [137.4, -34.14], [137.43, -34.13], [137.47, -34.12], [137.51, -34.1], [137.56, -34.04], [137.61, -33.98], [137.64, -33.98], [137.67, -33.99], [137.7, -34.05], [137.72, -34.11], [137.97, -34.17], [137.96, -34.08], [137.96, -33.98], [137.96, -33.97], [137.97, -33.94], [138.04, -33.88], [138.13, -33.83], [138.21, -33.83], [138.29, -33.8], [138.33, -33.71], [138.4, -33.61], [138.44, -33.59], [138.49, -33.59], [138.52, -33.53], [138.57, -33.47], [138.68, -33.57], [138.72, -33.5], [138.79, -33.45], [138.86, -33.42], [138.94, -33.4], [138.9, -33.27], [138.87, -33.14], [138.88, -33.1], [138.9, -33.08], [138.94, -33.08], [139.01, -33.12], [139.07, -33.19], [139.25, -32.95], [139.31, -33.01], [139.38, -33.05], [139.44, -33.06], [139.48, -32.92], [139.35, -32.87], [139.22, -32.81], [139.07, -32.55], [139.07, -32.48], [139.06, -32.4], [138.92, -32.37], [138.81, -32.34], [138.7, -32.28], [138.61, -32.22], [138.58, -32.15], [138.58, -32.08], [138.53, -32], [138.5, -31.99], [138.47, -31.98], [138.4, -32], [138.33, -32.01], [138.12, -32], [137.92, -31.99], [137.79, -32.05], [137.67, -32.13], [137.64, -32.08], [137.62, -32.03], [137.56, -32.02], [137.5, -32], [137.38, -32.02], [137.28, -32.03], [137.14, -31.79], [137.12, -31.77], [137.09, -31.76], [137.06, -31.77], [136.99, -31.77], [136.92, -31.79], [136.86, -31.77], [136.78, -31.75], [136.75, -31.69], [136.7, -31.66], [136.64, -31.65], [136.58, -31.64], [136.54, -31.64], [136.47, -31.68], [136.39, -31.72], [136.34, -31.71], [136.28, -31.7], [136.2, -31.67], [136.11, -31.59], [136.03, -31.51], [135.94, -31.48], [135.79, -31.45], [135.65, -31.41], [135.57, -31.39], [135.43, -31.34], [135.19, -31.25], [134.96, -31.17], [134.81, -31.11], [134.61, -31.06], [134.49, -31], [134.38, -30.93], [134.32, -30.89], [134.27, -30.84], [134.17, -30.81], [134.06, -30.79], [133.98, -30.72], [133.88, -30.67], [133.69, -30.62], [133.5, -30.58], [133.41, -30.47], [133.33, -30.38], [133.24, -30.35], [133.05, -30.29], [132.85, -30.22], [132.71, -30.17], [132.3, -30.07], [132.25, -30.01], [132.2, -29.96], [132.07, -29.92], [131.9, -29.87], [131.76, -29.83], [131.68, -29.83]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.01], [0.01, 0.01], [0, 0.01], [0, 0], [0.03, 0.04], [0.03, 0.02], [0.05, 0.04], [0, 0.01], [0, 0], [0.03, 0.05], [0.01, 0.01], [0.08, 0.05], [0.02, 0.02], [0.01, 0.02], [0, 0], [0, 0], [0, 0.02], [-0.02, 0.06], [-0.01, 0.03], [-0.01, 0.03], [0, 0], [-0.08, 0.04], [-0.02, 0.01], [-0.04, 0.02], [-0.02, 0.02], [0, 0], [-0.04, 0.03], [-0.02, 0.01], [0, 0], [0, 0], [-0.08, 0.04], [-0.02, 0.02], [-0.05, 0.03], [-0.01, 0], [0, 0], [0, 0], [-0.05, -0.05], [-0.02, 0], [0, 0], [0, 0], [-0.02, 0.04], [0, 0.02], [0, 0.05], [0, 0.03], [0, 0], [-0.04, 0.05], [-0.02, 0.01], [0, 0], [-0.02, -0.01], [-0.01, 0], [-0.03, 0.02], [-0.01, 0.01], [-0.05, 0.04], [-0.01, 0], [-0.06, 0.05], [-0.02, 0.02], [-0.07, 0.01], [-0.02, 0.02], [0, 0], [0, 0.01], [0, 0], [0, 0], [-0.05, -0.01], [0, 0], [0, 0], [-0.04, -0.01], [-0.02, 0.01], [0, 0], [0, 0], [-0.07, 0.06], [-0.02, 0.03], [-0.01, 0.02], [0, 0], [-0.04, -0.01], [-0.01, 0], [0, 0], [0, 0], [-0.03, 0.04], [0, 0.01], [0, 0.01], [-0.01, 0], [-0.03, -0.07], [-0.02, -0.02], [-0.04, -0.03], [-0.01, 0], [0, 0], [-0.02, 0.11], [-0.01, 0.03], [-0.02, 0.04], [0, 0], [0, 0], [-0.1, 0.01], [-0.02, 0.01], [-0.06, 0], [-0.01, 0], [0, 0], [-0.02, -0.02], [-0.01, -0.01], [-0.04, -0.09], [0, -0.02], [-0.07, -0.04], [0, -0.01], [-0.05, -0.04], [0, -0.01], [0, 0], [-0.06, -0.01], [0, 0], [0, 0], [-0.01, -0.03], [-0.02, -0.03], [-0.01, -0.01], [0, 0], [0, 0], [-0.01, 0.08], [0, 0.02], [-0.03, 0.04], [-0.01, 0], [0, 0], [0, 0], [0, -0.07], [0.01, -0.02], [0.02, -0.07], [0.01, -0.01], [0.01, -0.06], [0, -0.01], [0.05, -0.02], [0.02, 0], [0.04, 0.03], [0.02, 0.01], [0.06, -0.01], [0.02, 0], [0.03, 0.04], [0.01, 0.01], [0, 0], [0, 0], [0, 0], [0.03, -0.03], [0.01, 0], [0, 0], [0, 0], [0.05, 0.01], [0.02, 0], [0.01, 0.04], [-0.03, 0.02], [-0.01, 0.01], [-0.01, 0.03], [0.01, 0.02], [0.02, 0.01], [0.01, 0.01], [0, 0], [0, 0], [0.03, -0.08], [0.01, -0.04], [0, 0], [0, 0], [0.01, -0.02], [0, -0.01], [0.04, 0.03], [0.02, 0], [0, 0], [0, 0], [0.01, -0.06], [0, -0.01], [0.03, -0.06], [0.01, -0.02], [0.05, 0.02], [0.01, 0.01], [0, 0], [0.01, 0], [0.01, 0], [0.02, -0.01], [0.01, 0], [0.02, -0.04], [0.02, -0.03], [0, 0], [0, -0.01], [0.01, 0], [0, 0], [0, 0], [0.05, -0.03], [0.01, 0], [0.04, 0.02], [0.01, 0], [0.07, -0.02], [0.02, 0.01], [0.02, 0.02], [0, 0], [0.04, -0.01], [0, 0], [0, 0], [0.03, -0.03], [0.01, -0.01], [0.04, -0.03], [0.02, 0], [0.08, -0.04], [0.09, -0.04], [0.08, -0.04], [0.01, 0], [0.08, -0.06], [0.02, 0], [0.07, -0.01], [0.01, 0], [0, 0], [0, 0], [0, 0], [0.02, -0.04], [0.01, -0.01], [0.08, -0.03], [0.02, 0], [0, 0], [0.04, 0], [0.08, 0], [0.02, 0], [0, 0], [0, 0], [0.04, -0.05], [0.01, 0], [0, 0], [0, 0], [0.04, 0.01], [0.01, 0.01], [0.05, 0.01], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.04], [0.01, -0.01], [0, 0], [0.01, -0.01], [0, 0], [0.04, -0.02], [0.04, -0.02], [0.02, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0.01, 0], [0, 0], [0.02, -0.04], [0.01, -0.02], [0, 0], [-0.01, 0.08], [0, 0.02], [0, 0], [0, 0.01]], "o": [[0, -0.01], [-0.01, -0.01], [0, 0], [0, -0.02], [-0.03, -0.04], [0, -0.02], [-0.05, -0.04], [0, 0], [-0.02, -0.03], [-0.03, -0.05], [0, -0.01], [-0.07, -0.05], [-0.02, -0.02], [0, 0], [0, 0], [0, -0.02], [0, -0.01], [0.02, -0.06], [0.01, 0], [0, 0], [0.02, -0.03], [0.08, -0.04], [0.02, -0.02], [0.04, -0.02], [0, 0], [0.02, 0], [0.04, -0.03], [0, 0], [0, 0], [0.02, -0.02], [0.09, -0.04], [0.01, 0], [0.05, -0.03], [0, 0], [0, 0], [0.01, 0.01], [0.05, 0.05], [0, 0], [0, 0], [0, -0.01], [0.02, -0.04], [0.01, -0.01], [0, -0.05], [0, 0], [0.02, -0.02], [0.04, -0.05], [0, 0], [0.01, 0.01], [0.02, 0.01], [0.01, 0], [0.03, -0.02], [0.01, 0], [0.05, -0.04], [0.01, -0.02], [0.07, -0.05], [0.01, 0], [0.07, -0.01], [0, 0], [0.02, -0.03], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0.01], [0.04, 0.01], [0, 0], [0, 0], [0.03, -0.01], [0.07, -0.06], [0.01, -0.01], [0, 0], [0.01, 0], [0.04, 0.01], [0, 0], [0, 0], [0.01, -0.01], [0.03, -0.04], [0.01, 0], [0, -0.01], [0.01, 0.02], [0.03, 0.07], [0.01, 0.02], [0.04, 0.03], [0, 0], [0.01, -0.05], [0.02, -0.11], [0, -0.02], [0, 0], [0, 0], [0.05, 0.01], [0.1, -0.01], [0.01, 0.01], [0.06, 0], [0, 0], [0.01, 0.02], [0.02, 0.02], [0.01, 0.02], [0.05, 0.09], [0.01, 0.01], [0.07, 0.04], [0.03, 0.02], [0.06, 0.04], [0, 0], [0.01, 0.02], [0, 0], [0, 0], [0.05, 0.01], [0.01, 0.01], [0.02, 0.03], [0, 0], [0, 0], [0.01, -0.02], [0.01, -0.08], [0.01, -0.02], [0.03, -0.04], [0, 0], [0, 0], [0.01, 0.02], [0, 0.07], [0, 0.02], [-0.01, 0.08], [0.01, 0.02], [-0.01, 0.06], [0, 0.02], [-0.04, 0.02], [-0.02, 0], [-0.04, -0.03], [-0.02, 0], [-0.06, 0.01], [0, -0.02], [0, -0.01], [0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.03, 0.03], [0, 0], [0, 0], [-0.03, 0.01], [-0.05, -0.01], [-0.04, 0.01], [0.02, 0], [0.03, -0.02], [0.01, -0.02], [0.01, -0.03], [0, -0.01], [-0.02, -0.01], [0, 0], [0, 0], [-0.01, 0.01], [-0.03, 0.08], [0, 0], [0, 0], [-0.01, 0.01], [-0.01, 0.02], [-0.02, -0.02], [-0.04, -0.03], [0, 0], [0, 0], [0, 0.01], [-0.01, 0.06], [-0.01, 0.03], [-0.03, 0.06], [-0.02, -0.02], [-0.04, -0.02], [0, 0], [-0.01, 0], [-0.01, 0], [-0.02, 0.01], [-0.01, 0.01], [-0.01, 0.01], [-0.02, 0.04], [0, 0], [-0.01, 0.01], [0, 0.01], [0, 0], [0, 0], [-0.01, 0.01], [-0.05, 0.03], [-0.01, 0], [-0.04, -0.02], [-0.01, 0], [-0.07, 0.02], [-0.01, -0.01], [0, 0], [-0.01, -0.05], [0, 0], [0, 0], [-0.01, 0], [-0.02, 0.03], [-0.02, 0.01], [-0.04, 0.03], [-0.02, 0.01], [-0.08, 0.04], [-0.09, 0.04], [-0.08, 0.04], [-0.02, 0.01], [-0.08, 0.07], [-0.02, 0.02], [-0.07, 0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0.01], [-0.02, 0.04], [-0.02, 0.01], [-0.08, 0.03], [0, 0], [-0.04, 0.01], [-0.02, 0.01], [-0.08, 0], [0, 0], [0, 0], [-0.01, 0.01], [-0.04, 0.05], [0, 0], [0, 0], [-0.01, 0], [-0.04, -0.01], [-0.01, 0], [-0.05, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0.01], [-0.01, 0.04], [-0.01, 0.01], [0, 0.01], [0, 0], [-0.06, 0.02], [-0.05, 0.02], [-0.04, 0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0], [0, 0], [-0.02, 0.01], [-0.02, 0.04], [0, 0], [0.01, -0.02], [0.01, -0.08], [0, 0], [0, -0.01], [0, -0.01]], "v": [[132.34, -25.94], [132.33, -25.96], [132.31, -25.99], [132.31, -26.03], [132.26, -26.12], [132.18, -26.21], [132.09, -26.3], [132.01, -26.38], [132, -26.43], [131.93, -26.55], [131.87, -26.63], [131.75, -26.72], [131.62, -26.83], [131.58, -26.89], [131.56, -26.98], [131.53, -27.09], [131.53, -27.16], [131.56, -27.26], [131.6, -27.39], [131.64, -27.44], [131.66, -27.46], [131.81, -27.55], [131.96, -27.62], [132.06, -27.68], [132.15, -27.73], [132.34, -27.78], [132.43, -27.84], [132.52, -27.9], [132.57, -27.97], [132.62, -28.05], [132.77, -28.13], [132.93, -28.22], [133.01, -28.27], [133.1, -28.33], [133.33, -28.39], [133.49, -28.43], [133.58, -28.34], [133.68, -28.26], [133.74, -28.28], [133.82, -28.3], [133.85, -28.37], [133.89, -28.46], [133.9, -28.56], [133.91, -28.67], [134.02, -28.76], [134.1, -28.86], [134.19, -28.94], [134.33, -28.91], [134.38, -28.88], [134.42, -28.87], [134.48, -28.9], [134.53, -28.96], [134.63, -29.02], [134.72, -29.08], [134.82, -29.19], [134.95, -29.28], [135.07, -29.31], [135.2, -29.35], [135.22, -29.39], [135.24, -29.44], [135.27, -29.45], [135.3, -29.46], [135.38, -29.43], [135.45, -29.41], [135.59, -29.45], [135.65, -29.42], [135.75, -29.42], [136.07, -29.51], [136.37, -29.58], [136.53, -29.68], [136.68, -29.81], [136.7, -29.86], [136.71, -29.94], [136.78, -29.92], [136.85, -29.9], [136.9, -29.92], [136.92, -29.92], [136.97, -30], [137.01, -30.08], [137.02, -30.1], [137.03, -30.12], [137.08, -29.98], [137.15, -29.85], [137.22, -29.77], [137.3, -29.74], [138.09, -29.94], [138.14, -30.18], [138.2, -30.38], [138.22, -30.47], [138.25, -30.48], [138.3, -30.49], [138.53, -30.49], [138.71, -30.51], [138.82, -30.5], [138.92, -30.5], [139.26, -30.59], [139.29, -30.53], [139.33, -30.49], [139.41, -30.33], [139.48, -30.16], [139.61, -30.1], [139.72, -30.02], [139.84, -29.94], [139.93, -29.86], [139.88, -29.72], [139.98, -29.67], [140.09, -29.67], [140.2, -29.25], [140.28, -29.19], [140.34, -29.13], [140.39, -29.08], [140.43, -29.07], [140.46, -29.1], [140.49, -29.25], [140.51, -29.39], [140.56, -29.49], [140.62, -29.56], [140.67, -29.57], [140.75, -29.59], [140.75, -29.45], [140.74, -29.3], [140.71, -29.15], [140.68, -29.02], [140.67, -28.9], [140.67, -28.8], [140.59, -28.73], [140.49, -28.69], [140.41, -28.74], [140.32, -28.8], [140.19, -28.79], [140.07, -28.77], [140.02, -28.85], [140, -28.88], [139.96, -28.9], [139.93, -28.89], [139.88, -28.88], [139.82, -28.84], [139.77, -28.79], [139.74, -28.78], [139.72, -28.78], [139.6, -28.78], [139.49, -28.78], [139.41, -28.83], [139.48, -28.87], [139.54, -28.93], [139.56, -29.01], [139.56, -29.08], [139.53, -29.11], [139.48, -29.13], [139.4, -29.11], [139.34, -29.09], [139.29, -28.95], [139.22, -28.78], [139.21, -28.71], [139.19, -28.64], [139.16, -28.59], [139.15, -28.55], [139.07, -28.62], [138.97, -28.65], [138.96, -28.65], [138.91, -28.63], [138.9, -28.53], [138.9, -28.43], [138.82, -28.3], [138.76, -28.16], [138.67, -28.22], [138.59, -28.27], [138.56, -28.26], [138.52, -28.26], [138.49, -28.26], [138.44, -28.24], [138.4, -28.22], [138.35, -28.14], [138.3, -28.04], [138.28, -27.97], [138.27, -27.94], [138.26, -27.92], [138.23, -27.91], [138.2, -27.9], [138.12, -27.83], [138.03, -27.78], [137.95, -27.8], [137.87, -27.82], [137.75, -27.78], [137.6, -27.75], [137.56, -27.79], [137.51, -27.84], [137.43, -27.91], [137.42, -27.9], [137.37, -27.89], [137.32, -27.84], [137.26, -27.78], [137.17, -27.73], [137.07, -27.68], [136.92, -27.62], [136.67, -27.5], [136.42, -27.39], [136.28, -27.33], [136.13, -27.22], [135.99, -27.12], [135.85, -27.08], [135.72, -27.05], [135.62, -26.87], [135.54, -26.85], [135.43, -26.83], [135.37, -26.76], [135.33, -26.68], [135.19, -26.64], [135.04, -26.59], [134.87, -26.55], [134.75, -26.54], [134.6, -26.53], [134.46, -26.53], [134.44, -26.52], [134.39, -26.51], [134.32, -26.42], [134.24, -26.34], [134.19, -26.33], [134.18, -26.32], [134.1, -26.33], [134.02, -26.35], [133.93, -26.36], [133.82, -26.36], [133.79, -26.36], [133.75, -26.35], [133.73, -26.31], [133.71, -26.27], [133.69, -26.19], [133.67, -26.11], [133.66, -26.08], [133.65, -26.05], [133.62, -26.01], [133.47, -25.96], [133.34, -25.9], [133.24, -25.87], [133.21, -25.86], [133.18, -25.85], [133.11, -25.87], [133.09, -25.87], [133.06, -25.87], [133.04, -25.87], [132.98, -25.8], [132.94, -25.7], [132.3, -25.54], [132.32, -25.7], [132.35, -25.85], [132.34, -25.88], [132.34, -25.91]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.02], [0.01, -0.02], [0, 0], [0, -0.02], [0.01, -0.01], [-0.01, -0.04], [-0.01, -0.05], [-0.01, -0.04], [0, -0.01], [0.03, -0.05], [0.02, -0.02], [0.03, 0.06], [0.04, 0.07], [0.03, 0.06], [0.02, 0.02], [0, 0]], "o": [[0, 0.01], [0.01, 0.02], [0, 0], [-0.01, 0.01], [0, 0.01], [0, 0.01], [0.01, 0.04], [0.01, 0.05], [0.01, 0.04], [0, 0.03], [-0.03, 0.05], [-0.01, -0.03], [-0.03, -0.06], [-0.04, -0.07], [-0.03, -0.06], [0, 0], [0, 0]], "v": [[128.14, -27.14], [128.15, -27.09], [128.15, -27.03], [128.14, -26.96], [128.14, -26.9], [128.13, -26.87], [128.14, -26.8], [128.18, -26.67], [128.21, -26.54], [128.23, -26.46], [128.18, -26.35], [128.1, -26.25], [128.04, -26.39], [127.93, -26.59], [127.82, -26.8], [127.75, -26.92], [127.72, -27.03]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, 0.01], [-0.02, -0.06], [0.05, -0.01], [0.02, 0.06]], "o": [[0.05, -0.01], [0.02, 0.06], [-0.02, 0.01], [-0.02, -0.06]], "v": [[125.68, -33.97], [125.78, -33.89], [125.72, -33.78], [125.67, -33.86]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.04, 0.01], [-0.01, -0.06], [0.05, -0.01], [0.02, 0.06]], "o": [[0.05, -0.01], [0.02, 0.06], [-0.04, 0.01], [-0.01, -0.06]], "v": [[125.6, -33.69], [125.7, -33.63], [125.64, -33.52], [125.56, -33.59]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.04, 0.04], [0.01, 0.02], [0.02, 0.06], [0.01, 0.02], [-0.09, 0.02], [-0.02, 0.01], [-0.03, 0.03], [0, -0.02], [0.07, -0.05], [0.01, 0.01], [0, 0], [0, 0], [-0.06, -0.02], [-0.04, -0.02], [-0.01, -0.01], [0.03, -0.03], [0, 0], [0, 0], [0.02, -0.05], [0.01, 0], [0.06, 0.01], [0.01, 0]], "o": [[0, -0.01], [-0.04, -0.04], [-0.02, -0.02], [-0.02, -0.06], [0.02, 0], [0.09, -0.02], [0.02, 0], [0.03, -0.03], [0.07, 0.1], [-0.07, 0.05], [0, 0], [0, 0], [0.07, 0.02], [0.06, 0.01], [0.04, 0.02], [-0.04, 0.03], [0, 0], [0, 0], [0, 0.03], [-0.02, 0.05], [-0.02, 0.01], [-0.06, -0.01], [0, 0]], "v": [[127.42, -26.25], [127.36, -26.32], [127.3, -26.41], [127.24, -26.53], [127.2, -26.65], [127.37, -26.68], [127.53, -26.72], [127.61, -26.76], [127.67, -26.77], [127.67, -26.54], [127.55, -26.47], [127.54, -26.42], [127.58, -26.36], [127.77, -26.31], [127.91, -26.27], [127.99, -26.23], [127.9, -26.14], [127.82, -26.06], [127.83, -26.03], [127.8, -25.9], [127.76, -25.83], [127.64, -25.83], [127.53, -25.83]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.05, 0.01], [-0.01, -0.05], [0.03, -0.01], [0.01, 0.05]], "o": [[0.03, -0.01], [0.01, 0.05], [-0.05, 0.01], [-0.01, -0.05]], "v": [[127.48, -24.72], [127.55, -24.66], [127.52, -24.57], [127.42, -24.62]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[127.02, -25.47], [127.08, -25.57], [126.97, -25.42]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[126.79, -25.41], [126.83, -25.5], [126.75, -25.57]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.03, -0.02], [-0.01, -0.01], [-0.02, 0.1], [-0.01, 0.05], [0, 0], [0, 0], [0.03, 0.01], [0.01, 0.01], [0.02, -0.09], [0.01, -0.04], [0, 0]], "o": [[0, 0.01], [0.03, 0.02], [0.01, -0.02], [0.02, -0.09], [0, 0], [0, 0], [-0.01, -0.01], [-0.03, -0.01], [0, 0.02], [-0.02, 0.09], [0, 0], [0, 0]], "v": [[124.63, -33.45], [124.68, -33.4], [124.75, -33.36], [124.79, -33.54], [124.83, -33.75], [124.82, -33.78], [124.82, -33.81], [124.75, -33.84], [124.69, -33.86], [124.66, -33.69], [124.61, -33.49], [124.62, -33.46]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.03, 0.01], [-0.01, -0.06], [0.05, -0.01], [0.02, 0.06]], "o": [[0.05, -0.01], [0.02, 0.06], [-0.03, 0.01], [-0.01, -0.06]], "v": [[126.14, -26.18], [126.24, -26.11], [126.18, -26], [126.12, -26.08]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, 0], [-0.01, -0.04], [-0.02, 0.02], [0.01, 0.02]], "o": [[-0.02, 0.01], [0.01, 0.04], [0.03, -0.02], [0, -0.02]], "v": [[124.08, -33.56], [124.07, -33.49], [124.11, -33.46], [124.14, -33.52]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [-0.06, 0.08], [-0.01, 0.04], [-0.01, 0.03], [-0.01, 0.01], [0, 0.05], [0, 0.01], [0.04, 0.03], [0.03, 0.04], [0, 0.03], [-0.07, 0.09], [-0.13, 0.11], [0, 0], [-0.06, 0.04], [-0.03, 0.03], [-0.03, 0.08], [-0.01, 0.01], [0, 0.04], [0.01, 0.02], [0, 0], [0, 0], [0.02, -0.07], [0, -0.02], [-0.08, 0], [0, 0], [0, 0], [-0.01, 0.02], [-0.01, 0.03], [-0.01, 0.04], [0, 0.01], [-0.03, -0.03], [-0.02, -0.02], [-0.04, 0.01], [-0.06, 0.05], [-0.01, 0], [-0.03, -0.04], [-0.01, -0.01], [0, 0], [0, 0], [-0.07, 0.08], [-0.02, 0.01], [-0.14, -0.02], [-0.04, -0.02], [0, 0], [-0.01, -0.08], [-0.01, -0.09], [-0.01, -0.07], [-0.01, -0.03], [0, -0.05], [0.02, -0.02], [0.03, 0.01], [0.01, 0.02], [0.01, 0.07], [0.01, 0.02], [0.04, 0.08], [0.01, 0.02], [0.03, 0.04], [0.02, 0.01], [0, 0], [0, 0], [0, -0.07], [0.02, -0.02], [0.05, -0.08], [0.01, 0], [0, 0], [0, 0], [-0.02, -0.02], [-0.01, 0], [0.03, -0.05], [0.02, -0.03], [0, 0], [0, 0], [0.06, 0.03], [0.01, 0.01], [0.04, -0.03], [0, -0.01], [0.02, -0.08], [0.01, -0.02], [0, 0], [0, 0], [0.02, 0.03], [0.01, 0], [0.09, -0.03], [0.01, 0.01], [0.04, 0], [0.03, 0.02], [0.03, 0.02]], "o": [[0.03, -0.02], [0.06, -0.08], [0.02, 0], [0.01, -0.02], [0.02, -0.02], [0, -0.05], [-0.04, -0.04], [-0.03, -0.04], [-0.03, -0.04], [-0.13, 0.1], [0.07, -0.09], [0, 0], [-0.01, 0], [0.03, -0.02], [0, -0.02], [0.03, -0.07], [0, -0.02], [0, -0.04], [0, 0], [0, 0], [0, 0.02], [-0.01, 0.07], [0.01, 0.04], [0, 0], [0, 0], [0.01, -0.01], [0.01, -0.02], [0, -0.02], [0.01, -0.04], [0.01, 0.01], [0.03, 0.03], [0.02, 0.06], [0.02, 0], [0.06, -0.05], [0.01, 0.02], [0.03, 0.04], [0, 0], [0, 0], [0.03, -0.02], [0.07, -0.07], [0.05, 0.01], [0.14, 0.03], [0, 0], [0.01, 0.03], [0.02, 0.08], [0.01, 0.09], [0.01, 0.08], [-0.01, 0.01], [0, 0.05], [0, -0.01], [-0.03, -0.01], [-0.01, -0.01], [-0.01, -0.07], [-0.01, -0.01], [-0.04, -0.08], [-0.02, -0.02], [-0.03, -0.04], [0, 0], [0, 0], [0, 0.02], [0, 0.07], [0, 0.02], [-0.05, 0.09], [0, 0], [0, 0], [0.01, 0.01], [0.02, 0.02], [-0.01, 0.03], [-0.03, 0.05], [0, 0], [0, 0], [-0.02, -0.02], [-0.06, -0.03], [-0.01, 0], [-0.04, 0.03], [-0.01, 0.03], [-0.02, 0.08], [0, 0], [0, 0], [-0.01, 0], [-0.02, -0.03], [-0.02, 0], [-0.09, 0.03], [-0.01, 0], [-0.04, 0], [-0.01, -0.01], [-0.03, -0.01]], "v": [[124.11, -32.8], [124.25, -32.94], [124.36, -33.11], [124.41, -33.16], [124.44, -33.21], [124.47, -33.31], [124.47, -33.41], [124.34, -33.52], [124.25, -33.63], [124.2, -33.74], [124.11, -33.72], [124.4, -34.02], [124.12, -34.06], [124.19, -34.11], [124.27, -34.19], [124.31, -34.33], [124.37, -34.46], [124.36, -34.55], [124.35, -34.64], [124.59, -34.7], [124.6, -34.64], [124.57, -34.5], [124.56, -34.36], [124.69, -34.29], [124.71, -34.3], [124.76, -34.31], [124.78, -34.36], [124.81, -34.42], [124.84, -34.51], [124.85, -34.58], [124.91, -34.53], [124.98, -34.47], [125.07, -34.39], [125.19, -34.47], [125.29, -34.55], [125.35, -34.46], [125.42, -34.4], [125.49, -34.42], [125.56, -34.43], [125.71, -34.57], [125.86, -34.69], [126.13, -34.64], [126.4, -34.57], [126, -34.46], [126.03, -34.3], [126.07, -34.05], [126.1, -33.81], [126.12, -33.65], [126.11, -33.55], [126.07, -33.45], [126.03, -33.48], [125.99, -33.53], [125.95, -33.64], [125.92, -33.78], [125.84, -33.91], [125.77, -34.05], [125.7, -34.14], [125.61, -34.21], [125.58, -34.21], [125.57, -34.2], [125.56, -34.06], [125.54, -33.93], [125.45, -33.76], [125.36, -33.63], [125.4, -33.48], [125.45, -33.29], [125.5, -33.25], [125.55, -33.23], [125.49, -33.13], [125.42, -33.02], [125.37, -33], [125.36, -33], [125.25, -33.07], [125.14, -33.13], [125.07, -33.08], [125.02, -33.03], [124.96, -32.87], [124.91, -32.72], [124.87, -32.71], [124.82, -32.69], [124.77, -32.73], [124.72, -32.77], [124.55, -32.73], [124.4, -32.69], [124.32, -32.68], [124.22, -32.71], [124.16, -32.74]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, -0.02], [0.02, -0.03], [0.04, 0.01], [-0.03, 0.04]], "o": [[0.03, 0.02], [-0.02, 0.03], [-0.03, 0], [0.03, -0.03]], "v": [[125.91, -26.12], [125.92, -26.05], [125.84, -26.03], [125.84, -26.09]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.05], [-0.04, 0.01], [0.01, 0.05]], "o": [[-0.04, 0.01], [0.01, 0.05], [0.04, -0.01], [-0.01, -0.05]], "v": [[126.18, -24.39], [126.14, -24.3], [126.22, -24.23], [126.27, -24.33]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, 0.03], [-0.01, 0], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0, 0]], "o": [[0.01, 0], [0.01, -0.03], [0, 0], [0, -0.01], [0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[125.9, -25.18], [125.93, -25.23], [125.95, -25.28], [125.95, -25.29], [125.95, -25.31], [125.96, -25.33], [125.95, -25.36], [125.86, -25.34]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.03, 0.02], [0, 0]], "o": [[0, 0], [-0.01, -0.03], [0, 0], [0, 0]], "v": [[125.33, -23.75], [125.41, -23.77], [125.36, -23.84], [125.29, -23.91]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.05, 0.01], [-0.01, -0.06], [0.02, 0], [0.02, 0.06]], "o": [[0.02, 0], [0.02, 0.06], [-0.05, 0.01], [-0.01, -0.06]], "v": [[123.93, -29.22], [123.98, -29.14], [123.97, -29.05], [123.87, -29.12]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.05, -0.01], [-0.01, -0.05], [-0.03, 0.01], [0.01, 0.05]], "o": [[-0.03, 0.01], [0.01, 0.05], [0.05, -0.01], [-0.01, -0.05]], "v": [[122.63, -33.5], [122.61, -33.41], [122.67, -33.34], [122.73, -33.44]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, 0], [-0.01, -0.04], [0.02, -0.03], [0.02, 0.07]], "o": [[0.03, 0.02], [0.01, 0.04], [-0.02, 0.02], [-0.02, -0.07]], "v": [[123.61, -28.71], [123.66, -28.63], [123.66, -28.54], [123.6, -28.61]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.02, 0], [0, -0.01], [0.02, -0.02], [0.01, 0.04]], "o": [[0.02, 0.01], [0, 0.02], [-0.02, 0.02], [-0.01, -0.04]], "v": [[123.51, -29.11], [123.55, -29.08], [123.53, -29.04], [123.49, -29.07]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.03, 0.01], [-0.01, -0.06], [0.05, -0.01], [0.01, 0.06]], "o": [[0.05, -0.01], [0.01, 0.06], [-0.03, 0.01], [-0.01, -0.06]], "v": [[122.57, -32.05], [122.67, -31.99], [122.61, -31.88], [122.55, -31.95]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[123.32, -29.15], [123.25, -29.13], [123.23, -29.21]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.03], [0.04, -0.01], [-0.02, -0.02], [-0.02, 0.01]], "o": [[0.01, -0.02], [-0.04, 0.01], [0.01, 0.03], [0.02, 0]], "v": [[122.18, -33.33], [122.13, -33.35], [122.1, -33.31], [122.14, -33.27]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, 0], [-0.02, 0], [-0.01, -0.01], [-0.01, 0.04], [0.01, 0.03], [0.06, -0.02], [0.01, 0], [0.01, -0.01], [0, 0], [0, 0]], "o": [[0, 0.01], [0, 0.01], [0.02, 0.01], [0.02, -0.02], [0.01, -0.04], [-0.01, -0.04], [-0.01, 0.01], [-0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[122.18, -32.2], [122.2, -32.19], [122.24, -32.17], [122.3, -32.15], [122.34, -32.24], [122.35, -32.34], [122.24, -32.38], [122.22, -32.36], [122.2, -32.34], [122.17, -32.3], [122.18, -32.23]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0.01, 0.04], [0.01, 0.02], [-0.09, -0.04], [-0.03, -0.01], [-0.05, -0.1], [0, -0.02], [0.07, -0.01], [0.02, 0.01], [0.03, 0.04], [0, 0.01], [0.01, 0.01], [0, 0.01], [0.01, 0.04]], "o": [[0, -0.01], [-0.01, -0.04], [0.05, 0.01], [0.09, 0.04], [0.02, 0.02], [0.05, 0.1], [-0.01, 0], [-0.07, 0.01], [-0.02, -0.02], [-0.02, -0.04], [-0.02, 0], [-0.03, 0], [0, -0.02], [-0.01, -0.03]], "v": [[122.9, -28.77], [122.88, -28.85], [122.87, -28.95], [123.08, -28.88], [123.25, -28.8], [123.35, -28.62], [123.43, -28.43], [123.31, -28.41], [123.17, -28.41], [123.11, -28.5], [123.06, -28.57], [123.01, -28.59], [122.95, -28.61], [122.93, -28.69]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.04, 0.01], [-0.01, -0.06], [0.05, -0.01], [0.02, 0.06]], "o": [[0.05, -0.01], [0.02, 0.06], [-0.04, 0.01], [-0.01, -0.06]], "v": [[123.11, -27.98], [123.21, -27.91], [123.16, -27.81], [123.07, -27.88]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.03, -0.01], [-0.01, -0.01], [-0.01, 0.05], [0, 0], [0, 0.01], [0, 0.01], [0, 0], [0.02, -0.02], [0, -0.02], [0, 0]], "o": [[0.01, 0.02], [0.03, 0.02], [0.02, -0.04], [0, 0], [0, -0.02], [0, -0.01], [0, 0], [-0.01, 0.01], [-0.02, 0.02], [0, 0], [0, 0]], "v": [[121.61, -33.19], [121.66, -33.13], [121.72, -33.1], [121.77, -33.23], [121.75, -33.29], [121.75, -33.34], [121.75, -33.37], [121.66, -33.35], [121.62, -33.3], [121.6, -33.24], [121.61, -33.21]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[121.97, -27.69], [122.07, -27.71], [122.03, -27.85], [121.94, -27.83]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.03, -0.01], [-0.01, -0.06], [-0.04, 0.01], [0.02, 0.06]], "o": [[-0.04, 0.01], [0.02, 0.06], [0.03, -0.01], [-0.01, -0.06]], "v": [[122.66, -24.77], [122.62, -24.67], [122.7, -24.6], [122.73, -24.69]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.05], [-0.05, 0.01], [0.01, 0.05]], "o": [[-0.05, 0.01], [0.01, 0.05], [0.04, -0.01], [-0.01, -0.05]], "v": [[119.39, -32.5], [119.33, -32.41], [119.43, -32.36], [119.47, -32.45]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.04, 0.01], [0, -0.02], [0.02, -0.01], [0.01, 0.04]], "o": [[0.02, 0], [0.01, 0.02], [-0.04, 0.03], [-0.01, -0.04]], "v": [[119.01, -33.02], [119.05, -33], [119.03, -32.94], [118.96, -32.95]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.01, 0.03], [-0.01, 0.02], [0, 0], [0, 0], [0.03, 0.01], [0.02, 0.02]], "o": [[0.01, -0.01], [0.02, -0.03], [0, 0], [0, 0], [-0.01, -0.01], [-0.02, -0.01], [0, 0]], "v": [[118.98, -31.37], [119.02, -31.43], [119.05, -31.5], [119.03, -31.56], [119.01, -31.64], [118.96, -31.67], [118.9, -31.71]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[118.91, -32.03], [118.89, -32.12], [118.8, -32.1]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.06], [-0.04, 0.01], [0.02, 0.06]], "o": [[-0.04, 0.01], [0.02, 0.06], [0.04, -0.01], [-0.01, -0.06]], "v": [[119, -28.64], [118.96, -28.54], [119.04, -28.47], [119.09, -28.57]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.04, -0.01], [-0.01, -0.05], [-0.04, 0.01], [0.01, 0.05]], "o": [[-0.04, 0.01], [0.01, 0.05], [0.04, -0.01], [-0.01, -0.05]], "v": [[120.08, -22.74], [120.04, -22.65], [120.12, -22.58], [120.16, -22.68]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.02], [0.04, -0.01], [-0.01, -0.03], [-0.03, 0.01]], "o": [[0, -0.04], [-0.04, 0.01], [0.02, 0.02], [0.03, -0.01]], "v": [[119.61, -23.99], [119.55, -24.02], [119.5, -23.96], [119.57, -23.94]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.01, 0.02], [0, 0.02], [0, 0.01], [-0.1, -0.04], [-0.03, -0.01], [-0.04, -0.05], [0, -0.02], [0, 0], [0, 0], [0, 0], [0, 0.01], [0.02, 0.02], [0.02, 0.02], [0.04, 0.04], [0.01, 0.01], [0.06, 0.04], [0, 0.01], [0, 0.01]], "o": [[0, -0.02], [0.01, -0.02], [0.03, 0.02], [0.1, 0.04], [0.02, 0.02], [0.04, 0.05], [0, 0], [0, 0], [0, 0], [-0.01, 0], [0, -0.01], [-0.02, -0.02], [-0.01, 0], [-0.04, -0.04], [0, -0.01], [-0.06, -0.04], [-0.01, 0], [-0.01, -0.01]], "v": [[117.39, -31.47], [117.39, -31.54], [117.4, -31.59], [117.58, -31.51], [117.77, -31.42], [117.85, -31.32], [117.91, -31.21], [117.94, -31.08], [117.91, -31.07], [117.88, -31.07], [117.86, -31.08], [117.82, -31.12], [117.77, -31.17], [117.7, -31.22], [117.62, -31.28], [117.53, -31.35], [117.44, -31.42], [117.42, -31.43]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.01], [-0.03, 0.07], [-0.02, 0.02], [-0.04, -0.08], [-0.03, -0.03], [0, 0], [0.05, 0.01], [0.05, 0.03]], "o": [[0, -0.02], [0.03, -0.07], [0.03, 0.02], [0.04, 0.08], [0, 0], [-0.04, 0.01], [-0.02, 0], [-0.05, -0.03]], "v": [[117.06, -30.81], [117.09, -30.95], [117.16, -31.08], [117.25, -30.94], [117.35, -30.78], [117.39, -30.69], [117.26, -30.69], [117.15, -30.75]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.03, -0.02], [0.02, 0], [0.02, 0.04], [0.01, 0.01], [0, 0], [0, 0], [0.04, -0.03], [0.01, -0.01], [0, 0], [0, 0], [0.02, -0.03], [0.01, -0.01], [0.01, -0.02], [0, -0.01], [0.01, -0.01], [0, -0.01], [0.08, -0.03], [0.02, 0], [0.08, -0.01], [0.02, 0], [0.1, 0.04], [0.03, 0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.06, -0.04], [0.01, 0], [0.01, -0.05], [0, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0.02, 0.01], [0.03, 0.02], [0.01, 0], [0, 0], [0, 0], [0.05, -0.07], [0.02, 0], [0, 0], [0, 0], [0.12, 0.01], [0.04, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.02], [0, 0], [0, 0], [0.02, 0.01], [0.01, 0.01], [0.04, -0.01], [0.02, 0], [0.02, 0.07], [-0.01, 0.03], [0, 0.06], [0.01, 0.06], [0.01, 0.05], [0, 0.01], [0.02, 0.07], [0.02, 0.07], [0.02, 0.06], [0.01, 0.02], [0, 0.12], [0.01, 0.03], [0.03, 0.13], [0.02, 0.08], [0.01, 0.05], [0.02, 0.05], [0.01, 0.05], [0.01, 0.02], [0.04, 0.04], [0.01, 0.01], [0, 0.01], [0.01, 0.02], [-0.01, 0.01], [-0.01, 0.01], [0, 0.07], [0.01, 0.02], [0, 0], [0, 0], [0.05, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0.03, 0.01], [0, 0.01], [0.06, 0.01], [0, 0.01], [0, 0], [0, 0], [-0.03, 0.08], [-0.01, 0.03], [-0.04, 0.05], [0, 0.01], [-0.02, 0.05], [-0.01, 0.01], [-0.01, 0.01], [0, 0.01], [0, 0], [-0.02, 0.15], [0.01, 0.03], [0, 0], [0, 0], [0.07, 0.08], [0.03, 0.02], [0.03, 0.05], [0.01, 0.02], [0.03, 0.07], [0.04, 0.07], [0.03, 0.07], [0.01, 0.03], [0, 0], [0, 0], [-0.03, 0.04], [-0.03, 0.05], [-0.02, 0.04], [0, 0.01], [0.01, 0.04], [0.01, 0.01], [-0.08, -0.02], [-0.02, 0.01], [-0.03, 0.05], [-0.01, 0.04], [-0.01, 0.04], [0.01, 0.02], [-0.02, -0.03], [-0.02, 0], [-0.04, 0.04], [-0.02, 0.01], [0.01, 0.09], [0.02, 0.04], [0.06, 0.08], [0.03, 0.02], [0, 0], [0, 0], [0, 0.07], [0, 0.02], [0.01, 0.1], [0.01, 0.02], [0, 0], [0, 0.06], [0.01, 0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.03, -0.04], [0, -0.01], [-0.09, -0.02], [-0.04, -0.01], [0, 0], [0, 0], [-0.04, 0.04], [-0.04, 0.04], [-0.03, 0.03], [0, 0.01], [-0.04, 0.04], [-0.01, 0], [0, 0], [0, 0], [-0.07, -0.02], [-0.02, -0.02], [-0.03, -0.04], [-0.02, 0], [-0.04, 0], [-0.02, 0.01], [-0.02, 0.06], [0, 0], [-0.01, 0.05], [0, 0.01], [0, 0], [0, 0], [-0.08, -0.05], [-0.02, 0.01], [0, 0], [0, 0], [-0.04, 0.03], [-0.02, 0.01], [-0.06, 0.03], [-0.01, 0.01], [0, 0], [0, 0], [0.03, -0.04], [0.02, 0], [-0.01, -0.01], [-0.01, -0.01], [-0.06, -0.01], [-0.01, 0.01], [-0.03, 0.04], [-0.02, 0.03], [0, 0], [-0.01, 0.01], [0, 0], [0, 0.07], [-0.01, 0.01], [0, 0], [-0.01, 0], [-0.01, 0], [0, 0], [-0.02, -0.02], [-0.01, -0.01], [-0.06, 0.1], [-0.03, 0.03], [-0.03, 0.01], [-0.02, 0.01], [-0.03, 0.01], [0, -0.01], [-0.06, -0.06], [-0.01, -0.01], [-0.07, -0.05], [-0.03, -0.01], [-0.01, 0.04], [0.01, 0.02], [0.03, 0.02], [0.02, 0], [-0.02, 0.02], [-0.01, 0.03], [0, 0], [0, 0], [-0.07, 0], [-0.02, 0], [0, 0], [0, 0], [-0.01, 0.03], [0, 0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.03, -0.07], [0.02, -0.03], [0.01, 0], [0, 0], [0, 0], [0, 0.01], [0.05, 0.01], [0.02, 0], [0, 0], [-0.02, -0.04], [0.01, -0.01], [0.02, -0.03], [0.01, 0], [0.04, -0.01], [0.02, 0], [0.04, 0], [0.01, 0], [0.05, 0.03], [0.01, 0.01], [0.09, 0.02], [0.01, 0], [0.04, -0.08], [0.02, -0.02], [0, 0], [0, 0], [0.07, -0.04], [0.09, -0.04], [0.07, -0.04], [0.02, 0], [0, 0], [0.04, -0.04], [0.01, 0], [0.06, -0.02], [0.06, -0.03], [0.06, -0.02], [0.01, 0], [0.03, -0.02], [0.04, -0.02], [0.04, -0.02], [0.01, 0], [0.05, 0.01], [0.06, 0], [0.05, 0.01], [0.01, 0.01], [0, 0], [0.03, 0.01], [0.01, 0.01], [0.01, -0.01], [0.02, 0], [0, 0], [0.08, -0.02], [0.02, 0.01], [0.02, 0.01], [0.01, 0.02], [0, 0], [0, 0], [0.02, -0.02], [0.02, 0], [0.01, -0.01], [0, 0], [0, 0], [-0.02, -0.01], [0, -0.01], [-0.06, 0], [-0.01, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [-0.03, 0.03], [-0.01, 0.01], [0, 0], [0, 0], [-0.02, -0.01], [0, -0.01], [0, 0], [-0.05, 0], [-0.02, -0.02], [0, 0], [0, 0], [0, -0.09], [0, -0.02], [-0.03, -0.02], [-0.02, -0.02], [-0.02, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [0.04, -0.05], [0, -0.02], [0, 0], [0.03, -0.07], [0.01, -0.02], [0, 0], [0, 0], [0, 0], [-0.04, -0.05], [-0.03, -0.01], [0, 0], [0, 0], [-0.08, 0.04], [-0.02, 0.02], [-0.01, 0.02], [0, 0.02], [-0.01, 0.05], [-0.01, 0.01], [-0.02, -0.02], [-0.01, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0], [-0.02, 0.01], [0, 0], [0, 0.01], [-0.03, 0.06], [-0.02, 0.02], [0, 0], [-0.01, -0.01], [-0.07, -0.03], [-0.01, 0], [-0.05, 0.02], [-0.01, 0], [-0.03, -0.03], [0, -0.02], [0, 0], [-0.01, 0.05], [0.01, 0.02], [0, 0], [-0.03, -0.01], [-0.01, 0], [-0.07, -0.02], [-0.01, -0.01], [-0.02, -0.01], [-0.01, 0], [-0.04, -0.06], [-0.01, -0.01], [-0.04, 0], [-0.03, -0.01], [0, 0], [0, 0], [0.01, -0.02], [0.01, -0.01], [0, -0.06], [0.01, 0], [0.03, 0.01], [0.01, 0], [0.01, -0.06], [-0.02, -0.02], [-0.05, -0.03], [-0.02, 0], [0.03, -0.04], [0.02, 0], [0.06, -0.01], [0.02, 0], [0.04, -0.04], [0.01, 0], [0, 0], [0.08, -0.04], [0.03, -0.02], [0.07, -0.05], [0.08, -0.06], [0.07, -0.05], [0.01, -0.01], [0.09, -0.04], [0.02, -0.02], [0.11, -0.07], [0.02, -0.02], [0.01, -0.05], [-0.01, -0.07], [0.01, -0.02], [0, -0.09], [0.01, -0.02], [0.02, -0.03], [0, -0.01], [-0.01, -0.03], [0, 0], [-0.02, -0.05], [0, -0.02], [-0.04, -0.01], [-0.02, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0.01], [-0.02, 0.03], [-0.01, 0], [0, 0], [0, 0], [-0.07, -0.02], [-0.07, -0.02], [-0.07, -0.02], [-0.02, 0], [-0.04, 0], [0, -0.02], [-0.04, -0.06], [0, -0.02], [-0.02, 0.06], [-0.01, 0], [-0.08, 0], [-0.02, 0.01], [0, 0], [0, 0], [0.07, 0.03], [0.02, 0], [-0.04, 0.04], [-0.02, 0.01], [-0.05, -0.02], [-0.02, 0], [0, 0], [0, 0], [-0.04, 0.03], [-0.01, 0.03], [0, 0], [-0.02, 0], [-0.02, 0.02], [-0.02, 0.06], [-0.02, 0.01], [0, 0], [0, 0], [-0.02, -0.02], [-0.01, -0.01], [0.01, -0.02], [0, -0.02], [0.01, -0.04], [0, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.02], [0.01, 0.09], [-0.01, 0.01], [-0.05, 0.03], [0, 0.01], [0, 0.08], [0.01, 0.02], [0, 0], [0, 0], [0, 0], [-0.02, -0.05], [-0.03, -0.01], [-0.07, -0.02], [-0.03, -0.01], [0, 0], [-0.02, -0.02], [-0.01, -0.01], [0, -0.06], [-0.01, 0], [-0.07, 0], [-0.01, -0.01], [-0.04, -0.08], [-0.02, -0.02], [-0.05, -0.03], [-0.03, -0.01], [0, 0], [0.05, 0.05], [0.03, 0.02], [0, 0], [0, 0], [0.01, 0.11], [-0.01, 0.01], [-0.02, 0.04], [-0.02, 0.03], [-0.03, 0.01], [-0.02, 0.01], [-0.02, -0.05], [0, -0.02], [-0.02, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [-0.03, 0.1], [-0.03, 0.06], [-0.03, -0.02], [0.01, -0.18], [0, 0], [-0.04, 0], [-0.01, -0.06], [0.02, -0.1], [0.04, -0.09], [0, -0.08], [0.02, -0.03], [0.03, -0.05], [0.04, -0.05], [0.03, -0.05], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.06, 0.03], [0.01, 0], [0, 0], [0.06, 0.04], [0.01, 0], [0, 0], [0, 0], [0.09, -0.01], [0.04, -0.01], [0.13, 0.02], [0.06, 0.01], [0.02, 0.02], [0, 0.01], [0.01, 0.05], [0, 0.01], [0.08, 0], [0.04, 0.02], [0.01, 0], [0, 0], [0, 0], [0, -0.08], [0.01, -0.02], [0.01, -0.02], [0.01, 0], [0.07, -0.06], [0.02, -0.02], [0.03, 0.04], [0.02, 0.02], [0.01, 0.01], [0.01, 0.01], [0, 0]], "o": [[0, 0], [0, 0], [-0.02, 0.01], [-0.03, 0.03], [-0.01, 0], [-0.02, -0.04], [0, 0], [0, 0], [-0.01, 0], [-0.04, 0.03], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0.03], [0, 0.03], [-0.01, 0.01], [0, 0.01], [-0.01, 0.01], [-0.02, 0.01], [-0.08, 0.03], [-0.05, 0.01], [-0.08, 0.02], [-0.02, -0.01], [-0.1, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.01], [-0.05, 0.04], [-0.01, 0], [-0.01, 0.05], [0, 0.02], [0, 0], [0, 0], [0, 0], [-0.03, 0], [-0.01, 0], [-0.01, -0.01], [0, 0], [0, 0], [-0.01, 0], [-0.05, 0.07], [0, 0], [0, 0], [-0.02, -0.01], [-0.12, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.01], [0, 0], [0, 0], [-0.02, 0], [-0.02, -0.01], [-0.01, -0.01], [-0.04, 0.01], [0, -0.02], [-0.02, -0.07], [0, -0.02], [0, -0.06], [-0.01, -0.06], [-0.01, -0.05], [0, -0.01], [-0.02, -0.07], [-0.02, -0.07], [-0.02, -0.06], [0, -0.02], [0, -0.12], [0, -0.02], [-0.03, -0.13], [0, -0.01], [-0.01, -0.05], [-0.02, -0.05], [-0.01, -0.05], [-0.02, 0], [-0.04, -0.03], [-0.01, -0.01], [-0.01, -0.01], [-0.01, -0.03], [0, -0.01], [0.01, -0.01], [0, -0.07], [0, 0], [0, 0], [0, -0.02], [-0.05, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [-0.02, -0.01], [-0.01, 0], [-0.06, -0.01], [0, 0], [0, 0], [0, -0.01], [0.03, -0.08], [0, -0.01], [0.04, -0.05], [0.01, -0.02], [0.02, -0.05], [0, -0.01], [0.01, -0.01], [0, 0], [0, -0.03], [0.02, -0.15], [0, 0], [0, 0], [0, -0.02], [-0.07, -0.08], [0, -0.02], [-0.03, -0.05], [0, -0.02], [-0.03, -0.06], [-0.04, -0.07], [-0.03, -0.07], [0, 0], [0, 0], [0.01, -0.01], [0.03, -0.04], [0.03, -0.05], [0.02, -0.04], [0.01, -0.01], [-0.01, -0.04], [0.02, 0.03], [0.08, 0.02], [0.01, 0], [0.03, -0.05], [0.01, -0.02], [0.01, -0.04], [0.01, 0.02], [0.02, 0.03], [0.04, 0], [0.04, -0.04], [0, -0.02], [-0.01, -0.09], [0, -0.01], [-0.06, -0.08], [0, 0], [0, 0], [0.01, -0.02], [0.01, -0.07], [0.01, -0.01], [-0.01, -0.1], [0, 0], [0, -0.03], [0, -0.06], [0, 0], [0, 0], [0, 0], [0, 0.01], [0.03, 0.02], [0.03, 0.04], [0.02, 0], [0.09, 0.02], [0, 0], [0, 0], [0.01, -0.01], [0.04, -0.04], [0.04, -0.04], [0.03, -0.03], [0.02, 0], [0.04, -0.04], [0, 0], [0, 0], [0.04, 0.01], [0.07, 0.02], [0.02, 0], [0.03, 0.04], [0.02, 0.02], [0.04, 0.01], [0.02, 0], [0, 0], [0, -0.02], [0.01, -0.05], [0, 0], [0, 0], [0.03, 0.02], [0.08, 0.05], [0, 0], [0, 0], [0.01, 0], [0.04, -0.03], [0.02, -0.03], [0.06, -0.03], [0, 0], [0, 0], [-0.01, 0.03], [-0.03, 0.05], [0, 0.01], [0, 0.01], [0.02, 0], [0.06, 0.01], [0.02, 0], [0.03, -0.04], [0, 0], [0.01, 0], [0, 0], [0, -0.02], [0, -0.07], [0, 0], [0.01, 0], [0.01, 0], [0, 0], [0.01, 0], [0.02, 0.02], [0.01, -0.05], [0.06, -0.09], [0, -0.01], [0.03, -0.01], [0.01, 0], [0.03, -0.01], [0.02, 0], [0.06, 0.06], [0.03, 0.02], [0.07, 0.05], [0.02, -0.03], [0.01, -0.04], [0, -0.01], [-0.03, -0.01], [0, -0.01], [0.02, -0.02], [0, 0], [0, 0], [0.02, 0.02], [0.07, 0], [0, 0], [0, 0], [0.01, 0], [0.01, -0.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0.01], [-0.03, 0.08], [0, 0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, -0.01], [-0.05, 0], [0, 0], [0, 0.01], [0.02, 0.04], [0.01, 0.02], [-0.01, 0.03], [-0.01, 0], [-0.04, 0.02], [-0.02, 0.01], [-0.04, 0.01], [-0.01, -0.01], [-0.05, -0.03], [-0.06, -0.02], [-0.09, -0.02], [-0.02, 0.01], [-0.04, 0.08], [0, 0], [0, 0], [-0.02, 0.02], [-0.07, 0.04], [-0.09, 0.05], [-0.07, 0.04], [0, 0], [-0.01, 0], [-0.04, 0.04], [-0.02, 0.02], [-0.06, 0.02], [-0.06, 0.03], [-0.06, 0.02], [-0.01, 0], [-0.03, 0.02], [-0.04, 0.02], [-0.04, 0.02], [-0.02, 0.01], [-0.04, 0], [-0.06, 0], [-0.05, -0.01], [0, 0], [0, -0.02], [-0.02, -0.01], [-0.01, -0.01], [-0.01, 0.01], [0, 0], [-0.02, 0.01], [-0.08, 0.02], [-0.01, -0.01], [-0.02, 0], [0, 0], [0, 0], [-0.03, 0.02], [-0.01, 0.01], [-0.01, 0], [0, 0], [0, 0], [0.01, 0.01], [0.03, 0.01], [0.04, 0], [0.06, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0.02, -0.02], [0.03, -0.02], [0, 0], [0, 0], [0.02, 0.01], [0.03, 0.01], [0, 0], [0.01, 0], [0.05, 0.01], [0, 0], [0, 0], [0, 0.03], [0, 0.1], [0, 0.01], [0.03, 0.03], [0.02, 0.02], [0.02, 0.01], [0, 0], [0, 0], [-0.01, 0.01], [-0.04, 0.06], [0, 0], [0, 0.01], [-0.03, 0.07], [0, 0], [0, 0], [0, 0], [0.01, 0.02], [0.04, 0.05], [0, 0], [0, 0], [0.02, 0], [0.08, -0.04], [0, -0.02], [0.01, -0.02], [0.02, -0.02], [0.01, -0.05], [0, 0.01], [0.02, 0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0.02, 0], [0, 0], [0.01, 0], [0.02, 0], [0.03, -0.06], [0, 0], [0.01, 0], [0.02, 0.01], [0.08, 0.03], [0.01, 0.01], [0.05, -0.02], [0.02, 0.02], [0.03, 0.03], [0, 0], [0.01, 0], [0.01, -0.05], [0, 0], [0, 0.01], [0.03, 0.02], [0.03, 0.02], [0.07, 0.03], [0.01, 0.01], [0.03, 0.01], [0.01, 0.02], [0.04, 0.06], [0, 0.01], [0.05, 0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0.02], [-0.01, 0.01], [0, 0.06], [-0.02, 0.02], [-0.03, -0.01], [0, 0.01], [-0.01, 0.06], [0.01, 0.01], [0.05, 0.03], [-0.02, 0.02], [-0.03, 0.04], [-0.01, 0.01], [-0.06, 0.01], [-0.01, 0], [-0.04, 0.04], [0, 0], [-0.02, 0.01], [-0.08, 0.04], [-0.02, 0.02], [-0.07, 0.05], [-0.08, 0.06], [-0.07, 0.05], [-0.02, 0.02], [-0.09, 0.04], [-0.05, 0.02], [-0.11, 0.07], [-0.04, 0.03], [-0.01, 0], [0.01, 0.07], [0.01, 0.02], [0, 0.1], [-0.01, 0.01], [-0.02, 0.03], [0, 0.02], [0, 0], [0.01, 0.02], [0.02, 0.05], [0.02, 0], [0.04, 0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0.01, 0], [0.02, -0.03], [0, 0], [0, 0], [0.01, 0], [0.07, 0.02], [0.07, 0.02], [0.06, 0.02], [0.01, 0.01], [0.05, 0.01], [0.02, 0.01], [0.04, 0.06], [0.01, -0.02], [0.03, -0.06], [0.04, -0.02], [0.08, 0], [0, 0], [0, 0], [-0.02, 0], [-0.07, -0.02], [0.01, -0.02], [0.04, -0.04], [0.01, 0], [0.05, 0.02], [0, 0], [0, 0], [0.02, -0.02], [0.04, -0.03], [0, 0], [0.01, 0.01], [0.04, 0.01], [0.01, -0.01], [0.02, -0.06], [0, 0], [0, 0], [0.02, 0], [0.02, 0.02], [-0.01, 0.01], [-0.01, 0.02], [-0.01, 0.01], [-0.01, 0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.01], [0.01, -0.02], [-0.01, -0.09], [0.01, -0.02], [0.05, -0.03], [0, -0.02], [0, -0.08], [0, 0], [0, 0], [0, 0], [0.01, 0.02], [0.03, 0.06], [0.02, 0.02], [0.07, 0.02], [0, 0], [0.01, 0], [0.02, 0.02], [0.01, 0.01], [0, 0.05], [0.01, 0], [0.07, 0], [0.01, 0.02], [0.04, 0.08], [0.02, 0.02], [0.05, 0.03], [0, 0], [-0.03, -0.01], [-0.05, -0.05], [0, 0], [0, 0], [0.01, -0.07], [-0.01, -0.11], [0.02, -0.03], [0.02, -0.04], [0.01, -0.01], [0.03, -0.01], [0, 0.01], [0.02, 0.05], [0, 0.01], [0.02, 0.01], [0, 0], [0, 0], [0.01, -0.05], [0.03, -0.1], [0.03, -0.06], [0.03, 0.02], [0, 0], [0.05, -0.09], [0.04, 0], [0.02, 0.06], [-0.02, 0.1], [0, 0.03], [0, 0.08], [0, 0.02], [-0.03, 0.05], [-0.04, 0.05], [-0.03, 0.05], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.02], [-0.06, -0.04], [0, 0], [-0.02, 0.01], [-0.05, -0.04], [0, 0], [0, 0], [-0.02, 0.01], [-0.08, 0.01], [-0.02, 0.01], [-0.13, -0.02], [-0.01, 0], [-0.02, -0.02], [-0.01, 0], [-0.01, -0.05], [-0.01, -0.03], [-0.02, 0.01], [-0.04, -0.02], [0, 0], [0, 0], [0.01, 0.03], [0, 0.08], [-0.01, 0.01], [0, 0.02], [-0.04, 0.03], [-0.07, 0.06], [-0.01, -0.01], [-0.03, -0.04], [-0.01, -0.01], [-0.01, -0.01], [0, 0], [0, 0]], "v": [[123.76, -23.44], [123.63, -23.32], [123.58, -23.52], [123.5, -23.48], [123.42, -23.43], [123.37, -23.49], [123.31, -23.57], [123.27, -23.56], [123.22, -23.55], [123.14, -23.49], [123.07, -23.42], [123.01, -23.41], [122.93, -23.39], [122.89, -23.35], [122.85, -23.29], [122.84, -23.2], [122.83, -23.17], [122.82, -23.15], [122.82, -23.11], [122.66, -23.06], [122.51, -23.01], [122.31, -22.97], [122.16, -22.94], [121.97, -23], [121.77, -23.09], [121.83, -22.86], [121.36, -22.82], [121.3, -23.05], [121.26, -23.08], [121.2, -23.11], [121.15, -23.1], [121.11, -23.09], [120.99, -23.02], [120.89, -22.95], [120.86, -22.87], [120.84, -22.77], [120.82, -22.72], [120.8, -22.68], [120.75, -22.66], [120.7, -22.65], [120.62, -22.66], [120.56, -22.68], [120.53, -22.69], [120.49, -22.68], [120.45, -22.67], [120.35, -22.56], [120.24, -22.45], [120.16, -22.43], [120.08, -22.41], [119.87, -22.43], [119.64, -22.46], [119.61, -22.57], [119.58, -22.7], [119.59, -22.72], [119.61, -22.75], [119.65, -22.8], [119.63, -22.89], [119.61, -22.97], [119.54, -22.99], [119.49, -23.02], [119.41, -23.03], [119.31, -23.01], [119.28, -23.15], [119.27, -23.3], [119.26, -23.42], [119.23, -23.61], [119.21, -23.78], [119.19, -23.88], [119.16, -24], [119.11, -24.21], [119.05, -24.41], [119.02, -24.53], [119.01, -24.74], [118.99, -24.96], [118.95, -25.18], [118.87, -25.5], [118.85, -25.58], [118.81, -25.73], [118.76, -25.88], [118.73, -25.99], [118.63, -26.05], [118.55, -26.11], [118.53, -26.14], [118.5, -26.18], [118.5, -26.25], [118.51, -26.28], [118.52, -26.41], [118.51, -26.55], [118.49, -26.6], [118.49, -26.61], [118.4, -26.65], [118.3, -26.65], [118.22, -26.29], [118.21, -26.29], [118.14, -26.27], [118.1, -26.3], [118.06, -26.34], [117.96, -26.35], [117.86, -26.38], [117.84, -26.49], [117.8, -26.62], [117.84, -26.75], [117.91, -26.91], [117.96, -27], [118.02, -27.09], [118.05, -27.2], [118.08, -27.29], [118.1, -27.33], [118.11, -27.36], [117.94, -28.02], [117.97, -28.29], [117.99, -28.56], [117.98, -28.6], [117.97, -28.63], [117.86, -28.77], [117.72, -28.91], [117.66, -29.02], [117.61, -29.13], [117.56, -29.26], [117.46, -29.47], [117.37, -29.69], [117.31, -29.84], [117.28, -29.98], [117.25, -30.07], [117.31, -30.15], [117.39, -30.28], [117.47, -30.41], [117.5, -30.49], [117.49, -30.57], [117.46, -30.64], [117.6, -30.57], [117.75, -30.55], [117.81, -30.62], [117.88, -30.75], [117.9, -30.84], [117.9, -30.92], [117.95, -30.84], [118.02, -30.79], [118.12, -30.84], [118.21, -30.9], [118.19, -31.07], [118.14, -31.27], [118.04, -31.4], [117.91, -31.54], [117.9, -31.57], [117.89, -31.6], [117.91, -31.74], [117.92, -31.88], [117.92, -32.05], [117.9, -32.24], [117.98, -32.31], [117.98, -32.45], [117.96, -32.57], [118.05, -32.59], [118.06, -32.56], [118.07, -32.53], [118.09, -32.52], [118.17, -32.44], [118.21, -32.37], [118.38, -32.35], [118.58, -32.29], [118.61, -32.3], [118.67, -32.32], [118.74, -32.4], [118.84, -32.5], [118.94, -32.6], [118.99, -32.66], [119.07, -32.73], [119.14, -32.79], [119.18, -32.8], [119.21, -32.81], [119.37, -32.76], [119.49, -32.7], [119.57, -32.64], [119.64, -32.57], [119.73, -32.53], [119.82, -32.53], [119.88, -32.63], [119.84, -32.78], [119.84, -32.88], [119.86, -32.97], [119.9, -33], [119.95, -32.99], [120.11, -32.9], [120.25, -32.84], [120.29, -32.83], [120.32, -32.86], [120.39, -32.9], [120.48, -32.95], [120.59, -33.03], [120.69, -33.1], [120.77, -33.12], [120.86, -33.15], [120.79, -33.04], [120.72, -32.96], [120.75, -32.93], [120.77, -32.91], [120.89, -32.9], [120.99, -32.91], [121.06, -32.97], [121.13, -33.06], [121.14, -33.07], [121.17, -33.09], [121.2, -33.13], [121.2, -33.27], [121.21, -33.4], [121.22, -33.42], [121.25, -33.42], [121.29, -33.42], [121.3, -33.43], [121.34, -33.4], [121.39, -33.36], [121.5, -33.58], [121.63, -33.76], [121.68, -33.79], [121.75, -33.81], [121.82, -33.82], [121.87, -33.82], [121.99, -33.74], [122.1, -33.63], [122.25, -33.53], [122.4, -33.44], [122.44, -33.54], [122.45, -33.64], [122.4, -33.67], [122.33, -33.69], [122.36, -33.74], [122.4, -33.81], [122.44, -33.82], [122.48, -33.83], [122.61, -33.8], [122.76, -33.8], [122.82, -33.81], [122.91, -33.84], [122.94, -33.88], [122.97, -33.95], [123, -33.96], [123.03, -33.97], [123.47, -33.68], [123.99, -34.11], [124.35, -33.16], [124.02, -32.84], [123.99, -32.72], [123.92, -32.57], [123.91, -32.55], [123.88, -32.54], [123.85, -32.53], [123.83, -32.54], [123.74, -32.56], [123.64, -32.56], [123.59, -32.48], [123.62, -32.41], [123.63, -32.33], [123.62, -32.26], [123.58, -32.21], [123.5, -32.18], [123.41, -32.15], [123.31, -32.14], [123.23, -32.12], [123.14, -32.18], [123.05, -32.24], [122.83, -32.29], [122.69, -32.32], [122.59, -32.18], [122.51, -32.04], [122.46, -32.02], [122.43, -32.02], [122.29, -31.94], [122.05, -31.82], [121.8, -31.7], [121.66, -31.64], [121.43, -31.58], [121.36, -31.52], [121.29, -31.46], [121.18, -31.41], [121.01, -31.34], [120.83, -31.27], [120.73, -31.23], [120.67, -31.2], [120.55, -31.13], [120.42, -31.07], [120.35, -31.04], [120.25, -31.03], [120.1, -31.05], [119.94, -31.06], [119.84, -31.09], [119.76, -31.4], [119.71, -31.45], [119.66, -31.47], [119.62, -31.46], [119.57, -31.43], [119.7, -30.93], [119.54, -30.9], [119.37, -30.88], [119.32, -30.9], [119.28, -30.94], [119.24, -30.75], [118.18, -30.48], [118.11, -30.43], [118.07, -30.4], [118.04, -30.37], [118.06, -30.3], [118.08, -30.2], [118.14, -30.18], [118.18, -30.15], [118.32, -30.15], [118.43, -30.16], [118.52, -29.82], [118.54, -29.8], [118.57, -29.81], [118.61, -29.82], [118.69, -29.88], [118.75, -29.94], [118.81, -29.96], [118.83, -29.96], [118.9, -29.94], [118.94, -29.91], [119.05, -29.5], [119.13, -29.5], [119.22, -29.46], [119.23, -29.45], [119.24, -29.4], [119.23, -29.21], [119.23, -29.03], [119.29, -28.98], [119.36, -28.92], [119.42, -28.87], [119.46, -28.84], [119.47, -28.81], [119.48, -28.76], [119.41, -28.66], [119.36, -28.55], [119.44, -28.24], [119.4, -28.11], [119.34, -27.96], [119.37, -27.87], [119.38, -27.81], [119.52, -27.91], [119.58, -27.8], [119.67, -27.7], [119.74, -27.71], [119.85, -27.74], [120, -27.81], [120.14, -27.88], [120.15, -27.95], [120.18, -28.01], [120.22, -28.1], [120.25, -28.19], [120.28, -28.15], [120.33, -28.12], [120.58, -28.18], [120.62, -28.16], [120.66, -28.13], [120.72, -28.14], [120.78, -28.15], [120.81, -28.16], [120.82, -28.17], [120.89, -28.27], [120.97, -28.38], [120.99, -28.4], [121.03, -28.39], [121.17, -28.34], [121.3, -28.3], [121.39, -28.31], [121.47, -28.34], [121.54, -28.27], [121.6, -28.19], [122.25, -28.36], [122.28, -28.44], [122.29, -28.55], [122.21, -28.86], [122.25, -28.82], [122.3, -28.81], [122.45, -28.74], [122.58, -28.69], [122.64, -28.67], [122.69, -28.65], [122.76, -28.53], [122.84, -28.43], [122.91, -28.4], [123.01, -28.37], [123.02, -28.34], [123.03, -28.29], [123, -28.26], [122.97, -28.21], [122.96, -28.11], [122.94, -28.02], [122.86, -28.02], [122.8, -28.03], [122.8, -27.92], [122.81, -27.8], [122.91, -27.75], [123.02, -27.71], [122.95, -27.63], [122.87, -27.57], [122.76, -27.53], [122.64, -27.51], [122.57, -27.45], [122.5, -27.39], [122.17, -27.31], [122.02, -27.24], [121.87, -27.14], [121.73, -27.04], [121.51, -26.88], [121.28, -26.71], [121.17, -26.62], [121.01, -26.53], [120.84, -26.45], [120.61, -26.31], [120.42, -26.17], [120.35, -26.06], [120.34, -25.95], [120.34, -25.82], [120.34, -25.65], [120.33, -25.47], [120.3, -25.41], [120.28, -25.36], [120.28, -25.27], [120.31, -25.16], [120.34, -25.06], [120.38, -24.97], [120.47, -24.96], [120.57, -24.93], [120.86, -24.76], [120.93, -24.76], [120.97, -24.77], [121, -24.79], [121.05, -24.84], [121.1, -24.88], [121.13, -24.89], [121.16, -24.9], [121.27, -24.88], [121.48, -24.82], [121.68, -24.77], [121.82, -24.73], [121.9, -24.72], [121.98, -24.68], [122.07, -24.58], [122.14, -24.45], [122.18, -24.57], [122.24, -24.66], [122.41, -24.7], [122.56, -24.71], [122.56, -24.82], [122.44, -24.9], [122.3, -24.94], [122.15, -24.97], [122.22, -25.06], [122.3, -25.12], [122.39, -25.1], [122.5, -25.06], [122.53, -25.07], [122.58, -25.08], [122.67, -25.15], [122.75, -25.24], [122.83, -24.94], [122.88, -24.93], [122.97, -24.93], [123.01, -25.04], [123.08, -25.14], [123.16, -25.16], [123.23, -25.18], [123.29, -25.16], [123.33, -25.12], [123.3, -25.07], [123.28, -25.01], [123.25, -24.93], [123.24, -24.85], [123.26, -24.74], [123.28, -24.68], [123.38, -24.62], [123.42, -24.63], [123.46, -24.64], [123.49, -24.7], [123.53, -24.74], [123.52, -24.91], [123.51, -25.07], [123.59, -25.15], [123.66, -25.21], [123.67, -25.36], [123.67, -25.51], [124.38, -25.69], [124.5, -25.94], [125.16, -26.11], [125.21, -25.99], [125.28, -25.89], [125.41, -25.83], [125.56, -25.78], [125.73, -25.82], [125.78, -25.8], [125.83, -25.76], [125.85, -25.67], [125.87, -25.59], [126, -25.6], [126.13, -25.59], [126.21, -25.44], [126.3, -25.29], [126.4, -25.22], [126.5, -25.15], [126.56, -25.25], [126.44, -25.35], [126.32, -25.46], [126.32, -25.49], [126.31, -25.52], [126.3, -25.78], [126.29, -25.97], [126.35, -26.07], [126.41, -26.18], [126.46, -26.22], [126.54, -26.24], [126.58, -26.16], [126.62, -26.05], [126.66, -26.02], [126.7, -25.99], [126.75, -26], [126.81, -26.02], [126.88, -26.23], [126.97, -26.47], [127.06, -26.54], [127.09, -26.24], [127.22, -25.75], [127.35, -25.89], [127.44, -25.8], [127.43, -25.57], [127.34, -25.28], [127.34, -25.11], [127.32, -24.95], [127.27, -24.84], [127.18, -24.68], [127.09, -24.53], [127.04, -24.44], [127.06, -24.36], [127.23, -24.41], [127.14, -24.04], [126.48, -23.87], [126.39, -23.95], [126.28, -24], [125.97, -23.92], [125.86, -23.97], [125.76, -24.03], [125.68, -24.01], [125.77, -23.65], [125.61, -23.63], [125.42, -23.59], [125.19, -23.61], [124.91, -23.65], [124.86, -23.67], [124.83, -23.71], [124.8, -23.78], [124.79, -23.86], [124.66, -23.91], [124.57, -23.93], [124.5, -23.96], [124.45, -23.94], [124.4, -23.93], [124.41, -23.76], [124.4, -23.6], [124.39, -23.55], [124.36, -23.51], [124.21, -23.37], [124.08, -23.25], [124.01, -23.32], [123.94, -23.4], [123.9, -23.43], [123.86, -23.46], [123.8, -23.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 12, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [94.34, 22.63, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [81.84, 18.13, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.18, -3.53], [3.54, -0.18], [0.18, 3.54], [-3.53, 0.18]], "o": [[0.18, 3.54], [-3.53, 0.18], [-0.18, -3.53], [3.54, -0.18]], "v": [[116.58, 2.09], [110.5, 8.81], [103.78, 2.73], [109.86, -3.99]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.61, 0.36], [0.36, 0.16], [0.04, 0.7], [0, 0], [0, 0], [0, 0], [0.61, -0.35], [0.32, -0.23], [0.63, 0.32], [0, 0], [0, 0], [0, 0], [0, -0.7], [-0.01, -0.19], [-0.02, -0.19], [0.59, -0.38], [0, 0], [0, 0], [0, 0], [-0.61, -0.36], [-0.36, -0.16], [-0.04, -0.7], [0, 0], [0, 0], [0, 0], [-0.61, 0.35], [-0.31, 0.23], [-0.63, -0.32], [0, 0], [0, 0], [0, 0], [0, 0.7], [0.01, 0.2], [0.02, 0.19], [-0.59, 0.38]], "o": [[0, 0], [0, 0], [-0.59, 0.38], [-0.34, -0.2], [-0.64, -0.29], [0, 0], [0, 0], [0, 0], [0.04, 0.7], [-0.34, 0.2], [-0.57, 0.41], [0, 0], [0, 0], [0, 0], [0.63, 0.32], [0, 0.19], [0.01, 0.19], [0.07, 0.7], [0, 0], [0, 0], [0, 0], [0.59, -0.38], [0.34, 0.2], [0.64, 0.29], [0, 0], [0, 0], [0, 0], [-0.04, -0.7], [0.34, -0.19], [0.57, -0.41], [0, 0], [0, 0], [0, 0], [-0.62, -0.32], [0, -0.19], [-0.01, -0.19], [-0.07, -0.7], [0, 0]], "v": [[123.87, -1.78], [119.63, -8.36], [117.62, -7.05], [115.67, -7.02], [114.63, -7.56], [113.52, -9.16], [113.4, -11.55], [105.58, -11.16], [105.7, -8.77], [104.75, -7.06], [103.77, -6.43], [101.83, -6.26], [99.7, -7.36], [96.13, -0.39], [98.27, 0.7], [99.27, 2.38], [99.28, 2.96], [99.32, 3.53], [98.51, 5.29], [96.48, 6.6], [100.72, 13.17], [102.74, 11.87], [104.69, 11.84], [105.73, 12.38], [106.84, 13.98], [106.96, 16.37], [114.77, 15.98], [114.65, 13.58], [115.6, 11.88], [116.58, 11.25], [118.52, 11.08], [120.66, 12.18], [124.22, 5.21], [122.09, 4.12], [121.09, 2.44], [121.08, 1.86], [121.04, 1.29], [121.86, -0.48]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2706, 0.3529, 0.3922]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [110.31, 2.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [110.31, 2.5]}, "r": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 0}, {"s": [-360], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.33, -2.61], [2.61, 4.33], [-4.33, 2.61], [-2.61, -4.33]], "o": [[-4.33, 2.61], [-2.61, -4.33], [4.33, -2.61], [2.61, 4.33]], "v": [[89.1, 44.88], [76.54, 41.76], [79.65, 29.2], [92.22, 32.32]]}}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.04, 1], [0.04, 0.56], [-0.86, 0.52], [0, 0], [0, 0], [0, 0], [0.85, 0.53], [0.5, 0.24], [0.02, 1], [0, 0], [0, 0], [0, 0], [0.89, -0.47], [0.24, -0.14], [0.23, -0.15], [0.88, 0.48], [0, 0], [0, 0], [0, 0], [0.04, -1], [-0.04, -0.56], [0.86, -0.52], [0, 0], [0, 0], [0, 0], [-0.85, -0.53], [-0.5, -0.24], [-0.02, -1.01], [0, 0], [0, 0], [0, 0], [-0.88, 0.47], [-0.24, 0.14], [-0.23, 0.15], [-0.88, -0.49]], "o": [[0, 0], [0, 0], [-0.88, -0.49], [0.02, -0.56], [-0.07, -1], [0, 0], [0, 0], [0, 0], [-0.86, 0.52], [-0.47, -0.3], [-0.9, -0.44], [0, 0], [0, 0], [0, 0], [0.02, 1.01], [-0.24, 0.13], [-0.24, 0.14], [-0.83, 0.56], [0, 0], [0, 0], [0, 0], [0.88, 0.49], [-0.02, 0.55], [0.07, 1], [0, 0], [0, 0], [0, 0], [0.86, -0.52], [0.47, 0.29], [0.91, 0.44], [0, 0], [0, 0], [0, 0], [-0.02, -1], [0.24, -0.13], [0.24, -0.14], [0.83, -0.56], [0, 0]], "v": [[98.89, 51.44], [104.29, 41.67], [101.3, 40.02], [99.94, 37.58], [99.91, 35.91], [101.18, 33.44], [104.11, 31.67], [98.34, 22.1], [95.41, 23.87], [92.63, 23.83], [91.17, 23.02], [89.65, 20.69], [89.59, 17.27], [78.42, 17.48], [78.49, 20.91], [77.05, 23.29], [76.34, 23.7], [75.64, 24.14], [72.88, 24.3], [69.86, 22.64], [64.46, 32.41], [67.45, 34.07], [68.81, 36.51], [68.84, 38.17], [67.57, 40.64], [64.64, 42.41], [70.41, 51.98], [73.34, 50.21], [76.11, 50.25], [77.57, 51.05], [79.09, 53.39], [79.16, 56.81], [90.33, 56.6], [90.26, 53.17], [91.7, 50.8], [92.42, 50.39], [93.11, 49.94], [95.89, 49.79]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.2157, 0.2784, 0.3098]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [84.44, 37.06]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [84.44, 37.06]}, "r": {"a": 1, "k": [{"o": {"x": 0.17, "y": 0.17}, "i": {"x": 0.83, "y": 0.83}, "s": [0], "t": 0}, {"s": [360], "t": 125}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 13, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [53, 97, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [53, 97, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[5.71, 2.87], [-3.16, -0.75], [-1.5, -0.62], [-0.76, -0.31], [-0.73, -0.37], [-4.49, -4.76], [-1.68, -2.8], [0, 0], [-0.34, -0.74], [-0.69, -1.46], [-0.51, -1.53], [-0.46, -1.52], [-0.28, -1.57], [-0.15, -1.59], [1.66, 2.6], [0.83, 1.27], [0.4, 0.64], [0.42, 0.62], [0.84, 1.23], [0.46, 0.58], [0, 0], [1.04, 1.05], [0, 0], [4.71, 3.61]], "o": [[3.23, -0.27], [1.55, 0.44], [0.77, 0.28], [0.74, 0.34], [5.85, 2.93], [2.09, 2.51], [0, 0], [0.44, 0.68], [0.74, 1.44], [0.74, 1.43], [0.52, 1.52], [0.43, 1.54], [0.27, 1.58], [-1.96, -2.6], [-0.89, -1.27], [-0.42, -0.63], [-0.39, -0.64], [-0.89, -1.21], [-0.4, -0.63], [0, 0], [-0.93, -1.15], [0, 0], [-4.03, -4.26], [-4.72, -3.62]], "v": [[61.14, -192.03], [70.76, -191.03], [75.41, -189.55], [77.69, -188.66], [79.89, -187.59], [95.49, -175.81], [101.45, -168.06], [102.77, -166.01], [103.99, -163.91], [106.13, -159.55], [107.91, -155.07], [109.38, -150.49], [110.34, -145.78], [110.97, -141.03], [105.78, -148.9], [103.2, -152.71], [101.98, -154.62], [100.65, -156.46], [98.04, -160.12], [96.68, -161.89], [95.3, -163.62], [92.5, -167.06], [89.62, -170.39], [76.6, -182.26]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[5.71, 2.87], [-3.16, -0.75], [-1.5, -0.62], [-0.76, -0.31], [-0.73, -0.37], [-4.49, -4.76], [-1.68, -2.8], [0, 0], [-0.34, -0.74], [-0.69, -1.46], [-0.51, -1.53], [-0.46, -1.52], [-0.28, -1.57], [-0.15, -1.59], [1.66, 2.6], [0.83, 1.27], [0.4, 0.64], [0.42, 0.62], [0.84, 1.23], [0.46, 0.58], [0, 0], [1.04, 1.05], [0, 0], [4.71, 3.61]], "o": [[3.23, -0.27], [1.55, 0.44], [0.77, 0.28], [0.74, 0.34], [5.85, 2.93], [2.09, 2.51], [0, 0], [0.44, 0.68], [0.74, 1.44], [0.74, 1.43], [0.52, 1.52], [0.43, 1.54], [0.27, 1.58], [-1.96, -2.6], [-0.89, -1.27], [-0.42, -0.63], [-0.39, -0.64], [-0.89, -1.21], [-0.4, -0.63], [0, 0], [-0.93, -1.15], [0, 0], [-4.03, -4.26], [-4.72, -3.62]], "v": [[56.62, -188.41], [66.24, -187.41], [70.88, -185.93], [73.17, -185.04], [75.37, -183.97], [90.97, -172.19], [96.93, -164.44], [98.25, -162.39], [99.47, -160.29], [101.6, -155.93], [103.38, -151.45], [104.85, -146.87], [105.81, -142.16], [106.45, -137.41], [101.26, -145.28], [98.68, -149.09], [97.45, -151], [96.12, -152.84], [93.52, -156.5], [92.16, -158.27], [90.77, -160], [87.98, -163.44], [85.09, -166.77], [72.07, -178.64]]}], "t": 31}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[5.71, 2.87], [-3.16, -0.75], [-1.5, -0.62], [-0.76, -0.31], [-0.73, -0.37], [-4.49, -4.76], [-1.68, -2.8], [0, 0], [-0.34, -0.74], [-0.69, -1.46], [-0.51, -1.53], [-0.46, -1.52], [-0.28, -1.57], [-0.15, -1.59], [1.66, 2.6], [0.83, 1.27], [0.4, 0.64], [0.42, 0.62], [0.84, 1.23], [0.46, 0.58], [0, 0], [1.04, 1.05], [0, 0], [4.71, 3.61]], "o": [[3.23, -0.27], [1.55, 0.44], [0.77, 0.28], [0.74, 0.34], [5.85, 2.93], [2.09, 2.51], [0, 0], [0.44, 0.68], [0.74, 1.44], [0.74, 1.43], [0.52, 1.52], [0.43, 1.54], [0.27, 1.58], [-1.96, -2.6], [-0.89, -1.27], [-0.42, -0.63], [-0.39, -0.64], [-0.89, -1.21], [-0.4, -0.63], [0, 0], [-0.93, -1.15], [0, 0], [-4.03, -4.26], [-4.72, -3.62]], "v": [[61.14, -192.03], [70.76, -191.03], [75.41, -189.55], [77.69, -188.66], [79.89, -187.59], [95.49, -175.81], [101.45, -168.06], [102.77, -166.01], [103.99, -163.91], [106.13, -159.55], [107.91, -155.07], [109.38, -150.49], [110.34, -145.78], [110.97, -141.03], [105.78, -148.9], [103.2, -152.71], [101.98, -154.62], [100.65, -156.46], [98.04, -160.12], [96.68, -161.89], [95.3, -163.62], [92.5, -167.06], [89.62, -170.39], [76.6, -182.26]]}], "t": 62}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[5.71, 2.87], [-3.16, -0.75], [-1.5, -0.62], [-0.76, -0.31], [-0.73, -0.37], [-4.49, -4.76], [-1.68, -2.8], [0, 0], [-0.34, -0.74], [-0.69, -1.46], [-0.51, -1.53], [-0.46, -1.52], [-0.28, -1.57], [-0.15, -1.59], [1.66, 2.6], [0.83, 1.27], [0.4, 0.64], [0.42, 0.62], [0.84, 1.23], [0.46, 0.58], [0, 0], [1.04, 1.05], [0, 0], [4.71, 3.61]], "o": [[3.23, -0.27], [1.55, 0.44], [0.77, 0.28], [0.74, 0.34], [5.85, 2.93], [2.09, 2.51], [0, 0], [0.44, 0.68], [0.74, 1.44], [0.74, 1.43], [0.52, 1.52], [0.43, 1.54], [0.27, 1.58], [-1.96, -2.6], [-0.89, -1.27], [-0.42, -0.63], [-0.39, -0.64], [-0.89, -1.21], [-0.4, -0.63], [0, 0], [-0.93, -1.15], [0, 0], [-4.03, -4.26], [-4.72, -3.62]], "v": [[56.62, -188.41], [66.24, -187.41], [70.88, -185.93], [73.17, -185.04], [75.37, -183.97], [90.97, -172.19], [96.93, -164.44], [98.25, -162.39], [99.47, -160.29], [101.6, -155.93], [103.38, -151.45], [104.85, -146.87], [105.81, -142.16], [106.45, -137.41], [101.26, -145.28], [98.68, -149.09], [97.45, -151], [96.12, -152.84], [93.52, -156.5], [92.16, -158.27], [90.77, -160], [87.98, -163.44], [85.09, -166.77], [72.07, -178.64]]}], "t": 94}, {"s": [{"c": true, "i": [[5.71, 2.87], [-3.16, -0.75], [-1.5, -0.62], [-0.76, -0.31], [-0.73, -0.37], [-4.49, -4.76], [-1.68, -2.8], [0, 0], [-0.34, -0.74], [-0.69, -1.46], [-0.51, -1.53], [-0.46, -1.52], [-0.28, -1.57], [-0.15, -1.59], [1.66, 2.6], [0.83, 1.27], [0.4, 0.64], [0.42, 0.62], [0.84, 1.23], [0.46, 0.58], [0, 0], [1.04, 1.05], [0, 0], [4.71, 3.61]], "o": [[3.23, -0.27], [1.55, 0.44], [0.77, 0.28], [0.74, 0.34], [5.85, 2.93], [2.09, 2.51], [0, 0], [0.44, 0.68], [0.74, 1.44], [0.74, 1.43], [0.52, 1.52], [0.43, 1.54], [0.27, 1.58], [-1.96, -2.6], [-0.89, -1.27], [-0.42, -0.63], [-0.39, -0.64], [-0.89, -1.21], [-0.4, -0.63], [0, 0], [-0.93, -1.15], [0, 0], [-4.03, -4.26], [-4.72, -3.62]], "v": [[61.14, -192.03], [70.76, -191.03], [75.41, -189.55], [77.69, -188.66], [79.89, -187.59], [95.49, -175.81], [101.45, -168.06], [102.77, -166.01], [103.99, -163.91], [106.13, -159.55], [107.91, -155.07], [109.38, -150.49], [110.34, -145.78], [110.97, -141.03], [105.78, -148.9], [103.2, -152.71], [101.98, -154.62], [100.65, -156.46], [98.04, -160.12], [96.68, -161.89], [95.3, -163.62], [92.5, -167.06], [89.62, -170.39], [76.6, -182.26]]}], "t": 125}]}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-2.5, 1.39], [1.29, -4.87], [-0.71, -4.56], [-0.93, -2.18], [-1.38, -2.1], [-3.63, -4.36], [4.2, 4.14], [1.46, 2.69], [0.29, 0.71], [0, 0], [0.22, 1.55], [-0.56, 3.01], [-1.36, 2.61], [-1.93, 2.12]], "o": [[-2.44, 5.09], [-1.3, 4.86], [0.33, 2.29], [0.9, 2.19], [2.69, 4.27], [-5.46, -1.56], [-2.13, -2.04], [-0.35, -0.68], [0, 0], [-0.44, -1.49], [-0.46, -3.08], [0.56, -3.01], [1.37, -2.61], [1.96, -2.1]], "v": [[-16.55, -30.81], [-22.32, -15.98], [-23.35, -1.78], [-21.47, 4.94], [-18.06, 11.4], [-8.25, 24.04], [-22.93, 15.39], [-28.32, 8.2], [-29.33, 6.13], [-30.17, 3.97], [-31.26, -0.57], [-31.09, -9.81], [-28.14, -18.29], [-23.16, -25.42]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-2.5, 1.39], [1.29, -4.87], [-0.71, -4.56], [-0.93, -2.18], [-1.38, -2.1], [-3.63, -4.36], [4.2, 4.14], [1.46, 2.69], [0.29, 0.71], [0, 0], [0.22, 1.55], [-0.56, 3.01], [-1.36, 2.61], [-1.93, 2.12]], "o": [[-2.44, 5.09], [-1.3, 4.86], [0.33, 2.29], [0.9, 2.19], [2.69, 4.27], [-5.46, -1.56], [-2.13, -2.04], [-0.35, -0.68], [0, 0], [-0.44, -1.49], [-0.46, -3.08], [0.56, -3.01], [1.37, -2.61], [1.96, -2.1]], "v": [[-12.02, -36.69], [-17.79, -21.86], [-18.83, -7.66], [-16.95, -0.94], [-13.54, 5.52], [-3.73, 18.16], [-18.4, 9.51], [-23.8, 2.32], [-24.81, 0.25], [-25.65, -1.91], [-26.73, -6.45], [-26.56, -15.69], [-23.62, -24.17], [-18.64, -31.3]]}], "t": 31}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-2.5, 1.39], [1.29, -4.87], [-0.71, -4.56], [-0.93, -2.18], [-1.38, -2.1], [-3.63, -4.36], [4.2, 4.14], [1.46, 2.69], [0.29, 0.71], [0, 0], [0.22, 1.55], [-0.56, 3.01], [-1.36, 2.61], [-1.93, 2.12]], "o": [[-2.44, 5.09], [-1.3, 4.86], [0.33, 2.29], [0.9, 2.19], [2.69, 4.27], [-5.46, -1.56], [-2.13, -2.04], [-0.35, -0.68], [0, 0], [-0.44, -1.49], [-0.46, -3.08], [0.56, -3.01], [1.37, -2.61], [1.96, -2.1]], "v": [[-16.55, -30.81], [-22.32, -15.98], [-23.35, -1.78], [-21.47, 4.94], [-18.06, 11.4], [-8.25, 24.04], [-22.93, 15.39], [-28.32, 8.2], [-29.33, 6.13], [-30.17, 3.97], [-31.26, -0.57], [-31.09, -9.81], [-28.14, -18.29], [-23.16, -25.42]]}], "t": 62}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[-2.5, 1.39], [1.29, -4.87], [-0.71, -4.56], [-0.93, -2.18], [-1.38, -2.1], [-3.63, -4.36], [4.2, 4.14], [1.46, 2.69], [0.29, 0.71], [0, 0], [0.22, 1.55], [-0.56, 3.01], [-1.36, 2.61], [-1.93, 2.12]], "o": [[-2.44, 5.09], [-1.3, 4.86], [0.33, 2.29], [0.9, 2.19], [2.69, 4.27], [-5.46, -1.56], [-2.13, -2.04], [-0.35, -0.68], [0, 0], [-0.44, -1.49], [-0.46, -3.08], [0.56, -3.01], [1.37, -2.61], [1.96, -2.1]], "v": [[-12.02, -36.69], [-17.79, -21.86], [-18.83, -7.66], [-16.95, -0.94], [-13.54, 5.52], [-3.73, 18.16], [-18.4, 9.51], [-23.8, 2.32], [-24.81, 0.25], [-25.65, -1.91], [-26.73, -6.45], [-26.56, -15.69], [-23.62, -24.17], [-18.64, -31.3]]}], "t": 94}, {"s": [{"c": true, "i": [[-2.5, 1.39], [1.29, -4.87], [-0.71, -4.56], [-0.93, -2.18], [-1.38, -2.1], [-3.63, -4.36], [4.2, 4.14], [1.46, 2.69], [0.29, 0.71], [0, 0], [0.22, 1.55], [-0.56, 3.01], [-1.36, 2.61], [-1.93, 2.12]], "o": [[-2.44, 5.09], [-1.3, 4.86], [0.33, 2.29], [0.9, 2.19], [2.69, 4.27], [-5.46, -1.56], [-2.13, -2.04], [-0.35, -0.68], [0, 0], [-0.44, -1.49], [-0.46, -3.08], [0.56, -3.01], [1.37, -2.61], [1.96, -2.1]], "v": [[-16.55, -30.81], [-22.32, -15.98], [-23.35, -1.78], [-21.47, 4.94], [-18.06, 11.4], [-8.25, 24.04], [-22.93, 15.39], [-28.32, 8.2], [-29.33, 6.13], [-30.17, 3.97], [-31.26, -0.57], [-31.09, -9.81], [-28.14, -18.29], [-23.16, -25.42]]}], "t": 125}]}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[1.76, 0.9], [-2.86, 1.52], [-1.87, 1.92], [-1.03, 2.89], [-0.87, 3.81], [-0.02, -4.13], [0.86, -2.02], [1.54, -1.55], [3.88, -0.57], [1.91, 0.24]], "o": [[3.52, -1.64], [2.86, -1.49], [1.87, -1.91], [1.06, -2.88], [2.4, 3.09], [-0.03, 2.06], [-0.82, 2.03], [-3.11, 3.15], [-1.96, 0.28], [-1.91, -0.25]], "v": [[116.04, 50.58], [125.59, 46.1], [132.78, 41.06], [137.1, 34.03], [139.66, 23.97], [143.32, 34.99], [142.09, 41.22], [138.46, 46.69], [127.43, 52.09], [121.61, 52.16]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[1.76, 0.9], [-2.86, 1.52], [-1.87, 1.92], [-1.03, 2.89], [-0.87, 3.81], [-0.02, -4.13], [0.86, -2.02], [1.54, -1.55], [3.88, -0.57], [1.91, 0.24]], "o": [[3.52, -1.64], [2.86, -1.49], [1.87, -1.91], [1.06, -2.88], [2.4, 3.09], [-0.03, 2.06], [-0.82, 2.03], [-3.11, 3.15], [-1.96, 0.28], [-1.91, -0.25]], "v": [[108.81, 45.6], [118.35, 41.12], [125.54, 36.08], [129.86, 29.05], [132.42, 19], [136.08, 30.01], [134.85, 36.24], [131.22, 41.71], [120.19, 47.11], [114.37, 47.19]]}], "t": 31}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[1.76, 0.9], [-2.86, 1.52], [-1.87, 1.92], [-1.03, 2.89], [-0.87, 3.81], [-0.02, -4.13], [0.86, -2.02], [1.54, -1.55], [3.88, -0.57], [1.91, 0.24]], "o": [[3.52, -1.64], [2.86, -1.49], [1.87, -1.91], [1.06, -2.88], [2.4, 3.09], [-0.03, 2.06], [-0.82, 2.03], [-3.11, 3.15], [-1.96, 0.28], [-1.91, -0.25]], "v": [[116.04, 50.58], [125.59, 46.1], [132.78, 41.06], [137.1, 34.03], [139.66, 23.97], [143.32, 34.99], [142.09, 41.22], [138.46, 46.69], [127.43, 52.09], [121.61, 52.16]]}], "t": 62}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[1.76, 0.9], [-2.86, 1.52], [-1.87, 1.92], [-1.03, 2.89], [-0.87, 3.81], [-0.02, -4.13], [0.86, -2.02], [1.54, -1.55], [3.88, -0.57], [1.91, 0.24]], "o": [[3.52, -1.64], [2.86, -1.49], [1.87, -1.91], [1.06, -2.88], [2.4, 3.09], [-0.03, 2.06], [-0.82, 2.03], [-3.11, 3.15], [-1.96, 0.28], [-1.91, -0.25]], "v": [[108.81, 45.6], [118.35, 41.12], [125.54, 36.08], [129.86, 29.05], [132.42, 19], [136.08, 30.01], [134.85, 36.24], [131.22, 41.71], [120.19, 47.11], [114.37, 47.19]]}], "t": 94}, {"s": [{"c": true, "i": [[1.76, 0.9], [-2.86, 1.52], [-1.87, 1.92], [-1.03, 2.89], [-0.87, 3.81], [-0.02, -4.13], [0.86, -2.02], [1.54, -1.55], [3.88, -0.57], [1.91, 0.24]], "o": [[3.52, -1.64], [2.86, -1.49], [1.87, -1.91], [1.06, -2.88], [2.4, 3.09], [-0.03, 2.06], [-0.82, 2.03], [-3.11, 3.15], [-1.96, 0.28], [-1.91, -0.25]], "v": [[116.04, 50.58], [125.59, 46.1], [132.78, 41.06], [137.1, 34.03], [139.66, 23.97], [143.32, 34.99], [142.09, 41.22], [138.46, 46.69], [127.43, 52.09], [121.61, 52.16]]}], "t": 125}]}}, {"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[0, 0], [12.47, 16.21], [6.44, 23.76], [1.64, 18.22], [-3.48, 52.88], [-49.48, -49.93], [-25.17, -11.09], [-24.79, -17.26], [38.43, -4.41], [11.15, -14.75], [7.69, -12.98], [13.23, -10.71], [0, 0]], "o": [[0, 0], [-12.12, -15.75], [-6.1, -22.5], [-2.18, -24.21], [4.07, -61.83], [23.11, 23.31], [25.17, 11.09], [24.79, 17.26], [-34.37, 3.94], [-10.91, 14.44], [-7.21, 12.16], [-16.11, 13.05], [0, 0]], "v": [[52.02, 99.34], [37.73, 32.29], [-15.76, 0.25], [6.26, -54.52], [-48.41, -132.29], [83.75, -166.17], [121.59, -108.77], [203.5, -95.7], [176.91, -17.97], [120.53, -5.61], [127.33, 36.14], [83.65, 63.21], [56.91, 99.1]]}], "t": 0}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[0, 0], [12.47, 16.21], [6.44, 23.76], [1.68, 18.21], [-4.3, 52.82], [-49.48, -49.93], [-25.17, -11.09], [-24.79, -17.26], [37.78, -8.27], [7.57, -13.55], [7.69, -12.98], [13.23, -10.71], [0, 0]], "o": [[0, 0], [-12.12, -15.75], [-6.1, -22.5], [-1.68, -18.21], [4.16, -51.03], [23.11, 23.31], [25.17, 11.09], [24.79, 17.26], [-30.68, 6.71], [-8.83, 15.8], [-7.21, 12.16], [-16.11, 13.05], [0, 0]], "v": [[52.02, 99.34], [44.06, 37.72], [-11.23, -5.63], [2.64, -53.17], [-39.81, -130.48], [79.22, -162.55], [128.83, -96.1], [202.59, -90.72], [176.9, -27.02], [125.5, -5.61], [120.09, 31.17], [76.41, 58.24], [56.91, 99.1]]}], "t": 31}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[0, 0], [12.47, 16.21], [6.44, 23.76], [1.64, 18.22], [-3.48, 52.88], [-49.48, -49.93], [-25.17, -11.09], [-24.79, -17.26], [38.43, -4.41], [11.15, -14.75], [7.69, -12.98], [13.23, -10.71], [0, 0]], "o": [[0, 0], [-12.12, -15.75], [-6.1, -22.5], [-2.18, -24.21], [4.07, -61.83], [23.11, 23.31], [25.17, 11.09], [24.79, 17.26], [-34.37, 3.94], [-10.91, 14.44], [-7.21, 12.16], [-16.11, 13.05], [0, 0]], "v": [[52.02, 99.34], [37.73, 32.29], [-15.76, 0.25], [6.26, -54.52], [-48.41, -132.29], [83.75, -166.17], [121.59, -108.77], [203.5, -95.7], [176.91, -17.97], [120.53, -5.61], [127.33, 36.14], [83.65, 63.21], [56.91, 99.1]]}], "t": 62}, {"o": {"x": 0.33, "y": 0}, "i": {"x": 0.67, "y": 1}, "s": [{"c": true, "i": [[0, 0], [12.47, 16.21], [6.44, 23.76], [1.68, 18.21], [-4.3, 52.82], [-49.48, -49.93], [-25.17, -11.09], [-24.79, -17.26], [37.78, -8.27], [7.57, -13.55], [7.69, -12.98], [13.23, -10.71], [0, 0]], "o": [[0, 0], [-12.12, -15.75], [-6.1, -22.5], [-1.68, -18.21], [4.16, -51.03], [23.11, 23.31], [25.17, 11.09], [24.79, 17.26], [-30.68, 6.71], [-8.83, 15.8], [-7.21, 12.16], [-16.11, 13.05], [0, 0]], "v": [[52.02, 99.34], [44.06, 37.72], [-11.23, -5.63], [2.64, -53.17], [-39.81, -130.48], [79.22, -162.55], [128.83, -96.1], [202.59, -90.72], [176.9, -27.02], [125.5, -5.61], [120.09, 31.17], [76.41, 58.24], [56.91, 99.1]]}], "t": 94}, {"s": [{"c": true, "i": [[0, 0], [12.47, 16.21], [6.44, 23.76], [1.64, 18.22], [-3.48, 52.88], [-49.48, -49.93], [-25.17, -11.09], [-24.79, -17.26], [38.43, -4.41], [11.15, -14.75], [7.69, -12.98], [13.23, -10.71], [0, 0]], "o": [[0, 0], [-12.12, -15.75], [-6.1, -22.5], [-2.18, -24.21], [4.07, -61.83], [23.11, 23.31], [25.17, 11.09], [24.79, 17.26], [-34.37, 3.94], [-10.91, 14.44], [-7.21, 12.16], [-16.11, 13.05], [0, 0]], "v": [[52.02, 99.34], [37.73, 32.29], [-15.76, 0.25], [6.26, -54.52], [-48.41, -132.29], [83.75, -166.17], [121.59, -108.77], [203.5, -95.7], [176.91, -17.97], [120.53, -5.61], [127.33, 36.14], [83.65, 63.21], [56.91, 99.1]]}], "t": 125}]}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9961, 0.8941, 0.5647]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 14, "parent": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 253, "ip": 0, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.03, -136.97], [-170.03, -138.93], [-156.58, -138.93], [-156.58, -136.97]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.03, -142.4], [-170.03, -147.45], [-156.58, -147.45], [-156.58, -142.4]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.03, -110.14], [-170.03, -115.2], [-156.58, -115.2], [-156.58, -110.14]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-170.03, -104.39], [-170.03, -158.63], [-156.58, -158.63], [-156.58, -104.39]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.651, 0.651, 0.651]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-128.72, -119.42], [-128.72, -121.51], [-109.25, -121.51], [-109.25, -119.42]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-128.72, -110.51], [-128.72, -115.89], [-109.25, -115.89], [-109.25, -110.51]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-128.72, -104.39], [-128.72, -162.11], [-109.25, -162.11], [-109.25, -104.39]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.651, 0.651, 0.651]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-66.26, -118.58], [-87.04, -139.13], [-82.21, -144.02], [-61.42, -123.48]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-55.21, -104.39], [-109.25, -157.81], [-101.19, -165.96], [-47.15, -112.55]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-137.94, -144.02], [-137.94, -173.25], [-131.06, -173.25], [-131.06, -144.02]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-140.18, -104.41], [-140.18, -180.39], [-128.71, -180.39], [-128.71, -104.41]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-156.58, -112.95], [-156.58, -120.11], [-138.13, -120.11], [-138.13, -112.95]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-156.58, -154.04], [-156.58, -156.7], [-140.18, -156.7], [-140.18, -154.04]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-156.58, -165.87], [-156.58, -173.03], [-140.18, -173.03], [-140.18, -165.87]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-156.58, -104.41], [-156.58, -169.54], [-140.18, -169.54], [-140.18, -104.41]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.7804, 0.7804, 0.7804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-41.02, -94.18], [-41.02, -83.74], [-51.24, -83.74], [-51.24, -94.18]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-41.02, -94.18], [-41.02, -60.38], [-51.24, -60.38], [-51.24, -94.18]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-187.02, -94.18], [-176.8, -94.18], [-176.8, -83.74], [-187.02, -83.74]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.8588, 0.8588, 0.8588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-176.8, -94.18], [-176.8, -60.38], [-187.02, -60.38], [-187.02, -94.18]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-27.96, -94.19], [-200.09, -94.19], [-200.09, -104.41], [-27.96, -104.41]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-177.41, -192.07], [-203.89, -192.07], [-203.89, -203.26], [-177.41, -203.26]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[218.66, -177.96], [192.18, -177.96], [192.18, -189.15], [218.66, -189.15]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[203.89, -192.07], [177.41, -192.07], [177.41, -203.26], [203.89, -203.26]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[218.66, 91.04], [192.18, 91.04], [192.18, 79.85], [218.66, 79.85]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[113.37, -91.25], [86.89, -91.25], [86.89, -102.44], [113.37, -102.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[128.08, -77.14], [101.6, -77.14], [101.6, -88.33], [128.08, -88.33]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[142.86, -91.25], [116.38, -91.25], [116.38, -102.44], [142.86, -102.44]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-192.18, 91.04], [-218.66, 91.04], [-218.66, 79.85], [-192.18, 79.85]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "it": [{"ty": "sh", "bm": 0, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-177.41, 76.93], [-203.89, 76.93], [-203.89, 65.74], [-177.41, 65.74]]}}}, {"ty": "fl", "bm": 0, "c": {"a": 0, "k": [0.9216, 0.9216, 0.9216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 15, "parent": 1}], "v": "5.7.8", "fr": 25, "op": 126, "ip": 0, "assets": []}