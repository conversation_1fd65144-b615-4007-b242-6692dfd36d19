/**
 * Temporary contentlayer types until proper generation
 * These will be replaced by actual generated types from contentlayer
 */

export interface BlogPost {
  slug: string;
  title: string;
  description: string;
  excerpt: string;
  publishedAt: string;
  updatedAt?: string;
  featured: boolean;
  draft: boolean;
  readingTime: number;
  wordCount: number;
  categorySlug: string;
  tags: string[];
  author: string;
  url: string;
  body: {
    code: string;
    raw: string;
  };
  image?: {
    src: string;
    alt: string;
    caption?: string;
  };
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
    canonicalUrl?: string;
    noindex?: boolean;
  };
  relatedPosts?: string[];
  _raw: {
    flattenedPath: string;
    contentType: string;
  };
}

export interface BlogCategory {
  slug: string;
  name: string;
  description: string;
  color: string;
  icon?: string;
  body: {
    code: string;
    raw: string;
  };
  _raw: {
    flattenedPath: string;
    contentType: string;
  };
}

export interface BlogAuthor {
  slug: string;
  name: string;
  bio: string;
  avatar?: string;
  role: string;
  social?: {
    twitter?: string;
    linkedin?: string;
    email?: string;
  };
  body: {
    code: string;
    raw: string;
  };
  _raw: {
    flattenedPath: string;
    contentType: string;
  };
}

// Mock data arrays until contentlayer generates them
export const allBlogPosts: BlogPost[] = [];
export const allBlogCategories: BlogCategory[] = [];
export const allBlogAuthors: <AUTHORS>
