import "./src/env.js";
import withBundleAnalyzer from "@next/bundle-analyzer";
import { withContentlayer } from "next-contentlayer2";

/** @type {import("next").NextConfig} */
const config = {
  // Performance optimizations
  poweredByHeader: false,

  // Headers for better caching and performance
  async headers() {
    return [
      {
        source: "/_next/static/css/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        source: "/_next/static/chunks/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
    ];
  },
};

const bundleAnalyzerConfig = withBundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

export default withContentlayer(bundleAnalyzerConfig(config));
