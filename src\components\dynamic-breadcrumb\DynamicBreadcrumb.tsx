"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
} from "~/components/ui/breadcrumb";
import { usePathname } from "next/navigation";
import Script from "next/script";
import { ChevronRight } from "lucide-react";
import { capitalize, cn } from "~/lib/utils";
import { SITE_DOMAIN } from "~/constants/siteConfig";

type BreadcrumbSchemaItem = {
  "@type": string;
  position: number;
  name: string;
  item: string;
};

type BreadcrumbSchema = {
  "@context": string;
  "@type": string;
  itemListElement: BreadcrumbSchemaItem[];
};

export function DynamicBreadcrumb() {
  // Get the current pathname
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
  const pathname: string = usePathname() || "/";

  // Split the pathname into segments
  const paths = pathname.split("/").filter(Boolean);

  // Hide breadcrumb for home page
  if (pathname === "/") {
    return null;
  }

  // Generate breadcrumb schema for SEO
  const breadcrumbSchema: BreadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: paths.map((path: string, index: number) => {
      // Convert URL slug to readable format
      const readableName: string = path
        .split("-") // Split by hyphens
        .map((word: string) => capitalize(word)) // Capitalize each word
        .join(" "); // Join with spaces

      return {
        "@type": "ListItem",
        position: index + 1,
        name: readableName, // Use readable format
        item: `${SITE_DOMAIN}/${paths.slice(0, index + 1).join("/")}`,
      };
    }),
  };

  return (
    <section className="pt-2">
      {/* Breadcrumb UI */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
            <ChevronRight
              role="presentation"
              aria-hidden="true"
              className={cn("[&>svg]:h-3.5 [&>svg]:w-3.5")}
            />
          </BreadcrumbItem>
          {paths.map((path: string, index: number) => {
            const href = `/${paths.slice(0, index + 1).join("/")}`;
            const isLast = index === paths.length - 1;

            return (
              <BreadcrumbItem key={index}>
                {/* Render separator outside <li> */}
                {index > 0 && (
                  <span aria-hidden="true" className="mx-2">
                    <ChevronRight
                      role="presentation"
                      aria-hidden="true"
                      className={cn("[&>svg]:h-3.5 [&>svg]:w-3.5")}
                    />
                  </span>
                )}
                {isLast ? (
                  <BreadcrumbPage>{capitalize(path)}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href={href}>
                    {capitalize(path)}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            );
          })}
        </BreadcrumbList>
      </Breadcrumb>

      {/* Schema Markup for SEO using next/script */}
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
        strategy="afterInteractive" // Load after the page is interactive
      />
    </section>
  );
}
