{"version": 3, "sources": ["../../../contentlayer.config.ts"], "sourcesContent": ["import { defineDocumentType, makeSource } from 'contentlayer2/source-files';\nimport rehypeHighlight from 'rehype-highlight';\nimport rehypeSlug from 'rehype-slug';\nimport rehypeAutolinkHeadings from 'rehype-autolink-headings';\nimport remarkGfm from 'remark-gfm';\nimport readingTime from 'reading-time';\n\n// ============================================================================\n// Blog Post Document Type\n// ============================================================================\n\nexport const BlogPost = defineDocumentType(() => ({\n  name: 'BlogPost',\n  filePathPattern: 'blog/**/*.mdx',\n  contentType: 'mdx',\n  fields: {\n    title: {\n      type: 'string',\n      required: true,\n    },\n    description: {\n      type: 'string',\n      required: true,\n    },\n    publishedAt: {\n      type: 'date',\n      required: true,\n    },\n    updatedAt: {\n      type: 'date',\n      required: false,\n    },\n    featured: {\n      type: 'boolean',\n      default: false,\n    },\n    draft: {\n      type: 'boolean',\n      default: false,\n    },\n    category: {\n      type: 'string',\n      required: true,\n    },\n    tags: {\n      type: 'list',\n      of: { type: 'string' },\n      required: true,\n    },\n    author: {\n      type: 'string',\n      required: true,\n    },\n    image: {\n      type: 'nested',\n      of: {\n        src: { type: 'string', required: true },\n        alt: { type: 'string', required: true },\n        caption: { type: 'string', required: false },\n      },\n      required: false,\n    },\n    seo: {\n      type: 'nested',\n      of: {\n        title: { type: 'string', required: false },\n        description: { type: 'string', required: false },\n        keywords: { type: 'list', of: { type: 'string' }, required: false },\n        canonicalUrl: { type: 'string', required: false },\n        noindex: { type: 'boolean', required: false },\n      },\n      required: false,\n    },\n    relatedPosts: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n    },\n  },\n  computedFields: {\n    slug: {\n      type: 'string',\n      resolve: (doc) => {\n        const pathSegments = doc._raw.flattenedPath.split('/');\n        return pathSegments[pathSegments.length - 1];\n      },\n    },\n    url: {\n      type: 'string',\n      resolve: (doc) => {\n        const pathSegments = doc._raw.flattenedPath.split('/');\n        const category = pathSegments[1]; // blog/category/post-name\n        const slug = pathSegments[pathSegments.length - 1];\n        return `/blog/${category}/${slug}`;\n      },\n    },\n    categorySlug: {\n      type: 'string',\n      resolve: (doc) => {\n        const pathSegments = doc._raw.flattenedPath.split('/');\n        return pathSegments[1]; // blog/category/post-name\n      },\n    },\n    excerpt: {\n      type: 'string',\n      resolve: (doc) => {\n        // Extract first paragraph or first 160 characters\n        const content = doc.body.raw;\n        const firstParagraph = content.split('\\n\\n')[0];\n        if (firstParagraph && firstParagraph.length <= 160) {\n          return firstParagraph.replace(/[#*`]/g, '').trim();\n        }\n        return content.slice(0, 160).replace(/[#*`]/g, '').trim() + '...';\n      },\n    },\n    readingTime: {\n      type: 'number',\n      resolve: (doc) => {\n        const stats = readingTime(doc.body.raw);\n        return Math.ceil(stats.minutes);\n      },\n    },\n    wordCount: {\n      type: 'number',\n      resolve: (doc) => {\n        const stats = readingTime(doc.body.raw);\n        return stats.words;\n      },\n    },\n  },\n}));\n\n// ============================================================================\n// Blog Category Document Type\n// ============================================================================\n\nexport const BlogCategory = defineDocumentType(() => ({\n  name: 'BlogCategory',\n  filePathPattern: 'blog-categories/**/*.mdx',\n  contentType: 'mdx',\n  fields: {\n    name: {\n      type: 'string',\n      required: true,\n    },\n    description: {\n      type: 'string',\n      required: true,\n    },\n    color: {\n      type: 'string',\n      required: true,\n    },\n    icon: {\n      type: 'string',\n      required: false,\n    },\n  },\n  computedFields: {\n    slug: {\n      type: 'string',\n      resolve: (doc) => {\n        const pathSegments = doc._raw.flattenedPath.split('/');\n        return pathSegments[pathSegments.length - 1];\n      },\n    },\n  },\n}));\n\n// ============================================================================\n// Blog Author Document Type\n// ============================================================================\n\nexport const BlogAuthor = defineDocumentType(() => ({\n  name: 'BlogAuthor',\n  filePathPattern: 'blog-authors/**/*.mdx',\n  contentType: 'mdx',\n  fields: {\n    name: {\n      type: 'string',\n      required: true,\n    },\n    bio: {\n      type: 'string',\n      required: true,\n    },\n    avatar: {\n      type: 'string',\n      required: false,\n    },\n    role: {\n      type: 'string',\n      required: true,\n    },\n    social: {\n      type: 'nested',\n      of: {\n        twitter: { type: 'string', required: false },\n        linkedin: { type: 'string', required: false },\n        email: { type: 'string', required: false },\n      },\n      required: false,\n    },\n  },\n  computedFields: {\n    slug: {\n      type: 'string',\n      resolve: (doc) => {\n        const pathSegments = doc._raw.flattenedPath.split('/');\n        return pathSegments[pathSegments.length - 1];\n      },\n    },\n  },\n}));\n\n// ============================================================================\n// Content Source Configuration\n// ============================================================================\n\nexport default makeSource({\n  contentDirPath: 'content',\n  documentTypes: [BlogPost, BlogCategory, BlogAuthor],\n  mdx: {\n    remarkPlugins: [remarkGfm],\n    rehypePlugins: [\n      rehypeHighlight,\n      rehypeSlug,\n      [\n        rehypeAutolinkHeadings,\n        {\n          behavior: 'wrap',\n          properties: {\n            className: ['anchor-link'],\n          },\n        },\n      ],\n    ],\n  },\n});\n"], "mappings": ";AAAA,SAAS,oBAAoB,kBAAkB;AAC/C,OAAO,qBAAqB;AAC5B,OAAO,gBAAgB;AACvB,OAAO,4BAA4B;AACnC,OAAO,eAAe;AACtB,OAAO,iBAAiB;AAMjB,IAAM,WAAW,mBAAmB,OAAO;AAAA,EAChD,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,IAAI;AAAA,QACF,KAAK,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,QACtC,KAAK,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,QACtC,SAAS,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,MAC7C;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,IAAI;AAAA,QACF,OAAO,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,QACzC,aAAa,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,QAC/C,UAAU,EAAE,MAAM,QAAQ,IAAI,EAAE,MAAM,SAAS,GAAG,UAAU,MAAM;AAAA,QAClE,cAAc,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,QAChD,SAAS,EAAE,MAAM,WAAW,UAAU,MAAM;AAAA,MAC9C;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,eAAe,IAAI,KAAK,cAAc,MAAM,GAAG;AACrD,eAAO,aAAa,aAAa,SAAS,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,eAAe,IAAI,KAAK,cAAc,MAAM,GAAG;AACrD,cAAM,WAAW,aAAa,CAAC;AAC/B,cAAM,OAAO,aAAa,aAAa,SAAS,CAAC;AACjD,eAAO,SAAS,QAAQ,IAAI,IAAI;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,eAAe,IAAI,KAAK,cAAc,MAAM,GAAG;AACrD,eAAO,aAAa,CAAC;AAAA,MACvB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAEhB,cAAM,UAAU,IAAI,KAAK;AACzB,cAAM,iBAAiB,QAAQ,MAAM,MAAM,EAAE,CAAC;AAC9C,YAAI,kBAAkB,eAAe,UAAU,KAAK;AAClD,iBAAO,eAAe,QAAQ,UAAU,EAAE,EAAE,KAAK;AAAA,QACnD;AACA,eAAO,QAAQ,MAAM,GAAG,GAAG,EAAE,QAAQ,UAAU,EAAE,EAAE,KAAK,IAAI;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,QAAQ,YAAY,IAAI,KAAK,GAAG;AACtC,eAAO,KAAK,KAAK,MAAM,OAAO;AAAA,MAChC;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,QAAQ,YAAY,IAAI,KAAK,GAAG;AACtC,eAAO,MAAM;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAMK,IAAM,eAAe,mBAAmB,OAAO;AAAA,EACpD,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,eAAe,IAAI,KAAK,cAAc,MAAM,GAAG;AACrD,eAAO,aAAa,aAAa,SAAS,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAMK,IAAM,aAAa,mBAAmB,OAAO;AAAA,EAClD,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,IAAI;AAAA,QACF,SAAS,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,QAC3C,UAAU,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,QAC5C,OAAO,EAAE,MAAM,UAAU,UAAU,MAAM;AAAA,MAC3C;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,eAAe,IAAI,KAAK,cAAc,MAAM,GAAG;AACrD,eAAO,aAAa,aAAa,SAAS,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAMF,IAAO,8BAAQ,WAAW;AAAA,EACxB,gBAAgB;AAAA,EAChB,eAAe,CAAC,UAAU,cAAc,UAAU;AAAA,EAClD,KAAK;AAAA,IACH,eAAe,CAAC,SAAS;AAAA,IACzB,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,YACV,WAAW,CAAC,aAAa;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;", "names": []}