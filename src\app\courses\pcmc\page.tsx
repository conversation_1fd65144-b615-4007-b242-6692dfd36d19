import { type Metadata } from "next";
import { CoursesPageHero } from "~/components/courses-components/courses-page-hero/courses-page-hero";
import { ProgramFeatureSection } from "~/components/courses-components/feature-cards-section";
import { TrainingProcessSection } from "~/components/courses-components/training-process-section";
import { WhyChooseUs } from "~/components/courses-components/why-choose-us";
import { pcmcProgramFeatureList } from "~/constants/courses";

export const metadata: Metadata = {
  title: "PCMC Courses in Bangalore | Expert Coaching at Aims Academy",
  description:
    "Enroll in Aims Academy's PCMC courses in Bangalore. Expert faculty, comprehensive syllabus coverage, and personalized mentoring ensure top results in Physics, Chemistry, Mathematics, and Computer Science.",
  keywords: [
    "PCMC Courses Bangalore",
    "PCMC Coaching Yelahanka",
    "Best PCMC Classes in Bangalore",
    "PCMC Integrated Courses",
    "PCMC Preparation Bangalore",
  ],
  openGraph: {
    title: "PCMC Courses in Bangalore | Expert Coaching at Aims Academy",
    description:
      "Join Aims Academy for top-notch PCMC coaching in Bangalore. Expert guidance, regular assessments, and proven success. Enroll now!",
    images: [
      {
        url: "/pcmc-og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy PCMC Courses",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "PCMC Courses in Bangalore | Expert Coaching at Aims Academy",
    description:
      "Achieve excellence in PCMC with Aims Academy's expert coaching in Bangalore. Comprehensive preparation and personalized mentoring.",
  },
};

const PcmcPage = () => {
  const heroProps = {
    heading: "PCMC",
    subheading: "Physics, Chemistry, Mathematics, and Computer Science",
    description:
      "Our PCMC program covers Physics, Chemistry, Mathematics, and Computer Science. Ideal for careers in engineering and technology. Join us to excel in your studies and secure a bright future.",
    buttons: {
      primary: {
        text: "Enroll Now",
        url: "#contact-form",
      },
      secondary: {
        text: "Learn More",
        url: "#contact-form",
      },
    },
    image: {
      src: "/programmer_courses.svg",
      alt: "PCMC Courses",
    },
  };

  return (
    <article>
      <CoursesPageHero {...heroProps} />
      <ProgramFeatureSection
        features={pcmcProgramFeatureList}
        imageSrc="/program-feature-img.png"
      />
      <WhyChooseUs />
      <TrainingProcessSection courseType="PCMC" />
    </article>
  );
};

export default PcmcPage;
