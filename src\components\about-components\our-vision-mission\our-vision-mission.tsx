"use client";

import { m, LazyMotion, domMax } from "framer-motion";
import { Eye, Target } from "lucide-react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "~/components/ui/card";

interface VisionMissionCardProps {
  icon: React.ReactNode;
  title: string;
  content: string;
  delay?: number;
}

const VisionMissionCard = ({
  icon,
  title,
  content,
  delay = 0,
}: VisionMissionCardProps) => (
  <m.div
    className="w-full md:w-1/2"
    initial={{ opacity: 0, y: 30 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay }}
    viewport={{ once: true, amount: 0.2 }}
  >
    <Card className="flex h-full flex-col shadow-lg">
      <CardHeader className="flex flex-col items-center gap-2">
        {icon}
        <CardTitle className="text-2xl font-bold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="text-center text-lg">{content}</CardContent>
    </Card>
  </m.div>
);

const cardsData = [
  {
    icon: <Eye size={48} className="text-green-600" />,
    title: "Our Vision",
    content:
      "Our vision is to create awesome and invincible brand value to our Organization in the educational system by making the young aspirants materialize their career goals through imparting quality education and commitment.",
    delay: 0,
  },
  {
    icon: <Target size={48} className="text-red-600" />,
    title: "Our Mission",
    content:
      "Our mission is to develop a conducive and nurturing environment for learners to attain academic excellence through advanced pedagogy and technology in learning processes.",
    delay: 0.2,
  },
];

const OurVisionMission = () => {
  return (
    <LazyMotion features={domMax}>
      <section className="container mx-auto px-4 lg:py-16">
        <div className="flex flex-col items-stretch justify-center gap-8 md:flex-row">
          {cardsData.map((card, index) => (
            <VisionMissionCard
              key={index}
              icon={card.icon}
              title={card.title}
              content={card.content}
              delay={card.delay}
            />
          ))}
        </div>
      </section>
    </LazyMotion>
  );
};

export default OurVisionMission;
