import { Card, CardContent } from "~/components/ui/card";

export default function TestInstructions() {
  return (
    <section className="mb-6">
      <h2 className="mb-6 text-2xl font-semibold">Test Instructions</h2>
      <Card>
        <CardContent className="p-6">
          <ul className="ml-6 list-disc space-y-2">
            <li>
              The test contains 180 multiple-choice questions from Physics,
              Chemistry, and Biology.
            </li>
            <li>
              Each question has 4 options, out of which only one is correct.
            </li>
            <li>
              Each correct answer carries 4 marks, and each incorrect answer
              results in a deduction of 1 mark.
            </li>
            <li>
              Use a blue/black ballpoint pen to mark your answers on the OMR
              sheet.
            </li>
            <li>
              Rough work should be done on the space provided in the test
              booklet.
            </li>
            <li>
              Calculator, mobile phones, or any electronic devices are not
              allowed during the test.
            </li>
            <li>
              After completing the test, check your answers with the provided
              solutions.
            </li>
            <li>
              Analyze your performance to identify strengths and areas for
              improvement.
            </li>
          </ul>
        </CardContent>
      </Card>
    </section>
  );
}
