// NOTE This file is auto-generated by Contentlayer

import type { Markdown, MDX, Image<PERSON>ieldData, IsoDateTimeString } from 'contentlayer2/core'
import * as Local from 'contentlayer2/source-files'

export { isType } from 'contentlayer2/client'

export type { Markdown, MDX, ImageFieldData, IsoDateTimeString }

/** Document types */
export type BlogAuthor = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'BlogAuthor'
  name: string
  bio: string
  avatar?: string | undefined
  role: string
  social?: any | undefined
  /** MDX file body */
  body: MDX
  slug: string
}

export type BlogCategory = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'BlogCategory'
  name: string
  description: string
  color: string
  icon?: string | undefined
  /** MDX file body */
  body: MDX
  slug: string
}

export type BlogPost = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'BlogPost'
  title: string
  description: string
  publishedAt: IsoDateTimeString
  updatedAt?: IsoDateTimeString | undefined
  featured: boolean
  draft: boolean
  category: string
  tags: string[]
  author: string
  image?: any | undefined
  seo?: any | undefined
  relatedPosts?: string[] | undefined
  /** MDX file body */
  body: MDX
  slug: string
  url: string
  categorySlug: string
  excerpt: string
  readingTime: number
  wordCount: number
}  

/** Nested types */
  

/** Helper types */

export type AllTypes = DocumentTypes | NestedTypes
export type AllTypeNames = DocumentTypeNames | NestedTypeNames

export type DocumentTypes = BlogAuthor | BlogCategory | BlogPost
export type DocumentTypeNames = 'BlogAuthor' | 'BlogCategory' | 'BlogPost'

export type NestedTypes = never
export type NestedTypeNames = never

export type DataExports = {
  allDocuments: DocumentTypes[]
  allBlogPosts: BlogPost[]
  allBlogCategories: BlogCategory[]
  allBlogAuthors: <AUTHORS>
}


export interface ContentlayerGenTypes {
  documentTypes: DocumentTypes
  documentTypeMap: DocumentTypeMap
  documentTypeNames: DocumentTypeNames
  nestedTypes: NestedTypes
  nestedTypeMap: NestedTypeMap
  nestedTypeNames: NestedTypeNames
  allTypeNames: AllTypeNames
  dataExports: DataExports
}

declare global {
  interface ContentlayerGen extends ContentlayerGenTypes {}
}

export type DocumentTypeMap = {
  BlogAuthor: BlogAuthor
  BlogCategory: BlogCategory
  BlogPost: BlogPost
}

export type NestedTypeMap = {

}

 