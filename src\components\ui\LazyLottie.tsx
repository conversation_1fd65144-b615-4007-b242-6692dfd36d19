"use client";
import { type LottieComponentProps } from "lottie-react";
import { Suspense, lazy, useEffect, useState } from "react";
import { Skeleton } from "./skeleton";

const LazyLottieComponent = lazy(() => import("lottie-react"));

interface LottieProps<T extends Record<string, unknown>> {
  getAnimationData: () => Promise<T>;
  id: string;
  className?: string;
}

export function LazyLottie<T extends Record<string, unknown>>({
  getAnimationData,
  id,
  ref,
  className,
  ...props
}: LottieProps<T> & Omit<LottieComponentProps, "animationData">) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only run on client-side
    if (typeof window === "undefined") return;

    let isMounted = true;

    const loadData = async () => {
      try {
        void import("lottie-react"); // Trigger the library lazy load even if the animationData is not ready
        const animationData = await getAnimationData();

        if (isMounted) {
          setData(animationData);
          setIsLoading(false);
        }
      } catch (error) {
        console.error(`Failed to load animation: ${id}`, error);
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    void loadData();

    return () => {
      isMounted = false;
    };
  }, [getAnimationData, id]);

  if (isLoading || !data) return <Skeleton className="h-40" />;

  return (
    <Suspense fallback={<Skeleton className="h-40" />}>
      <LazyLottieComponent
        className={className}
        animationData={data}
        {...props}
      />
    </Suspense>
  );
}
