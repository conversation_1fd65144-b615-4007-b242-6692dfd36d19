import DoubleUnderline from "~/components/animata/text/double-underline";
import { cn } from "~/lib/utils";

export default function WelcomeMessageHome() {
  return (
    <section>
      <div className="flex flex-col items-center gap-3 text-center">
        {/* SEO-Optimized H1 for Homepage */}
        <h1 className="text-2xl font-bold text-primary sm:text-3xl lg:text-4xl">
          Top NEET Coaching in Bangalore | Aims Academy - Best PU College
        </h1>

        {/* Brand Message as H2 with Visual Styling */}
        <DoubleUnderline>
          <h2
            className={cn(
              "inline-block text-[#7A4100] dark:text-[#FF9E3F]" /* Much darker orange for better contrast in light mode, lighter in dark mode */,
              "font-cursive text-lg italic sm:text-xl lg:text-2xl",
            )}
          >
            Transforming Aspirants into Achievers
          </h2>
        </DoubleUnderline>
      </div>
    </section>
  );
}
