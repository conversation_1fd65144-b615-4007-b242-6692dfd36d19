"use client"; // Error boundaries must be Client Components

import { useEffect } from "react";
import { Button } from "~/components/ui/button";
import Link from "next/link";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Page Error:", error);
  }, [error]);

  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center space-y-4 text-center">
      <h1 className="text-2xl font-bold">Oops! Something went wrong</h1>
      <p className="max-w-md text-muted-foreground">
        We&apos;re experiencing a temporary issue. Please try refreshing the
        page or go back to our homepage.
      </p>
      <div className="flex gap-4">
        <Button onClick={() => reset()}>Try again</Button>
        <Button variant="outline" asChild>
          <Link href="/">Go to Homepage</Link>
        </Button>
      </div>
      <p className="text-sm text-muted-foreground">
        If the problem persists, please contact us at{" "}
        <a href="tel:9008466404" className="text-primary hover:underline">
          9008466404
        </a>
      </p>
    </div>
  );
}
