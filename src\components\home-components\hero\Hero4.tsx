import { ArrowRightSquare } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "~/components/ui/tabs";
import { Button } from "~/components/ui/button";

interface Hero4Props {
  heading?: string;
  description?: string;
}

export async function Hero4({
  heading = "Why Choose Aims Academy?",
  description = "Choose Aims Academy for unparalleled NEET and PU coaching in Bangalore. Our experienced instructors, customized learning plans, and extensive resources empower students to excel and reach their academic aspirations.",
}: Hero4Props) {
  return (
    <section className="py-8">
      <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-20">
        <div className="flex justify-center">
          <Image
            src="/hero-home-2.png"
            alt="Why choose Aims Academy for education"
            width={600}
            height={1000}
            priority={true}
            loading="eager"
            fetchPriority="high"
            style={{ height: "auto" }}
          />
        </div>
        <div className="mx-auto flex flex-col items-center text-center md:ml-auto lg:max-w-3xl lg:items-start lg:text-left">
          <h2 className="my-6 text-pretty text-4xl font-bold lg:text-6xl xl:text-7xl">
            {heading}
          </h2>
          <p className="mb-8 max-w-xl text-muted-foreground">{description}</p>

          <Tabs
            defaultValue="vision"
            className="flex w-full max-w-lg flex-col items-center justify-start"
          >
            <TabsList className="flex justify-center lg:justify-start">
              <TabsTrigger value="vision">Our Vision</TabsTrigger>
              <TabsTrigger value="mission">Our Mission</TabsTrigger>
            </TabsList>
            <TabsContent value="vision">
              <p className="mt-4 text-muted-foreground">
                Our vision is to create an awesome and invincible brand value
                for our organization in the educational system by making young
                aspirants materialize their career goals through imparting
                quality education and commitment.
              </p>
            </TabsContent>
            <TabsContent value="mission">
              <p className="mt-4 text-muted-foreground">
                Our mission is to develop a conducive and nurturing environment
                for learners to attain academic excellence through advanced
                pedagogy and technology in learning processes.
              </p>
            </TabsContent>
          </Tabs>

          <div className="mt-6">
            <Link href="/about">
              <Button variant="default" className="w-full sm:w-auto">
                <span>About Us</span>{" "}
                <ArrowRightSquare className="ml-2 size-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
