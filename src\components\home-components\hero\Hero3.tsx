import { PhoneCallIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import type { ReviewCardProps } from "~/components/react-google-reviews/review-card";
import { ReviewBadge } from "~/components/react-google-reviews/reviews-badge";
import { ReviewsCarouselClient } from "~/components/react-google-reviews/reviews-carousel";
import { Button } from "~/components/ui/button";
import { SITE_CONFIG } from "~/constants/siteConfig";
import GoogleIcon from "~/icons/GoogleIcon";
import { EXAMPLE_REVIEWS } from "~/constants/reviews-example";
import type { Review } from "~/lib/featurable/types";
import { getGoogleReviews } from "~/lib/shared-data";

interface Hero3Props {
  heading?: string;
  description?: string;
}

export async function Hero3({
  heading = "Top NEET Long Term & PU Integrated Coaching Center in Bangalore",
  description = "Achieve your academic goals with Bangalore's leading coaching center for NEET and PU exams. Expert faculty, personalized mentorship, and comprehensive study materials ensure top results.",
}: Hero3Props) {
  // Try to fetch reviews, but fall back to example data if needed
  let reviewsData;
  try {
    reviewsData = await getGoogleReviews();
  } catch (error) {
    console.error("Error fetching reviews, using fallback data", error);
    reviewsData = EXAMPLE_REVIEWS;
  }

  // Transform reviews into the expected format
  const transformedReviews: ReviewCardProps[] =
    reviewsData?.reviews?.slice(0, 13).map((review: Review) => ({
      name: review.reviewer.displayName,
      date: new Date(review.createTime).toDateString(),
      rating: review.starRating,
      comment: review.comment,
      avatarUrl: review.reviewer.profilePhotoUrl,
    })) || [];

  return (
    <section>
      <div className="grid items-center gap-6 lg:grid-cols-2 lg:gap-10">
        <div className="mx-auto flex flex-col items-center text-center md:ml-auto lg:max-w-3xl lg:items-start lg:text-left">
          <h2 className="my-4 text-4xl font-bold lg:text-5xl xl:text-6xl">
            Comprehensive{" "}
            <Link
              href="/courses/long-term-for-neet-repeaters"
              className="animate-gradient bg-gradient-to-r from-orange-400 via-purple-500 to-blue-500 bg-clip-text text-transparent"
              prefetch={false}
            >
              NEET Long Term
            </Link>{" "}
            &{" "}
            <Link
              href="/courses/pu-course"
              className="animate-gradient bg-gradient-to-r from-teal-400 via-purple-500 to-blue-600 bg-clip-text text-transparent"
              prefetch={false}
            >
              PU Integrated Programs
            </Link>
          </h2>
          <p className="mb-4 max-w-xl text-muted-foreground lg:text-xl">
            {description}
          </p>
          <div className="mb-6">
            <div className="flex flex-wrap items-center justify-center gap-6">
              <ReviewBadge
                rating={reviewsData?.averageRating ?? 0}
                totalReviews={reviewsData?.totalReviewCount ?? 0}
                userImages={
                  reviewsData?.reviews
                    ?.slice(0, 5)
                    .map((review) => review.reviewer.profilePhotoUrl ?? "") ??
                  []
                }
              />
              <div className="flex w-full flex-col items-start gap-4 sm:w-auto">
                <Link
                  href={SITE_CONFIG.maps.url}
                  target="_blank"
                  className="w-full"
                >
                  <Button variant="secondary" className="w-full">
                    Leave a review <GoogleIcon className="ml-2" />
                  </Button>
                </Link>

                {/* Contact Us Button */}
                <Link
                  href={`tel:${SITE_CONFIG.contact.phone1}`}
                  className="w-full"
                >
                  <Button variant="default" className="w-full">
                    <span>Contact Us</span>
                    <PhoneCallIcon className="ml-2 h-5 w-5 text-current" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <Image
            src="/hero-home-1.png"
            alt="NEET and PU coaching at Aims Academy"
            width={700}
            height={600}
            priority={true}
            loading="eager"
            fetchPriority="high"
            style={{ height: "auto" }}
          />
        </div>
      </div>

      <ReviewsCarouselClient reviews={transformedReviews} />
    </section>
  );
}
