import { <PERSON><PERSON> } from "~/components/ui/button";
import { BentoGrid, BentoGridItem } from "~/components/ui/bento-grid";
import {
  ClipboardCheckIcon,
  BookOpen,
  GraduationCap,
  Users,
  Clock,
  Home,
  School,
} from "lucide-react";
import { SITE_CONFIG } from "~/constants/siteConfig";
import { PAGES, COURSES_PAGES } from "~/constants/pages";
import Link from "next/link";
import Image from "next/image";
import { FacultyAvatars } from "./FacultyAvatars";

export function Hero5() {
  return (
    <section className="py-12">
      <div className="text-center">
        <h2 className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
          Your Hustle, Our Strategy
        </h2>
      </div>

      <div className="mx-auto mt-12 max-w-6xl">
        <BentoGrid className="gap-6">
          {items.map((item, i) => {
            const gridItemClass = [0].includes(i)
              ? "rounded-xl border-2 border-blue-500 shadow-lg md:col-span-2"
              : [2].includes(i)
                ? "rounded-xl md:col-span-2"
                : "";

            return (
              <div key={i} className={gridItemClass}>
                <Link href={item.url || "#"} className="block h-full">
                  <BentoGridItem
                    title={
                      <div className="flex items-center gap-3">
                        {item.icon}
                        <span>{item.title}</span>
                      </div>
                    }
                    description={item.description}
                    header={item.header}
                  />
                </Link>
              </div>
            );
          })}
        </BentoGrid>
      </div>

      <div className="mt-8 text-center">
        <Link href={`tel:${SITE_CONFIG.contact.phone1}`}>
          <Button className="rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-lg font-semibold text-white shadow-lg transition-all hover:scale-105 hover:shadow-xl">
            Enroll Now <ClipboardCheckIcon className="ml-2 h-6 w-6" />
          </Button>
        </Link>
      </div>
    </section>
  );
}

const items = [
  {
    title: "ONE YEAR Intense NEET Long Term Coaching for Repeaters",
    description: "Individual care, Expert teaching faculty",
    icon: <BookOpen className="h-6 w-6 text-blue-600" />,
    url: COURSES_PAGES.NEET_FOR_REPEATERS.url,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl">
        <Image
          src="/neet-long-term-section.png"
          alt="NEET Long Term Coaching at Aims Academy"
          width={400}
          height={200}
          loading="lazy"
          priority={false}
          style={{ height: "auto" }}
        />
      </div>
    ),
  },
  {
    title: "JEE Integrated Coaching",
    description: "Students with IIT Aspirations",
    icon: <Users className="h-6 w-6 text-yellow-600" />,
    url: COURSES_PAGES.JEE_FOR_PU_STUDENTS.url,
    header: (
      <div className="relative flex h-40 items-center justify-center overflow-hidden rounded-xl bg-gradient-to-br from-yellow-50 to-orange-50 transition-transform hover:scale-[1.02]">
        <Image
          src="/jee-mains-section.jpg"
          alt="JEE Integrated Coaching at Aims Academy"
          width={400}
          height={200}
          className="max-h-full max-w-full object-scale-down"
          loading="lazy"
          priority={false}
        />
      </div>
    ),
  },
  {
    title: "Guided by Bangalore's Top-tier Faculty",
    description: "20+ Years Average Teaching Experience",
    icon: <GraduationCap className="h-6 w-6 text-purple-600" />,
    url: PAGES.ABOUT.url,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-purple-50 to-pink-50">
        <FacultyAvatars />
      </div>
    ),
  },
  {
    title: "NEET Integrated Coaching",
    description: "Medical Entrance Preparation",
    icon: <Clock className="h-6 w-6 text-red-600" />,
    url: COURSES_PAGES.NEET_FOR_PU_STUDENTS.url,
    header: (
      <div className="relative flex h-40 items-center justify-center overflow-hidden rounded-xl bg-gradient-to-br from-red-50 to-pink-50 transition-transform hover:scale-[1.02]">
        <Image
          src="/neet-section.jpg"
          alt="NEET Integrated Coaching at Aims Academy"
          width={400}
          height={200}
          className="max-h-full max-w-full object-scale-down"
          loading="lazy"
          priority={false}
        />
      </div>
    ),
  },
  {
    title: "Separate Campuses for Boys & Girls",
    description: "Safe and focused learning environment",
    icon: <School className="h-6 w-6 text-green-600" />,
    url: PAGES.GALLERY.url,
    header: (
      <div className="relative flex h-40 items-center justify-center overflow-hidden rounded-xl bg-gradient-to-br from-green-50 to-teal-50 transition-transform hover:scale-[1.02]">
        <Image
          src="/gallery/aims_academy_campus.png"
          alt="Aims Academy Separate Campuses"
          width={300}
          height={160}
          className="h-full w-full object-cover"
          loading="lazy"
          priority={false}
        />
      </div>
    ),
  },
  {
    title: "Residential Options",
    description: "Residential, Semi-Residential, Day-Boarder",
    icon: <Home className="h-6 w-6 text-indigo-600" />,
    url: PAGES.CONTACT.url,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-50 to-violet-50 transition-transform hover:scale-[1.02]">
        <Home className="h-16 w-16 text-indigo-600" />
      </div>
    ),
  },
];
