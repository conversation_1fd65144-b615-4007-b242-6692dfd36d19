import { type Metadata } from "next";
import {
  allBlogPosts,
  allBlogCategories,
  type BlogCategory,
} from ".contentlayer/generated";
import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";
import Script from "next/script";

import BlogSidebar from "~/components/blog/blog-sidebar";
import BlogCategories from "~/components/blog/blog-categories";
import { getBlogMetadata } from "~/lib/blog-utils";
import BlogGrid from "~/components/blog/blog-grid";

// ============================================================================
// Static Params Generation
// ============================================================================

export function generateStaticParams(): Array<{ category: string }> {
  return allBlogCategories.map((category: BlogCategory) => ({
    category: category.slug,
  }));
}

// ============================================================================
// Metadata Generation
// ============================================================================

export async function generateMetadata({
  params,
}: {
  params: { category: string };
}): Promise<Metadata> {
  const { category: categorySlug } = params;
  const categoryFound = allBlogCategories.find(
    (cat): cat is BlogCategory => cat.slug === categorySlug,
  );

  if (!categoryFound) {
    return {
      title: "Category Not Found",
      description: "The requested blog category could not be found.",
    };
  }

  const category: BlogCategory = categoryFound;

  const categoryPosts = allBlogPosts.filter(
    (post) => !post.draft && post.categorySlug === categorySlug,
  );

  return {
    title: `${category.name} | NEET Blog - Aims Academy`,
    description: `${category.description} - ${categoryPosts.length} articles on ${category.name.toLowerCase()} for NEET preparation.`,
    keywords: [
      `NEET ${category.name}`,
      `${category.name} Study Tips`,
      `${category.name} NEET Preparation`,
      "NEET Blog",
      "Aims Academy",
      "Medical Entrance Preparation",
    ],
    alternates: {
      canonical: `${SITE_DOMAIN}/blog/${categorySlug}`,
    },
    openGraph: {
      title: `${category.name} | NEET Blog`,
      description: category.description,
      url: `${SITE_DOMAIN}/blog/${categorySlug}`,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: `/blog/categories/${categorySlug}-og.jpg`,
          width: 1200,
          height: 630,
          alt: `${category.name} - NEET Blog Category`,
        },
      ],
      locale: "en_IN",
      type: "website",
    },
  };
}

// ============================================================================
// Blog Category Page Component
// ============================================================================

export default async function BlogCategoryPage({
  params,
}: {
  params: { category: string };
}): Promise<JSX.Element> {
  const { category: categorySlug } = params;

  // Find the category with type guard
  const categoryFound = allBlogCategories.find(
    (cat): cat is BlogCategory => cat.slug === categorySlug,
  );

  if (!categoryFound) {
    return <div>Category not found</div>;
  }

  const category: BlogCategory = categoryFound;

  // Get posts for this category
  const categoryPosts = allBlogPosts
    .filter((post) => !post.draft && post.categorySlug === categorySlug)
    .sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
    );

  // Get all categories for navigation
  const allCategories: BlogCategory[] = allBlogCategories;

  // Get recent posts from other categories for sidebar
  const otherPosts = allBlogPosts
    .filter((post) => !post.draft && post.categorySlug !== categorySlug)
    .sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
    )
    .slice(0, 5);

  // Get blog metadata
  const blogMetadata = getBlogMetadata(
    allBlogPosts.filter((post) => !post.draft),
  );

  // Generate structured data
  const categorySchema: Record<string, unknown> = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: `${category.name} - NEET Blog`,
    description: category.description,
    url: `${SITE_DOMAIN}/blog/${categorySlug}`,
    mainEntity: {
      "@type": "ItemList",
      numberOfItems: categoryPosts.length,
      itemListElement: categoryPosts.map((post, index) => ({
        "@type": "ListItem",
        position: index + 1,
        item: {
          "@type": "BlogPosting",
          headline: post.title,
          description: post.description,
          url: `${SITE_DOMAIN}${post.url}`,
          datePublished: post.publishedAt,
          dateModified: post.updatedAt ?? post.publishedAt,
          author: {
            "@type": "Person",
            name: post.author,
          },
        },
      })),
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: SITE_DOMAIN,
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Blog",
          item: `${SITE_DOMAIN}/blog`,
        },
        {
          "@type": "ListItem",
          position: 3,
          name: category.name,
          item: `${SITE_DOMAIN}/blog/${categorySlug}`,
        },
      ],
    },
  };

  return (
    <>
      <article className="min-h-screen bg-background">
        {/* Category Header */}
        <div className="bg-gradient-to-r from-primary/10 to-accent/10 py-16">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl text-center">
              <h1 className="mb-4 text-4xl font-bold text-primary md:text-5xl">
                {category.name}
              </h1>
              <p className="mb-6 text-lg text-muted-foreground">
                {category.description}
              </p>
              <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
                <span>{categoryPosts.length} articles</span>
                <span>•</span>
                <span>Updated regularly</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Categories Navigation */}
              <BlogCategories
                categories={allCategories}
                currentCategory={categorySlug}
                className="mb-8"
              />

              {/* Posts Grid */}
              {categoryPosts.length > 0 ? (
                <BlogGrid posts={categoryPosts} variant="default" />
              ) : (
                <div className="py-12 text-center">
                  <h2 className="mb-4 text-2xl font-semibold text-muted-foreground">
                    No articles yet
                  </h2>
                  <p className="text-muted-foreground">
                    We&apos;re working on adding more content to this category.
                    Check back soon!
                  </p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <BlogSidebar
                categories={allCategories}
                recentPosts={otherPosts}
                popularPosts={blogMetadata.popularPosts}
                currentCategory={categorySlug}
              />
            </div>
          </div>
        </div>
      </article>

      {/* Structured Data */}
      <Script
        id="category-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(categorySchema),
        }}
      />
    </>
  );
}
