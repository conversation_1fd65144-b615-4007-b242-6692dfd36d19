/**
 * Blog System TypeScript Types
 * Comprehensive type definitions for the NEET blog system
 */

// ============================================================================
// Core Blog Types
// ============================================================================

export interface BlogPost {
  slug: string;
  title: string;
  description: string;
  content: string;
  excerpt: string;
  publishedAt: string;
  updatedAt?: string;
  featured: boolean;
  draft: boolean;
  readingTime: number;
  wordCount: number;
  category: BlogCategory;
  tags: string[];
  author: BlogAuthor;
  seo: BlogSEO;
  image?: BlogImage;
  relatedPosts?: string[];
}

export interface BlogCategory {
  slug: string;
  name: string;
  description: string;
  color: string;
  icon?: string;
  postCount?: number;
}

export interface BlogAuthor {
  name: string;
  bio: string;
  avatar?: string;
  role: string;
  social?: {
    twitter?: string;
    linkedin?: string;
    email?: string;
  };
}

export interface BlogImage {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  caption?: string;
}

export interface BlogSEO {
  title?: string;
  description?: string;
  keywords: string[];
  canonicalUrl?: string;
  noindex?: boolean;
  ogImage?: string;
}

// ============================================================================
// Content Management Types
// ============================================================================

export interface BlogFrontmatter {
  title: string;
  description: string;
  publishedAt: string;
  updatedAt?: string;
  featured?: boolean;
  draft?: boolean;
  category: string;
  tags: string[];
  author: string;
  image?: {
    src: string;
    alt: string;
    caption?: string;
  };
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
    canonicalUrl?: string;
    noindex?: boolean;
  };
  relatedPosts?: string[];
}

export interface BlogMetadata {
  totalPosts: number;
  totalCategories: number;
  totalTags: number;
  lastUpdated: string;
  featuredPosts: BlogPost[];
  popularPosts: BlogPost[];
  recentPosts: BlogPost[];
}

// ============================================================================
// Search and Filtering Types
// ============================================================================

export interface BlogSearchParams {
  query?: string;
  category?: string;
  tag?: string;
  author?: string;
  page?: number;
  limit?: number;
  sortBy?: 'publishedAt' | 'updatedAt' | 'title' | 'readingTime';
  sortOrder?: 'asc' | 'desc';
}

export interface BlogSearchResult {
  posts: BlogPost[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface BlogCardProps {
  post: BlogPost;
  variant?: 'default' | 'featured' | 'compact';
  showExcerpt?: boolean;
  showAuthor?: boolean;
  showReadingTime?: boolean;
  showCategory?: boolean;
  className?: string;
}

export interface BlogCategoryListProps {
  categories: BlogCategory[];
  currentCategory?: string;
  showPostCount?: boolean;
  className?: string;
}

export interface BlogTagCloudProps {
  tags: Array<{
    name: string;
    count: number;
    slug: string;
  }>;
  maxTags?: number;
  className?: string;
}

export interface BlogPaginationProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  className?: string;
}

// ============================================================================
// RSS and Sitemap Types
// ============================================================================

export interface RSSItem {
  title: string;
  description: string;
  link: string;
  pubDate: string;
  category: string;
  author: string;
  guid: string;
}

export interface BlogSitemapEntry {
  url: string;
  lastModified: string;
  changeFrequency: 'daily' | 'weekly' | 'monthly';
  priority: number;
}

// ============================================================================
// Configuration Types
// ============================================================================

export interface BlogConfig {
  postsPerPage: number;
  featuredPostsCount: number;
  popularPostsCount: number;
  recentPostsCount: number;
  relatedPostsCount: number;
  excerptLength: number;
  enableComments: boolean;
  enableNewsletter: boolean;
  enableSearch: boolean;
  enableRSS: boolean;
  enableSitemap: boolean;
  categories: BlogCategory[];
  authors: BlogAuthor[];
}

// ============================================================================
// Utility Types
// ============================================================================

export type BlogPostStatus = 'draft' | 'published' | 'archived';
export type BlogSortField = 'publishedAt' | 'updatedAt' | 'title' | 'readingTime';
export type BlogSortOrder = 'asc' | 'desc';

export interface BlogStats {
  totalViews: number;
  totalShares: number;
  averageReadingTime: number;
  mostPopularCategory: string;
  mostUsedTags: string[];
}
