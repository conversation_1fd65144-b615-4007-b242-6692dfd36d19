"use client";
import { useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";

// Error boundaries must be Client Components

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Global Error:", error);
  }, [error]);

  return (
    // global-error must include html and body tags
    <html lang="en">
      <head>
        <title>Error - Aims Academy</title>
        <meta name="robots" content="noindex, nofollow" />
      </head>
      <body className="flex min-h-screen items-center justify-center bg-background">
        <div className="space-y-4 p-8 text-center">
          <h1 className="text-2xl font-bold">Aims Academy - Temporary Issue</h1>
          <p className="max-w-md text-muted-foreground">
            We&apos;re experiencing a temporary technical issue. Please try
            refreshing the page.
          </p>
          <div className="space-y-2">
            <Button onClick={() => reset()}>Try again</Button>
            <p className="text-sm text-muted-foreground">
              Or contact us at{" "}
              <a href="tel:9008466404" className="text-primary hover:underline">
                9008466404
              </a>
            </p>
          </div>
        </div>
      </body>
    </html>
  );
}
