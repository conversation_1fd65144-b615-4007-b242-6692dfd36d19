// NOTE This file is auto-generated by Contentlayer

import blog__examStrategy__neetTimeManagementStrategiesMdx from './blog__exam-strategy__neet-time-management-strategies.mdx.json' with { type: 'json' }
import blog__newsUpdates__neet_2025ImportantDatesUpdatesMdx from './blog__news-updates__neet-2025-important-dates-updates.mdx.json' with { type: 'json' }
import blog__studyTips__effectiveNeetStudySchedule_2025Mdx from './blog__study-tips__effective-neet-study-schedule-2025.mdx.json' with { type: 'json' }
import blog__studyTips__memoryTechniquesNeetBiologyMdx from './blog__study-tips__memory-techniques-neet-biology.mdx.json' with { type: 'json' }
import blog__subjectGuides__neetBiologyHighYieldTopics_2025Mdx from './blog__subject-guides__neet-biology-high-yield-topics-2025.mdx.json' with { type: 'json' }
import blog__subjectGuides__neetChemistryOrganicReactionsGuideMdx from './blog__subject-guides__neet-chemistry-organic-reactions-guide.mdx.json' with { type: 'json' }
import blog__successStories__neetTopper_2024InterviewMdx from './blog__success-stories__neet-topper-2024-interview.mdx.json' with { type: 'json' }

export const allBlogPosts = [blog__examStrategy__neetTimeManagementStrategiesMdx, blog__newsUpdates__neet_2025ImportantDatesUpdatesMdx, blog__studyTips__effectiveNeetStudySchedule_2025Mdx, blog__studyTips__memoryTechniquesNeetBiologyMdx, blog__subjectGuides__neetBiologyHighYieldTopics_2025Mdx, blog__subjectGuides__neetChemistryOrganicReactionsGuideMdx, blog__successStories__neetTopper_2024InterviewMdx]
