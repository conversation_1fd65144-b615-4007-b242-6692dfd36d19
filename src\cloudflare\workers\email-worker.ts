/* eslint-disable */

import { <PERSON>o } from "hono";
import { cors } from "hono/cors";
// @ts-ignore: Cannot find module 'cloudflare:email' or its corresponding type declarations.
import { EmailMessage } from "cloudflare:email";
import { createMimeMessage } from "mimetext";

const worker = new Hono();

worker.use("*", (c, next) => {
  const origins =
    // @ts-ignore
    c.env.ALLOWED_ORIGINS == "*" ? "*" : c.env.ALLOWED_ORIGINS.split(",");
  // @ts-ignore
  const corsMiddleware = cors({ origins });
  return corsMiddleware(c, next);
});

worker.post("/send", async (c) => {
  const text = await c.req.text();
  const body = JSON.parse(text);

  if (!body.name || !body.phone) {
    c.status(400);
    return c.json({
      status: "error",
      message: "Name and phone are required.",
    });
  }

  // Get the form source and use it directly
  const formSource = body.formSource || "default";

  // Log the request for debugging
  console.log("Email request received:", {
    formSource,
    name: body.name,
    phone: body.phone,
    email: body.email,
    course: body.course,
    timestamp: new Date().toISOString(),
  });

  // Format the email content using HTML
  const emailContent = `
    <html>
      <head>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #f9f9f9;
          }
          h1 {
            color: #007BFF;
          }
          .source {
            background-color: #e9f5ff;
            padding: 8px 12px;
            border-radius: 6px;
            display: inline-block;
            margin-top: 5px;
            font-weight: bold;
          }
          .details {
            margin-top: 20px;
          }
          .details p {
            margin: 10px 0;
          }
          .footer {
            margin-top: 30px;
            font-size: 0.9em;
            color: #777;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>New Contact Form Submission</h1>
          <div class="source">
            Source: ${formSource}
          </div>
          <div class="details">
            <p><strong>Name:</strong> ${body.name}</p>
            <p><strong>Phone:</strong> ${body.phone}</p>
            <p><strong>Email:</strong> ${body.email || "N/A"}</p>
            <p><strong>Course:</strong> ${body.course || "N/A"}</p>
            <p><strong>Message:</strong> ${body.message || "N/A"}</p>
            <p><strong>Form ID:</strong> ${body.formSource || "default"}</p>
          </div>
          <div class="footer">
            <p>This email was sent from your website. Form: ${formSource}</p>
          </div>
        </div>
      </body>
    </html>
  `;

  const msg = createMimeMessage();
  // @ts-ignore
  msg.setSender({ name: c.env.SENDER_NAME, addr: c.env.SENDER_ADDRESS });
  // @ts-ignore
  msg.setRecipient(c.env.RECIPIENT_ADDRESS);
  msg.setSubject("New Contact Form Submission");
  msg.addMessage({
    contentType: "text/html",
    data: emailContent,
  });

  const message = new EmailMessage(
    // @ts-ignore
    c.env.SENDER_ADDRESS,
    // @ts-ignore
    c.env.RECIPIENT_ADDRESS,
    msg.asRaw(),
  );

  try {
    // @ts-ignore
    await c.env.SEB.send(message);

    // Log successful email sending
    console.log("Email sent successfully:", {
      formSource,
      // @ts-ignore
      recipient: c.env.RECIPIENT_ADDRESS,
      timestamp: new Date().toISOString(),
    });
  } catch (e) {
    // Log error
    console.error("Email failed to send:", {
      formSource,
      // @ts-ignore
      error: e.message,
      timestamp: new Date().toISOString(),
    });

    c.status(500);
    return c.json({
      status: "error",
      message: "Email failed to send",
      // @ts-ignore
      error_details: e.message,
    });
  }

  return c.json({
    status: "success",
    message: "Email sent successfully",
  });
});

export default worker;
