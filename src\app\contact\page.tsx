import { type Metadata } from "next";

import { ContactInfoBlock } from "~/components/contact-components/contact-info-block/contact-info-block";
import ContactUsForm from "~/components/contact-components/contact-us-form/contact-us-form";
import GoogleMap from "~/components/contact-components/google-map-iframe/GoogleMap";
import { TextGenerateEffect } from "~/components/ui/text-generate-effect";
import { SITE_DOMAIN } from "~/constants/siteConfig";

export const metadata: Metadata = {
  title: "Contact Us",
  description:
    "Get in touch with Aims Academy Bangalore for inquiries about PU, NEET, JEE, and KCET coaching. Call us at +91 9008266404 or visit our campus in Yelahanka.",
  keywords: [
    "Contact Aims Academy Bangalore",
    "PU Coaching Contact",
    "NEET Coaching Contact",
    "JEE Coaching Contact",
    "KCET Coaching Contact",
    "Yelahanka Coaching Center",
  ],
  alternates: {
    canonical: "/contact",
  },
  openGraph: {
    title: "Contact Us | Aims Academy Bangalore",
    description:
      "Reach out to Aims Academy Bangalore for PU, NEET, JEE, and KCET coaching. Call +91 9008266404 or visit us in Yelahanka.",
    url: `${SITE_DOMAIN}/contact`,
    siteName: "Aims Academy",

    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Contact Us | Aims Academy Bangalore",
    description:
      "Get in touch with Aims Academy Bangalore for PU, NEET, JEE, and KCET coaching. Call +91 9008266404 or visit us in Yelahanka.",
  },
};

const ContactPage = () => {
  return (
    <section className="py-4">
      <h1 className="mb-4 mt-1 text-balance text-3xl font-semibold md:text-4xl">
        <TextGenerateEffect words="Contact us - Speak with Our Friendly Team" />
      </h1>
      <section className="flex flex-col gap-8 pb-16 md:flex-row">
        <div className="flex-1">
          <ContactUsForm formId="contact-page" />
        </div>
        <div className="flex-1">
          <h2 className="mb-4 text-center text-xl font-bold">Find Us Here</h2>
          <div className="overflow-hidden">
            <GoogleMap />
          </div>
        </div>
      </section>
      <ContactInfoBlock />
    </section>
  );
};

export default ContactPage;
