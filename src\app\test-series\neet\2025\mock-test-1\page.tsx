import { type Metada<PERSON> } from "next";
import <PERSON>rip<PERSON> from "next/script";
import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";
import { generateCourseSchema } from "~/lib/structured-data";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { cn } from "~/lib/utils";
import { buttonVariants } from "~/components/ui/button";
import MockTestDownload from "~/components/test-series/mock-test-download";
import TestDetails from "~/components/test-series/test-details";
import TestSections from "~/components/test-series/test-sections";
import TestInstructions from "~/components/test-series/test-instructions";
import type { MockTestDetails as MockTestDetailsType } from "~/types/test-series";

// Define metadata for SEO
export const metadata: Metadata = {
  title: "NEET 2025 Mock Test 1 | Aims Academy",
  description:
    "Download NEET 2025 Mock Test 1 with detailed solutions. Practice with exam-like questions and improve your score for NEET 2025.",
  keywords: [
    "NEET 2025 Mock Test 1",
    "NEET 2025 Practice Test",
    "NEET 2025 Sample Paper",
    "NEET 2025 Preparation",
    "NEET 2025 Question Paper",
    "NEET 2025 Solutions",
    "Aims Academy Test Series",
  ],
  alternates: {
    canonical: `${SITE_DOMAIN}/test-series/neet/2025/mock-test-1`,
  },
  openGraph: {
    title: "NEET 2025 Mock Test 1 | Aims Academy",
    description:
      "Download NEET 2025 Mock Test 1 with detailed solutions. Practice with exam-like questions and improve your score for NEET 2025.",
    url: `${SITE_DOMAIN}/test-series/neet/2025/mock-test-1`,
    siteName: "Aims Academy",
    images: [
      {
        url: `${SITE_DOMAIN}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: "Aims Academy NEET 2025 Mock Test 1",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NEET 2025 Mock Test 1 | Aims Academy",
    description:
      "Download NEET 2025 Mock Test 1 with detailed solutions. Practice with exam-like questions and improve your score for NEET 2025.",
    images: [`${SITE_DOMAIN}/og-image.jpg`],
  },
};

// Define mock test details
const mockTestDetails: MockTestDetailsType = {
  title: "NEET 2025 Mock Test 1",
  description:
    "Full-length mock test with 180 questions covering Physics, Chemistry, and Biology.",
  totalQuestions: 180,
  totalMarks: 720,
  difficultyLevel: "Moderate",
  duration: "3 hours",
  formId: "neet-2025-mock-test-1",
  sections: [
    {
      name: "Physics",
      questions: 45,
      topics: "Mechanics, Electrodynamics, Optics, Modern Physics",
    },
    {
      name: "Chemistry",
      questions: 45,
      topics: "Physical Chemistry, Organic Chemistry, Inorganic Chemistry",
    },
    {
      name: "Biology (Botany)",
      questions: 45,
      topics: "Plant Physiology, Genetics, Ecology, Cell Biology",
    },
    {
      name: "Biology (Zoology)",
      questions: 45,
      topics: "Human Physiology, Reproduction, Evolution, Biotechnology",
    },
  ],
};

export default function NEET2025MockTest1Page() {
  return (
    <article className="mx-auto max-w-6xl">
      {/* Hero Section */}
      <section className="my-6">
        <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
          <div>
            <h1 className="text-3xl font-bold md:text-4xl">
              {mockTestDetails.title}
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              {mockTestDetails.description}
            </p>
          </div>
          <Link
            href="/test-series/neet/2025"
            className={cn(
              buttonVariants({ variant: "outline" }),
              "flex items-center gap-2",
            )}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to All Tests
          </Link>
        </div>
      </section>

      {/* Test Details Section */}
      <TestDetails
        totalQuestions={mockTestDetails.totalQuestions}
        duration={mockTestDetails.duration}
        totalMarks={mockTestDetails.totalMarks}
        difficultyLevel={mockTestDetails.difficultyLevel}
      />

      {/* Test Sections */}
      <TestSections sections={mockTestDetails.sections} />

      {/* Download Form Section */}
      <MockTestDownload
        testId={mockTestDetails.formId}
        testTitle="Download NEET 2025 Mock Test 1"
        testDescription="Get access to the complete mock test paper and detailed answer key to improve your NEET preparation."
        downloadUrl={`${SITE_DOMAIN}/downloads/test-series/neet/2025/mock-test-1.pdf`}
        questionPaperUrl="https://drive.google.com/uc?export=download&id=1gm6Zj6AVxDukW_uc16ySIg8nl3_bOAIj"
        answerKeyUrl="https://drive.google.com/uc?export=download&id=1w4OHiCVA2u0l3pe5ozvLnU2YwbX4r4dB"
        defaultCourse="NEET for PU students"
      />

      {/* Instructions Section */}
      <TestInstructions />

      {/* Structured Data */}
      <Script
        id="test-series-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            generateCourseSchema({
              name: "NEET 2025 Mock Test 1",
              description:
                "Full-length mock test with 180 questions covering Physics, Chemistry, and Biology for NEET 2025 preparation.",
              courseCode: "NEET-MT1-2025",
              provider: SITE_CONFIG.name,
            }),
          ),
        }}
      />
    </article>
  );
}
