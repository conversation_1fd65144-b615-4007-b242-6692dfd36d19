# Aims Academy

## Description

Aims Academy is a web application built using Next.js, Tailwind CSS, Shadcn, and other technologies. It aims to provide a comprehensive learning platform for students and teachers.

## Technical Stack

- **Next.js**: A framework for server-rendered React applications.
- **Tailwind CSS**: A utility-first CSS framework for rapidly building custom designs.
- **Shadcn**: A design system and component library for React.
- **Git Workflows**: Version control and collaboration using Git.
- **Semantic Versioning**: A versioning scheme for software.
- **ESLint**: A pluggable linter for identifying and reporting on patterns in JavaScript.
- **TypeScript**: A statically typed superset of JavaScript that adds static type definitions.
- **Next-Themes**: A Next.js plugin for managing themes.
- **Geist**: A design system and component library for React.
- **Prettier**: An opinionated code formatter.
- **Lucide-React**: A React component library for Lucide Icons.

## Getting Started

1. Clone the repository: `git clone https://github.com/Aims-Academy/aims-academy`
2. Install dependencies: `npm ci`
3. Start the development server: `npm run dev`

## Contributing

Contributions are welcome! Please refer to the [contributing guidelines](CONTRIBUTING.md) for details.

## License

This project is licensed under the [MIT License](LICENSE).
