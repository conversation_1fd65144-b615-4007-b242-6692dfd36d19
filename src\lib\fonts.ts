import { <PERSON><PERSON><PERSON><PERSON>, Dancing_Script } from 'next/font/google';

// Configure Kodchasan font (main font for blog system)
export const kodchasan = Kodchasan({
  subsets: ['latin', 'thai'],
  weight: ['200', '300', '400', '500', '600', '700'],
  variable: '--font-kodchasan',
  display: 'swap',
  fallback: ['system-ui', 'arial'],
});

// Configure Dancing Script font (cursive variant)
export const dancingScript = Dancing_Script({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-dancing-script',
  display: 'swap',
  fallback: ['cursive'],
});

// Font class names for easy usage
export const fontClassNames = {
  kodchasan: kodchasan.className,
  dancingScript: dancingScript.className,
  kodchasanVariable: kodchasan.variable,
  dancingScriptVariable: dancingScript.variable,
};
