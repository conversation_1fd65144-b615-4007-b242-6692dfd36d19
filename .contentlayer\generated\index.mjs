// NOTE This file is auto-generated by Contentlayer

export { isType } from 'contentlayer2/client'

// NOTE During development Contentlayer imports from `.mjs` files to improve HMR speeds.
// During (production) builds Contentlayer it imports from `.json` files to improve build performance.
import allBlogPosts from './BlogPost/_index.json' with { type: 'json' }
import allBlogCategories from './BlogCategory/_index.json' with { type: 'json' }
import allBlogAuthors from './BlogAuthor/_index.json' with { type: 'json' }

export { allBlogPosts, allBlogCategories, allBlogAuthors }

export const allDocuments = [...allBlogPosts, ...allBlogCategories, ...allBlogAuthors]


