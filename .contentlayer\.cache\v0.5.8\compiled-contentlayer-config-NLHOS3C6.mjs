// contentlayer.config.ts
import { defineDocumentType, makeSource } from "contentlayer2/source-files";
import rehypeHighlight from "rehype-highlight";
import rehypeSlug from "rehype-slug";
import rehypeAutolinkHeadings from "rehype-autolink-headings";
import remarkGfm from "remark-gfm";
import readingTime from "reading-time";
var BlogPost = defineDocumentType(() => ({
  name: "BlogPost",
  filePathPattern: "blog/**/*.mdx",
  contentType: "mdx",
  fields: {
    title: {
      type: "string",
      required: true
    },
    description: {
      type: "string",
      required: true
    },
    publishedAt: {
      type: "date",
      required: true
    },
    updatedAt: {
      type: "date",
      required: false
    },
    featured: {
      type: "boolean",
      default: false
    },
    draft: {
      type: "boolean",
      default: false
    },
    category: {
      type: "string",
      required: true
    },
    tags: {
      type: "list",
      of: { type: "string" },
      required: true
    },
    author: {
      type: "string",
      required: true
    },
    image: {
      type: "nested",
      of: {
        src: { type: "string", required: true },
        alt: { type: "string", required: true },
        caption: { type: "string", required: false }
      },
      required: false
    },
    seo: {
      type: "nested",
      of: {
        title: { type: "string", required: false },
        description: { type: "string", required: false },
        keywords: { type: "list", of: { type: "string" }, required: false },
        canonicalUrl: { type: "string", required: false },
        noindex: { type: "boolean", required: false }
      },
      required: false
    },
    relatedPosts: {
      type: "list",
      of: { type: "string" },
      required: false
    }
  },
  computedFields: {
    slug: {
      type: "string",
      resolve: (doc) => {
        const pathSegments = doc._raw.flattenedPath.split("/");
        return pathSegments[pathSegments.length - 1];
      }
    },
    url: {
      type: "string",
      resolve: (doc) => {
        const pathSegments = doc._raw.flattenedPath.split("/");
        const category = pathSegments[1];
        const slug = pathSegments[pathSegments.length - 1];
        return `/blog/${category}/${slug}`;
      }
    },
    categorySlug: {
      type: "string",
      resolve: (doc) => {
        const pathSegments = doc._raw.flattenedPath.split("/");
        return pathSegments[1];
      }
    },
    excerpt: {
      type: "string",
      resolve: (doc) => {
        const content = doc.body.raw;
        const firstParagraph = content.split("\n\n")[0];
        if (firstParagraph && firstParagraph.length <= 160) {
          return firstParagraph.replace(/[#*`]/g, "").trim();
        }
        return content.slice(0, 160).replace(/[#*`]/g, "").trim() + "...";
      }
    },
    readingTime: {
      type: "number",
      resolve: (doc) => {
        const stats = readingTime(doc.body.raw);
        return Math.ceil(stats.minutes);
      }
    },
    wordCount: {
      type: "number",
      resolve: (doc) => {
        const stats = readingTime(doc.body.raw);
        return stats.words;
      }
    }
  }
}));
var BlogCategory = defineDocumentType(() => ({
  name: "BlogCategory",
  filePathPattern: "blog-categories/**/*.mdx",
  contentType: "mdx",
  fields: {
    name: {
      type: "string",
      required: true
    },
    description: {
      type: "string",
      required: true
    },
    color: {
      type: "string",
      required: true
    },
    icon: {
      type: "string",
      required: false
    }
  },
  computedFields: {
    slug: {
      type: "string",
      resolve: (doc) => {
        const pathSegments = doc._raw.flattenedPath.split("/");
        return pathSegments[pathSegments.length - 1];
      }
    }
  }
}));
var BlogAuthor = defineDocumentType(() => ({
  name: "BlogAuthor",
  filePathPattern: "blog-authors/**/*.mdx",
  contentType: "mdx",
  fields: {
    name: {
      type: "string",
      required: true
    },
    bio: {
      type: "string",
      required: true
    },
    avatar: {
      type: "string",
      required: false
    },
    role: {
      type: "string",
      required: true
    },
    social: {
      type: "nested",
      of: {
        twitter: { type: "string", required: false },
        linkedin: { type: "string", required: false },
        email: { type: "string", required: false }
      },
      required: false
    }
  },
  computedFields: {
    slug: {
      type: "string",
      resolve: (doc) => {
        const pathSegments = doc._raw.flattenedPath.split("/");
        return pathSegments[pathSegments.length - 1];
      }
    }
  }
}));
var contentlayer_config_default = makeSource({
  contentDirPath: "content",
  documentTypes: [BlogPost, BlogCategory, BlogAuthor],
  mdx: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [
      rehypeHighlight,
      rehypeSlug,
      [
        rehypeAutolinkHeadings,
        {
          behavior: "wrap",
          properties: {
            className: ["anchor-link"]
          }
        }
      ]
    ]
  }
});
export {
  BlogAuthor,
  BlogCategory,
  BlogPost,
  contentlayer_config_default as default
};
//# sourceMappingURL=compiled-contentlayer-config-NLHOS3C6.mjs.map
