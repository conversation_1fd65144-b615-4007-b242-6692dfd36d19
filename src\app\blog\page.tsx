import { type Metadata } from "next";
import { allBlogPosts, allBlogCategories } from ".contentlayer/generated";
import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";
import Script from "next/script";
import BlogHero from "~/components/blog/blog-hero";
import BlogSidebar from "~/components/blog/blog-sidebar";
import BlogCategories from "~/components/blog/blog-categories";
import { getBlogMetadata } from "~/lib/blog-utils";
import BlogGrid from "~/components/blog/blog-grid";

// ============================================================================
// Metadata Configuration
// ============================================================================

export const metadata: Metadata = {
  title: "NEET Blog | Study Tips, Exam Strategy & Success Stories",
  description:
    "Comprehensive NEET preparation blog with study tips, exam strategies, subject guides, and success stories. Expert insights from Aims Academy faculty for NEET 2025 preparation.",
  keywords: [
    "NEET Blog",
    "NEET Preparation Tips",
    "NEET Study Guide",
    "NEET Exam Strategy",
    "NEET Success Stories",
    "Medical Entrance Preparation",
    "NEET 2025",
    "Biology Study Tips",
    "Chemistry Study Guide",
    "Physics NEET Preparation",
    "Aims Academy Blog",
  ],
  alternates: {
    canonical: `${SITE_DOMAIN}/blog`,
  },
  openGraph: {
    title: "NEET Blog | Expert Study Tips & Exam Strategies",
    description:
      "Get expert NEET preparation tips, study strategies, and success stories from Aims Academy. Your complete guide to NEET 2025 success.",
    url: `${SITE_DOMAIN}/blog`,
    siteName: SITE_CONFIG.name,
    images: [
      {
        url: "/blog/blog-og-image.jpg",
        width: 1200,
        height: 630,
        alt: "NEET Blog - Study Tips and Exam Strategies",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NEET Blog | Study Tips & Success Stories",
    description:
      "Expert NEET preparation tips and strategies from Aims Academy. Your guide to NEET 2025 success.",
  },
};

// ============================================================================
// Blog Home Page Component
// ============================================================================

export default async function BlogHomePage() {
  // Get all published blog posts
  const publishedPosts = allBlogPosts
    .filter((post) => !post.draft)
    .sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
    );

  // Get blog categories
  const categories = allBlogCategories;

  // Get blog metadata
  const blogMetadata = getBlogMetadata(publishedPosts);

  // Get featured posts
  const featuredPosts = publishedPosts
    .filter((post) => post.featured)
    .slice(0, 3);

  // Get recent posts (excluding featured)
  const recentPosts = publishedPosts
    .filter((post) => !post.featured)
    .slice(0, 6);

  // Generate structured data for the blog
  const blogSchema = {
    "@context": "https://schema.org",
    "@type": "Blog",
    name: "NEET Blog - Aims Academy",
    description:
      "Comprehensive NEET preparation blog with study tips and exam strategies",
    url: `${SITE_DOMAIN}/blog`,
    publisher: {
      "@type": "EducationalOrganization",
      name: SITE_CONFIG.name,
      url: SITE_DOMAIN,
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": `${SITE_DOMAIN}/blog`,
    },
    blogPost: featuredPosts.map((post) => ({
      "@type": "BlogPosting",
      headline: post.title,
      description: post.description,
      url: `${SITE_DOMAIN}${post.url}`,
      datePublished: post.publishedAt,
      dateModified: post.updatedAt ?? post.publishedAt,
      author: {
        "@type": "Person",
        name: post.author,
      },
      publisher: {
        "@type": "EducationalOrganization",
        name: SITE_CONFIG.name,
      },
    })),
  };

  return (
    <>
      <article className="min-h-screen bg-background">
        {/* Blog Hero Section */}
        <BlogHero
          featuredPost={featuredPosts[0]}
          totalPosts={publishedPosts.length}
        />

        {/* Main Content Area */}
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Categories Navigation */}
              <BlogCategories categories={categories} className="mb-8" />

              {/* Featured Posts Section */}
              {featuredPosts.length > 1 && (
                <section className="mb-12">
                  <h2 className="mb-6 text-2xl font-bold text-primary">
                    Featured Articles
                  </h2>
                  <BlogGrid posts={featuredPosts.slice(1)} variant="featured" />
                </section>
              )}

              {/* Recent Posts Section */}
              <section>
                <h2 className="mb-6 text-2xl font-bold text-primary">
                  Latest Articles
                </h2>
                <BlogGrid posts={recentPosts} variant="default" />
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <BlogSidebar
                categories={categories}
                recentPosts={publishedPosts.slice(0, 5)}
                popularPosts={blogMetadata.popularPosts}
              />
            </div>
          </div>
        </div>
      </article>

      {/* Structured Data */}
      <Script
        id="blog-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(blogSchema),
        }}
      />
    </>
  );
}
