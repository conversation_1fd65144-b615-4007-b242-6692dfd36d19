import OurEducationalPhilosophy from "~/components/about-components/our-educational-philosophy/our-educational-philosophy";
import WelcomeMessageHome from "~/components/home-components/welcome-message-home/welcome-message-home";
import ContactUsForm from "~/components/contact-components/contact-us-form/contact-us-form";
import { Hero3 } from "~/components/home-components/hero/Hero3";
import { Hero4 } from "~/components/home-components/hero/Hero4";
import { Hero5 } from "~/components/home-components/hero/Hero5";
import { CoursesFeaturesMain } from "~/components/courses-components/courses-features-main/courses-features-main";
import Script from "next/script";
import { getGoogleReviews } from "~/lib/shared-data";
import { generateProductReviewSchema } from "~/lib/structured-data";
import { EXAMPLE_REVIEWS } from "~/constants/reviews-example";
import { Review } from "~/lib/featurable/types";

export default async function HomePage() {
  // Fetch Google reviews data for homepage structured data
  let reviewsData;
  try {
    reviewsData = await getGoogleReviews();
  } catch (error) {
    console.error(
      "Error fetching reviews on homepage, using fallback data:",
      error,
    );
    // Use fallback data to prevent the page from crashing
    reviewsData = EXAMPLE_REVIEWS;
  }

  return (
    <>
      <article>
        <WelcomeMessageHome />
        <Hero3 />
        <Hero4 />
        <Hero5 />
        <CoursesFeaturesMain />
        <OurEducationalPhilosophy />
        <ContactUsForm formId="home-page" />
      </article>

      {/* Product review schema for SEO - only on homepage */}
      <Script
        id="product-review-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            generateProductReviewSchema({
              ratingValue: reviewsData?.averageRating?.toString() ?? "4.8",
              ratingCount: reviewsData?.totalReviewCount?.toString() ?? "500",
              image: "/logo.png",
              description:
                "Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore",
              reviews: Array.isArray(reviewsData?.reviews)
                ? reviewsData.reviews.map((review: Review) => ({
                    author: review.reviewer.displayName,
                    datePublished: review.createTime ?? "",
                    reviewBody: review.comment ?? "",
                    ratingValue: review.starRating?.toString() ?? "",
                  }))
                : [],
            }),
          ),
        }}
      />
    </>
  );
}
