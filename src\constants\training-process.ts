import type { COURSES_PAGES } from "./pages";

type TrainingStep = {
  title: string;
  description: string;
  subDescription?: string;
  note?: string;
  gradient: string;
};

type TrainingProcessMap = {
  [key in keyof typeof COURSES_PAGES]: {
steps: TrainingStep[];
    description: string;
  };
};

export const TRAINING_PROCESS: Partial<TrainingProcessMap> = {
  PCMB: {
    description:
      "Our comprehensive PCMB preparation combines structured learning with personalized attention",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for each student",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Building",
        description: "Detailed lectures on all PCMB topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physics, Chemistry, Mathematics, Biology",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Practice & Revision",
        description: "Diverse study materials and resources",
        subDescription: "Application Mastery",
        note: "Effective concept application through practice",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "Career Guidance",
        description: "Counseling on career options after completing PCMB",
        subDescription: "Exploring Opportunities",
        note: "Guidance on choosing the right career path",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
  PCMC: {
    description:
      "Our comprehensive PCMC preparation focuses on building strong foundations in Physics, Chemistry, Mathematics, and Computer Science",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for each student",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Building",
        description: "Detailed lectures on all PCMC topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physics, Chemistry, Mathematics, and Computer Science",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Coding & Problem Solving",
        description:
          "Intensive practice with coding challenges and problem-solving strategies",
        subDescription: "Application Mastery",
        note: "Effective application of concepts through coding and problem-solving",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Project Development",
        description:
          "Guided projects to apply theoretical knowledge practically",
        subDescription: "Practical Experience",
        note: "Hands-on experience with real-world projects",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
  PU_COURSE: {
    description:
      "Our comprehensive PU course preparation combines structured learning with personalized attention",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for each student",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Building",
        description: "Detailed lectures on all PU course topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physical, Chemistry, Mathematics, Biological Sciences",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Practice & Revision",
        description: "Diverse study materials and resources",
        subDescription: "Application Mastery",
        note: "Effective concept application through practice",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "Career Guidance",
        description: "Counseling on career options after completing PU course",
        subDescription: "Exploring Opportunities",
        note: "Guidance on choosing the right career path",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
  NEET_FOR_PU_STUDENTS: {
    description:
      "Our comprehensive NEET preparation for PU students combines structured learning with problem-solving skills",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for NEET preparation",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Building",
        description: "Detailed lectures on all NEET topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physics, Chemistry, Biology",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Problem Solving",
        description: "Intensive practice with NEET-level problems",
        subDescription: "Exam Strategy Development",
        note: "Focus on time management and question selection",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "Medical College Counseling",
        description: "Guidance on medical college admissions and procedures",
        subDescription: "Admission Support",
        note: "Assistance with the medical college admission process",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
  JEE_FOR_PU_STUDENTS: {
    description:
      "Our comprehensive JEE preparation combines structured learning with problem-solving skills",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for JEE preparation",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Building",
        description: "Detailed lectures on all JEE topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physics, Chemistry, Mathematics",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Problem Solving",
        description: "Intensive practice with JEE-level problems",
        subDescription: "Exam Strategy Development",
        note: "Focus on time management and question selection",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "Engineering College Counseling",
        description:
          "Guidance on engineering college admissions and procedures",
        subDescription: "Admission Support",
        note: "Assistance with the engineering college admission process",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
  KCET_FOR_PU_STUDENTS: {
    description:
      "Our comprehensive KCET preparation combines structured learning with problem-solving skills",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for KCET preparation",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Building",
        description: "Detailed lectures on all KCET topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physics, Chemistry, Mathematics, Biology",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Problem Solving",
        description: "Intensive practice with KCET-level problems",
        subDescription: "Exam Strategy Development",
        note: "Focus on time management and question selection",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "State-Specific Counseling",
        description:
          "Guidance on state-specific college admissions and procedures",
        subDescription: "Admission Support",
        note: "Assistance with the state-specific college admission process",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
  NEET_FOR_REPEATERS: {
    description:
      "Our comprehensive NEET preparation for repeaters focuses on reinforcing concepts and improving problem-solving skills",
    steps: [
      {
        title: "Initial Assessment",
        description: "Comprehensive evaluation of strengths and weaknesses",
        subDescription: "Personalized Study Plans",
        note: "Customized learning paths for NEET repeaters",
        gradient: "from-blue-500 to-purple-600",
      },
      {
        title: "Concept Reinforcement",
        description: "Detailed review of all NEET topics",
        subDescription: "Deep Conceptual Understanding",
        note: "Strong foundations in Physics, Chemistry, Biology",
        gradient: "from-purple-500 to-pink-600",
      },
      {
        title: "Intensive Practice",
        description: "Intensive practice with NEET-level problems",
        subDescription: "Exam Strategy Development",
        note: "Focus on time management and question selection",
        gradient: "from-yellow-500 to-orange-600",
      },
      {
        title: "Mock Tests & Feedback",
        description:
          "Regular mock tests to assess progress and provide feedback",
        subDescription: "Performance Analysis",
        note: "Identifying areas for improvement and refining strategies",
        gradient: "from-green-500 to-teal-600",
      },
      {
        title: "Final Preparations",
        description: "Last-minute tips and strategies for exam success",
        subDescription: "Confidence Building",
        note: "Mental preparation and stress management techniques",
        gradient: "from-red-500 to-pink-600",
      },
      {
        title: "Repeater-Specific Strategies",
        description:
          "Tailored strategies for repeaters to overcome past challenges",
        subDescription: "Overcoming Weaknesses",
        note: "Addressing specific weaknesses and improving performance",
        gradient: "from-indigo-500 to-violet-600",
      },
    ],
  },
};

export const DEFAULT_TRAINING_PROCESS = {
  description:
    "Our comprehensive preparation combines structured learning with personalized attention",
  steps: [
    {
      title: "Initial Assessment",
      description: "Comprehensive evaluation of strengths and weaknesses",
      subDescription: "Personalized Study Plans",
      note: "Customized learning paths for each student",
      gradient: "from-blue-500 to-purple-600",
    },
    {
      title: "Concept Building",
      description: "Detailed lectures on all topics",
      subDescription: "Deep Conceptual Understanding",
      note: "Strong foundations in core subjects",
      gradient: "from-purple-500 to-pink-600",
    },
  ],
};
