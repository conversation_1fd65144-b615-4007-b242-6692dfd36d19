export interface PagesMetadata {
  url: string;
  title: string;
  description: string;
  keywords: string[];
}

export interface Pages {
  ABOUT: PagesMetadata;
  CONTACT: PagesMetadata;
  GALLERY: PagesMetadata;
  COURSES: PagesMetadata;
  TEST_SERIES: PagesMetadata;
}

export const PAGES: Pages = {
  COURSES: {
    url: "/courses",
    title: "Courses",
    description: "Explore our range of courses offered at Aims Academy",
    keywords: ["Courses", "Aims Academy"],
  },
  GALLERY: {
    url: "/gallery",
    title: "Gallery",
    description: "View images and videos from Aims Academy events",
    keywords: ["Gallery", "Aims Academy"],
  },
  ABOUT: {
    url: "/about",
    title: "About Us",
    description: "Learn more about Aims Academy, our mission, and values",
    keywords: ["About", "Aims Academy"],
  },
  CONTACT: {
    url: "/contact",
    title: "Contact Us",
    description: "Get in touch with Aims Academy for inquiries and support",
    keywords: ["Contact", "Aims Academy"],
  },
  TEST_SERIES: {
    url: "/test-series",
    title: "NEET Test Series 2025",
    description:
      "Comprehensive test series for NEET 2025 preparation with mock tests and detailed solutions",
    keywords: [
      "NEET Test Series",
      "NEET 2025",
      "NEET Mock Tests",
      "NEET Preparation",
      "Aims Academy",
    ],
  },
};

export interface CoursesPages {
  PCMB: PagesMetadata;
  PCMC: PagesMetadata;
  PU_COURSE: PagesMetadata;
  NEET_FOR_PU_STUDENTS: PagesMetadata;
  JEE_FOR_PU_STUDENTS: PagesMetadata;
  KCET_FOR_PU_STUDENTS: PagesMetadata;
  NEET_FOR_REPEATERS: PagesMetadata;
}

export const COURSES_PAGES: CoursesPages = {
  PCMB: {
    url: "/courses/pcmb",
    title: "PCMB",
    description: "Physics, Chemistry, Mathematics, and Biology",
    keywords: ["PCMB", "Aims Academy"],
  },
  PCMC: {
    url: "/courses/pcmc",
    title: "PCMC",
    description: "Physics, Chemistry, Mathematics, and Computer Science",
    keywords: ["PCMC", "Aims Academy"],
  },
  PU_COURSE: {
    url: "/courses/pu-course",
    title: "PU Course",
    description: "Physical, chemistry, Mathematics, Biological Sciences",
    keywords: ["PU Courses", "Aims Academy"],
  },
  NEET_FOR_PU_STUDENTS: {
    url: "/courses/neet-for-pu-students",
    title: "NEET for PU Students",
    description: "NEET coaching for PU students",
    keywords: ["NEET", "PU Students", "Aims Academy"],
  },
  JEE_FOR_PU_STUDENTS: {
    url: "/courses/jee-for-pu-students",
    title: "JEE for PU Students",
    description: "JEE coaching for PU students",
    keywords: ["JEE", "PU Students", "Aims Academy"],
  },
  KCET_FOR_PU_STUDENTS: {
    url: "/courses/kcet-for-pu-students",
    title: "KCET for PU Students",
    description: "KCET coaching for PU students",
    keywords: ["KCET", "PU Students", "Aims Academy"],
  },
  NEET_FOR_REPEATERS: {
    url: "/courses/long-term-for-neet-repeaters",
    title: "Long Term for NEET Repeaters",
    description: "NEET coaching for repeaters",
    keywords: ["NEET", "Repeaters", "Aims Academy"],
  },
};
