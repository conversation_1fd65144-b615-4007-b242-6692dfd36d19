import Link from "next/link";
import Image from "next/image";
import { type BlogPost as ContentlayerBlogPost } from ".contentlayer/generated";
import { formatPublishDate, formatReadingTime } from "~/lib/blog-utils";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent } from "~/components/ui/card";
import { Clock, Calendar, User, ArrowRight } from "lucide-react";
import { cn } from "~/lib/utils";

interface BlogGridProps {
  posts: ContentlayerBlogPost[];
  variant?: "default" | "featured" | "compact";
  className?: string;
}

export default function BlogGrid({
  posts,
  variant = "default",
  className,
}: BlogGridProps) {
  if (posts.length === 0) {
    return (
      <div className="py-12 text-center">
        <p className="text-muted-foreground">No articles found.</p>
      </div>
    );
  }

  const gridClasses = {
    default: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
    featured: "grid grid-cols-1 md:grid-cols-2 gap-8",
    compact: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",
  };

  return (
    <div className={cn(gridClasses[variant], className)}>
      {posts.map((post) => (
        <BlogCard key={post.slug} post={post} variant={variant} />
      ))}
    </div>
  );
}

interface BlogCardProps {
  post: ContentlayerBlogPost;
  variant: "default" | "featured" | "compact";
}

function BlogCard({ post, variant }: BlogCardProps) {
  const isCompact = variant === "compact";
  const isFeatured = variant === "featured";

  return (
    <Card className="group overflow-hidden border-border/50 transition-all duration-300 hover:border-primary/20 hover:shadow-lg">
      <div className="relative">
        {/* Post Image */}
        {post.image && (
          <div
            className={cn(
              "relative overflow-hidden",
              isCompact ? "h-32" : isFeatured ? "h-56" : "h-48",
            )}
          >
            <Image
              src={post.image.src}
              alt={post.image.alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes={
                isCompact
                  ? "(max-width: 768px) 100vw, 25vw"
                  : "(max-width: 768px) 100vw, 33vw"
              }
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

            {/* Category Badge */}
            <Badge
              variant="secondary"
              className="absolute left-3 top-3 bg-background/90 text-xs text-foreground"
            >
              {post.category.replace("-", " ").toUpperCase()}
            </Badge>

            {/* Featured Badge */}
            {post.featured && (
              <Badge
                variant="default"
                className="absolute right-3 top-3 bg-primary text-xs text-primary-foreground"
              >
                Featured
              </Badge>
            )}
          </div>
        )}

        {/* Fallback for posts without images */}
        {!post.image && (
          <div
            className={cn(
              "relative flex items-center justify-center bg-gradient-to-br from-primary/10 to-accent/10",
              isCompact ? "h-32" : isFeatured ? "h-56" : "h-48",
            )}
          >
            <div className="space-y-2 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/20">
                <span className="text-lg font-bold text-primary">
                  {post.title.charAt(0)}
                </span>
              </div>
              <Badge variant="outline" className="text-xs">
                {post.category.replace("-", " ").toUpperCase()}
              </Badge>
            </div>
          </div>
        )}
      </div>

      <CardContent className={cn("p-4", isFeatured && "p-6")}>
        <div className="space-y-3">
          {/* Title */}
          <h3
            className={cn(
              "font-bold leading-tight transition-colors group-hover:text-primary",
              isCompact
                ? "line-clamp-2 text-sm"
                : isFeatured
                  ? "line-clamp-2 text-xl"
                  : "line-clamp-2 text-lg",
            )}
          >
            <Link href={post.url}>{post.title}</Link>
          </h3>

          {/* Excerpt */}
          {!isCompact && (
            <p
              className={cn(
                "text-muted-foreground",
                isFeatured ? "line-clamp-3 text-base" : "line-clamp-2 text-sm",
              )}
            >
              {post.excerpt}
            </p>
          )}

          {/* Tags */}
          {!isCompact && post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {post.tags.slice(0, 3).map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="px-2 py-0.5 text-xs"
                >
                  {tag}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="px-2 py-0.5 text-xs">
                  +{post.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Meta Information */}
          <div
            className={cn(
              "flex items-center gap-3 text-muted-foreground",
              isCompact ? "text-xs" : "text-sm",
            )}
          >
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span className="truncate">{post.author}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatPublishDate(post.publishedAt)}</span>
            </div>
            {!isCompact && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{formatReadingTime(post.readingTime)}</span>
              </div>
            )}
          </div>

          {/* Read More Link */}
          {!isCompact && (
            <div className="pt-2">
              <Link
                href={post.url}
                className="inline-flex items-center gap-1 text-sm font-medium text-primary transition-all hover:underline group-hover:gap-2"
              >
                Read More
                <ArrowRight className="h-3 w-3" />
              </Link>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
