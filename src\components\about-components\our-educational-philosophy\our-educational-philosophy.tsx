import { BentoGrid, BentoGridItem } from "~/components/ui/bento-grid";
import { BookOpen, Lightbulb, GraduationCap, Users } from "lucide-react";

const points = [
  {
    icon: <BookOpen size={40} className="text-blue-500" />,
    title: "Innovative Teaching Methods",
    description:
      "We transformed 'Teach to Influence' into 'Teach to Inspire' and 'Reinforce' to 'Integrate' strategies in our curriculum.",
  },
  {
    icon: <Lightbulb size={40} className="text-yellow-500" />,
    title: "Scientific & Scholastic Learning",
    description:
      "Our vision is to enlighten young minds by addressing learning difficulties with scientifically proven methodologies.",
  },
  {
    icon: <GraduationCap size={40} className="text-green-500" />,
    title: "Experience-Driven Curriculum",
    description:
      "Our vast experience with diverse educational organizations has enabled us to design a flawless, all-round curriculum.",
  },
  {
    icon: <Users size={40} className="text-purple-500" />,
    title: "Future-Ready Education",
    description:
      "We are committed to fulfilling students' scholastic and social needs, preparing them for future challenges.",
  },
];

const OurEducationalPhilosophy = () => {
  return (
    <section className="container mx-auto px-4 py-16">
      <h2 className="mb-12 text-center text-4xl font-bold tracking-tight md:text-5xl">
        Our Educational Philosophy & Commitment
      </h2>
      <BentoGrid className="md:grid-cols-2 lg:grid-cols-4">
        {points.map((point) => (
          <BentoGridItem
            key={point.title}
            title={point.title}
            description={point.description}
            header={
              <div className="flex h-24 items-center justify-center">
                <div className="rounded-full bg-primary/10 p-3">
                  {point.icon}
                </div>
              </div>
            }
            className="border border-gray-200 bg-white p-6 transition-all hover:shadow-lg"
          />
        ))}
      </BentoGrid>
    </section>
  );
};

export default OurEducationalPhilosophy;
