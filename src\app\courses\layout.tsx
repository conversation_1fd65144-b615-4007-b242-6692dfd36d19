import { type Metadata } from "next";
import ContactUsForm from "~/components/contact-components/contact-us-form/contact-us-form";
import DynamicComponents from "./_components/dynamic-components";
import { CoursesFeaturesMain } from "~/components/courses-components/courses-features-main/courses-features-main";

export const metadata: Metadata = {
  title: "Aims Academy | Top NEET Long Term Coaching & PU Integrated Courses",
  description:
    "Enroll at Aims Academy, the Top NEET Long Term Coaching center in Bangalore. Expert faculty, personalized mentoring, and PU integrated courses for comprehensive preparation.",
  keywords: [
    "Top NEET Coaching Bangalore",
    "NEET Long-Term Coaching Center",
    "PU Integrated Courses Bangalore",
    "Best NEET Repeaters Program",
    "NEET Preparation Bangalore",
    "PU Courses Yelahanka",
    "Integrated PU Coaching Bangalore",
  ],
  openGraph: {
    title: "Aims Academy | Top NEET Long Term Coaching & PU Integrated Courses",
    description:
      "Achieve top ranks with Aims Academy's NEET long-term coaching and PU integrated courses. Comprehensive syllabus coverage, expert guidance, and proven results.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy NEET and PU Integrated Coaching",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Aims Academy | Top NEET Long Term Coaching & PU Integrated Courses",
    description:
      "Join Aims Academy for Top NEET Long Term Coaching and PU integrated courses in Bangalore. Expert faculty and personalized mentoring for success!",
  },
};

export default function CoursesLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <article className="p-2">
      {children}
      <CoursesFeaturesMain />
      <DynamicComponents />
      <ContactUsForm formId="courses-footer" />
    </article>
  );
}
