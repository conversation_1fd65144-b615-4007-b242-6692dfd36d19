@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  :root {
    --background: 32 0% 100%;
    --foreground: 32 0% 10%;
    --card: 32 0% 100%;
    --card-foreground: 32 0% 15%;
    --popover: 32 0% 100%;
    --popover-foreground: 32 95% 10%;
    --primary: 32 94% 54.1%;
    --primary-foreground: 0 0% 100%;
    --secondary: 32 10% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 354 10% 95%;
    --muted-foreground: 32 0% 40%;
    --accent: 354 10% 90%;
    --accent-foreground: 32 0% 15%;
    --destructive: 0 50% 50%;
    --destructive-foreground: 32 0% 100%;
    --border: 32 20% 82%;
    --input: 32 20% 50%;
    --ring: 32 94% 54.1%;
    --radius: 1rem;
  }
  .dark {
    --background: 32 10% 10%;
    --foreground: 32 0% 100%;
    --card: 32 0% 10%;
    --card-foreground: 32 0% 100%;
    --popover: 32 10% 5%;
    --popover-foreground: 32 0% 100%;
    --primary: 32 94% 54.1%;
    --primary-foreground: 0 0% 100%;
    --secondary: 32 10% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 354 10% 25%;
    --muted-foreground: 32 0% 65%;
    --accent: 354 10% 25%;
    --accent-foreground: 32 0% 95%;
    --destructive: 0 50% 50%;
    --destructive-foreground: 32 0% 100%;
    --border: 32 20% 50%;
    --input: 32 20% 50%;
    --ring: 32 94% 54.1%;
    --radius: 1rem;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 6s ease infinite; /* Slower speed with 6s duration */
}
