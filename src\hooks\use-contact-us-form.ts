"use client";

import { useState, useCallback } from "react";
import { useToast } from "~/hooks/use-toast";
import { sendGAEvent } from "@next/third-parties/google";
import { courseOptions } from "~/constants/courses";

interface FormData {
  name: string;
  phone: string;
  email: string;
  course: string;
  message?: string;
  formSource?: string;
}

interface ApiResponse {
  status: "success" | "error";
  message: string;
  error_details?: string;
}

const defaultFormData: FormData = {
  name: "",
  phone: "",
  email: "",
  course: courseOptions[0] ?? "",
  message: "",
};

export function useContactUsForm(
  initialFormData?: Partial<FormData>,
  formSource = "default",
) {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    ...defaultFormData,
    ...initialFormData,
    formSource,
  });
  const [loading, setLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");

  const handleFormChange = useCallback(
    (field: keyof FormData, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    [],
  );

  const validateForm = useCallback(() => {
    if (!formData.name || !formData.phone) {
      toast({
        title: "Error",
        description: "Please fill all required fields.",
        variant: "destructive",
      });

      // Send GA event for validation error
      sendGAEvent("event", "form_error", {
        form_name: "contact_us",
        error_type: "validation_error",
      });

      return false;
    }
    return true;
  }, [formData, toast]);

  const submitForm = async (data: FormData): Promise<ApiResponse> => {
    const response = await fetch(
      "https://email-worker.aims-academy2025.workers.dev/send",
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: data.name,
          phone: data.phone,
          email: data.email,
          course: data.course,
          message: data.message,
          formSource: data.formSource,
        }),
      },
    );
    const result: ApiResponse = (await response.json()) as ApiResponse;
    return result;
  };

  const handleSubmit = async (
    event: React.FormEvent,
  ): Promise<ApiResponse | undefined> => {
    event.preventDefault();

    if (!validateForm()) {
      return undefined;
    }

    setLoading(true);
    setSubmitStatus("idle");

    // Send GA event for form submission
    sendGAEvent("event", "form_submit", {
      form_name: "contact_us",
      form_status: "submitted",
    });

    try {
      const response = await submitForm(formData);
      if (response.status === "success") {
        handleResponse(response);
        setSubmitStatus("success");
        return response;
      } else {
        handleError(response);
        setSubmitStatus("error");
        return response;
      }
    } catch (error) {
      const errorResponse: ApiResponse = {
        status: "error",
        message: "Failed to submit the form. Please try again.",
        error_details: error instanceof Error ? error.message : "Unknown error",
      };
      handleError(errorResponse);
      setSubmitStatus("error");
      return errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const handleResponse = useCallback(
    (response: ApiResponse) => {
      toast({
        title: "Submitted",
        description: response.message,
        variant: "success",
      });

      // Send GA event for form success
      sendGAEvent("event", "form_success", {
        form_name: "contact_us",
        form_status: "success",
        message: response.message,
      });

      // Reset form data but preserve the course selection
      setFormData((prev) => ({
        ...defaultFormData,
        course: prev.course, // Preserve the course selection
      }));
    },
    [toast],
  );

  const handleError = useCallback(
    (response: ApiResponse) => {
      toast({
        title: "Error",
        description: response.message,
        variant: "destructive",
      });

      // Send GA event for form error
      sendGAEvent("event", "form_error", {
        form_name: "contact_us",
        error_type: "submission_error",
        message: response.message,
      });
    },
    [toast],
  );

  return {
    formData,
    loading,
    submitStatus,
    handleFormChange,
    handleSubmit,
    setLoading,
  };
}
