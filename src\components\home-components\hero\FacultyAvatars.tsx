import Image from "next/image";
import {
  Toolt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "~/components/ui/tooltip";

const facultyMembers = [
  { name: "<PERSON>", image: "/faculty/aims_academy_Krishna_Reddy.jpg" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", image: "/faculty/aims_academy_Venkatesh.jpg" },
  {
    name: "<PERSON><PERSON><PERSON>",
    image: "/faculty/aims_academy_Kranthi_Kiran_Kumar.jpg",
  },
  { name: "Nagaraju", image: "/faculty/aims_academy_Nagaraju.jpg" },
  { name: "<PERSON><PERSON>", image: "/faculty/aims_academy_Ramana.jpg" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", image: "/faculty/aims_academy_Srinivasa_Rao.jpg" },
  { name: "<PERSON>ppa<PERSON>aj<PERSON>", image: "/faculty/aims_academy_Appalaraju.jpg" },
];

export const FacultyAvatars = () => {
  return (
    <div className="flex h-full w-full cursor-pointer items-center justify-center">
      <div className="flex -space-x-2">
        <TooltipProvider>
          {facultyMembers.map((member, i) => (
            <Tooltip key={i}>
              <TooltipTrigger asChild>
                <div>
                  <Image
                    className="border-1 rounded-full border-white object-cover"
                    src={member.image}
                    alt={`${member.name} Aims Academy`}
                    width={96}
                    height={96}
                    loading="lazy"
                    priority={false}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="font-medium">{member.name}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </TooltipProvider>
      </div>
    </div>
  );
};
