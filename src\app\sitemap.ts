import { type <PERSON>adataRoute } from "next";
import { PAGES, COURSES_PAGES, type PagesMetadata } from "~/constants/pages";
import { SITE_DOMAIN } from "~/constants/siteConfig";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const allPages: PagesMetadata[] = [
    ...(Object.values(PAGES) as PagesMetadata[]),
    ...(Object.values(COURSES_PAGES) as PagesMetadata[]),
  ];

  const defaultPages: MetadataRoute.Sitemap = allPages.map((page) => ({
    url: `${SITE_DOMAIN}${page.url}`,
    lastModified: new Date(),
    changeFrequency: "weekly" as const,
    priority: 0.9,
  }));

  // Add test series pages
  const testSeriesPages: MetadataRoute.Sitemap = [
    {
      url: `${SITE_DOMAIN}/test-series`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.9,
    },
    {
      url: `${SITE_DOMAIN}/test-series/neet`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.8,
    },
    {
      url: `${SITE_DOMAIN}/test-series/neet/2025`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.8,
    },
    {
      url: `${SITE_DOMAIN}/test-series/neet/2026`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.8,
    },
    {
      url: `${SITE_DOMAIN}/test-series/neet/2025/mock-test-1`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.7,
    },
    {
      url: `${SITE_DOMAIN}/test-series/neet/2025/mock-test-2`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.7,
    },
  ];

  const sitemap: MetadataRoute.Sitemap = [
    {
      url: SITE_DOMAIN,
      lastModified: new Date(),
      changeFrequency: "daily" as const,
      priority: 1,
    },
    ...defaultPages,
    ...testSeriesPages,
  ];

  return sitemap;
}
