name = "email-worker"
main = "src/cloudflare/workers/email-worker.ts"
compatibility_date = "2024-10-22"
compatibility_flags = ["nodejs_compat" ]
workers_dev = true


# The name configuration here corresponds to c.env.SEB.send in the code, 
# which needs to be synchronized.

send_email = [
    { type = "send_email", name = "SEB", destination_address = "<EMAIL>" },
]

[vars]
# Allow all origins, or specify a comma-separated list of allowed origins
ALLOWED_ORIGINS = "*" 
# Replace with the recipient's email address
RECIPIENT_ADDRESS = "<EMAIL>"
# Replace with the sender's email address
SENDER_ADDRESS = "<EMAIL>"
# Replace with the sender's name
SENDER_NAME = "Form Submission" 
