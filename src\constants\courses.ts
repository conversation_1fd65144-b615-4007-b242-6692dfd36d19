import type { ProgramFeatureCardsSectionProps } from "~/components/courses-components/feature-cards-section";
import { type FAQItemProps } from "~/components/kokonutui/faq-02";

export const courseOptions: string[] = [
  "PU COURSE",
  "PCMB",
  "PCMC",
  "NEET for PU students",
  "JEE for PU students",
  "KCET for PU students",
  "Long Term for NEET Repeaters",
];

export const puCourseProgramFeatureList: ProgramFeatureCardsSectionProps["features"] = [
  {
    title: "Comprehensive Curriculum",
    details: [
      "Extensive coverage of theoretical and practical knowledge",
      "Emphasis on developing foundational skills in chosen subjects",
      "Regular assessments to track progress and identify areas for improvement",
    ],
  },
  {
    title: "Stream Options",
    details: [
      "Science Stream: PCMB (Physics, Chemistry, Mathematics, Biology) or PCMC (Physics, Chemistry, Mathematics, Computer Science)",
      "Commerce Stream: Accountancy, Business Studies, Economics, and Mathematics/Statistics",
      "Arts Stream: Various humanities subjects",
    ],
  },
  {
    title: "Practical Learning",
    details: [
      "Laboratory experiments for science students",
      "Practical exercises in business and finance for commerce students",
      "Engagement in extracurricular activities to enhance overall development",
    ],
  },
];
export const pcmbProgramFeatureList: ProgramFeatureCardsSectionProps["features"] = [
  {
    title: "Science Foundation",
    details: [
      "In-depth study of Physics, Chemistry, Mathematics, and Biology",
      "Focus on developing problem-solving skills through practical experiments",
      "Preparation for competitive exams like NEET, JEE, etc.",
    ],
  },
  {
    title: "Career Opportunities",
    details: [
      "Gateway to medical and engineering fields",
      "Opportunities in research and scientific careers",
      "Strong foundation for higher education in sciences",
    ],
  },
  {
    title: "Expert Faculty Guidance",
    details: [
      "Personalized mentoring by experienced science faculty",
      "Regular check-ins to monitor progress and adjust study plans",
      "Tips and strategies for effective time management during exams",
    ],
  },
];
export const pcmcProgramFeatureList: ProgramFeatureCardsSectionProps["features"] = [
  {
    title: "Science and Technology Integration",
    details: [
      "In-depth study of Physics, Chemistry, Mathematics, and Computer Science",
      "Focus on developing programming skills and understanding of algorithms",
      "Preparation for careers in IT and technology",
    ],
  },
  {
    title: "Career Opportunities",
    details: [
      "Gateway to careers in software development, data science, and IT",
      "Opportunities in research and technological innovation",
      "Strong foundation for higher education in computer science",
    ],
  },
  {
    title: "Expert Guidance",
    details: [
      "Personalized mentoring by experienced computer science faculty",
      "Regular check-ins to monitor progress and adjust study plans",
      "Strategies for managing exam stress and maintaining focus",
    ],
  },
];
export const jeeForPuStudentsProgramFeatureList: ProgramFeatureCardsSectionProps["features"] = [
  {
    title: "Comprehensive JEE Preparation",
    details: [
      "In-depth study of Physics, Chemistry, and Mathematics with a focus on JEE syllabus",
      "Practice with previous years' question papers and mock tests",
      "Strategies for managing time effectively during the exam",
    ],
  },
  {
    title: "Expert Faculty Support",
    details: [
      "Personalized mentoring by experienced JEE instructors",
      "Regular check-ins to monitor progress and adjust study plans",
      "Tips for improving problem-solving skills and accuracy",
    ],
  },
  {
    title: "Practice and Assessment",
    details: [
      "Regular mock tests to simulate the actual JEE exam experience",
      "Detailed analysis of performance to identify areas for improvement",
      "Access to online resources and study materials",
    ],
  },
];
export const kcetForPuStudentsProgramFeatureList: ProgramFeatureCardsSectionProps["features"] = [
  {
    title: "Comprehensive KCET Preparation",
    details: [
      "In-depth study of Physics, Chemistry, Mathematics, and Biology with a focus on KCET syllabus",
      "Practice with previous years' question papers and mock tests",
      "Strategies for managing time effectively during the exam",
    ],
  },
  {
    title: "Expert Guidance",
    details: [
      "Personalized mentoring by experienced KCET instructors",
      "Regular check-ins to monitor progress and adjust study plans",
      "Tips for improving problem-solving skills and accuracy",
    ],
  },
  {
    title: "Practice and Assessment",
    details: [
      "Regular mock tests to simulate the actual KCET exam experience",
      "Detailed analysis of performance to identify areas for improvement",
      "Access to online resources and study materials",
    ],
  },
];


export const neetProgramFeatureList: ProgramFeatureCardsSectionProps["features"] = [
  {
    title: "Dedicated Guidance",
    details: [
      "Personal Mentors: Each student is assigned a personal mentor who provides one-on-one support",
      "Subject Experts: Our faculty includes experienced subject experts in Physics, Chemistry, and Biology",
      "Regular Check-ins: Regular meetings help students stay on track and adjust study plans",
    ],
  },
  {
    title: "Study Materials",
    details: [
      "Comprehensive Resources: We provide textbooks, practice worksheets, and digital resources",
      "Updated Syllabus Coverage: Materials are regularly updated for the latest NEET syllabus",
      "Access to Online Library: Includes video lectures and interactive quizzes",
    ],
  },
  {
    title: "Regular Assessments",
    details: [
      "Weekly Tests: Help reinforce understanding of key concepts",
      "Full-Length Mock Exams: Simulate the actual NEET exam experience",
      "Detailed Analysis: Performance analysis highlights strengths and weaknesses",
    ],
  },
];

export type FaqItem = {
  question: string;
  answer: string;
  index?: number;
};

/**
 * Central FAQ configuration for all courses
 * - Key format: Use course slugs matching URL paths
 * - 'root' contains FAQs for /courses page
 */
export const COURSE_FAQS: Record<string, Omit<FAQItemProps, "index">[]> = {
  // Root /courses page FAQs
  root: [
    {
      question: "What are the key courses offered at Aims Academy?",
      answer:
        "Aims Academy offers PU Courses, PCMB, PCMC, NEET, JEE, and KCET coaching programs designed for academic excellence and competitive exam success.",
    },
    {
      question:
        "How do I choose the best course to achieve my career aspirations?",
      answer:
        "Our experienced academic counselors provide personalized guidance to help you select the most suitable course based on your career goals, academic strengths, and the requirements of various competitive exams.",
    },
    {
      question:
        "What advantages does Aims Academy offer over other coaching centers?",
      answer:
        "Aims Academy stands out with personalized mentoring, highly experienced faculty, comprehensive study materials, and an integrated approach to competitive exam preparation.",
    },
    {
      question: "How long are the courses at Aims Academy?",
      answer:
        "Most of our comprehensive programs are structured to be completed within a timeframe of one to two years, ensuring thorough preparation and coverage of all essential topics.",
    },
    {
      question: "What is the enrollment process for Aims Academy courses?",
      answer:
        "You can easily enroll online through our website or by visiting our campus. Our dedicated admissions team is available to guide you through every step of the process.",
    },
    {
      question:
        "Does Aims Academy provide scholarships for deserving students?",
      answer:
        "Yes, Aims Academy offers scholarships based on both merit and financial need. Please contact our admissions office for detailed information on eligibility criteria and application procedures.",
    },
  ],

  // PCMB Course FAQs
  pcmb: [
    {
      question:
        "Is the PCMB course an effective path for NEET exam preparation?",
      answer:
        "Yes, our PCMB program includes specialized NEET coaching, focusing on biology and featuring a comprehensive mock test series designed to optimize your performance.",
    },
    {
      question:
        "Can PCMB students pursue engineering studies after completing the course?",
      answer:
        "Absolutely! PCMB students are eligible to appear for JEE Main/Advanced, with additional support classes available to strengthen their mathematical skills.",
    },
    {
      question:
        "What specific subjects are covered in the Aims Academy PCMB course?",
      answer:
        "The PCMB course at Aims Academy covers Physics, Chemistry, Mathematics, and Biology, with a strategic emphasis on both board exams and competitive entrance exams like NEET.",
    },
    {
      question:
        "How does the PCMB course at Aims Academy ensure thorough preparation for medical entrance exams?",
      answer:
        "Our PCMB program provides comprehensive biology coaching, regular mock tests simulating the actual exam environment, and personalized doubt-clearing sessions tailored to enhance students' readiness for NEET.",
    },
    {
      question:
        "What additional learning resources are available to PCMB students?",
      answer:
        "PCMB students gain access to a wealth of online study materials, engaging video lectures, and regular workshops designed to enrich their learning experience and deepen their understanding of key concepts.",
    },
  ],

  // PCMC Course FAQs
  pcmc: [
    {
      question:
        "What are the primary programming languages taught in the PCMC course?",
      answer:
        "The PCMC course covers essential programming languages like Python, C++, and Java, emphasizing competitive programming techniques and practical application in real-world scenarios.",
    },
    {
      question:
        "Is the PCMC course beneficial for a future career in data science?",
      answer:
        "Yes, our PCMC program lays a strong foundation in statistics and data analysis, making it highly beneficial for students aspiring to become data scientists.",
    },
    {
      question:
        "How does the PCMC course prepare students for engineering entrance exams such as JEE?",
      answer:
        "Our PCMC program offers intensive coaching for JEE Main/Advanced, focusing on mathematics and computer science to help students excel in these competitive exams.",
    },
    {
      question:
        "Does Aims Academy offer opportunities for PCMC students to participate in projects or internships?",
      answer:
        "Yes, PCMC students can participate in project-based learning and internships, providing them with valuable hands-on experience in programming and software development.",
    },
    {
      question:
        "What career paths can students pursue after completing the PCMC course?",
      answer:
        "Graduates of the PCMC course can pursue diverse career opportunities in software development, data science, artificial intelligence, and other technology-driven fields.",
    },
  ],

  "pu-course": [
    {
      question: "What are PU Courses, and how do they benefit students?",
      answer:
        "PU Courses at Aims Academy are designed to prepare students for the Pre-University (PU) exams while laying a strong foundation for future competitive exams like NEET, JEE, and KCET. These courses emphasize comprehensive learning and academic excellence.",
    },
    {
      question: "What subjects are covered in the PU Courses?",
      answer:
        "Our PU Courses cover a range of subjects including Physics, Chemistry, Mathematics, Biology, and other core subjects, depending on the stream chosen (PCMB or PCMC).",
    },
    {
      question:
        "How does Aims Academy's PU Course prepare students for competitive exams?",
      answer:
        "Our PU Courses integrate preparation for competitive exams like NEET, JEE, and KCET through specialized coaching, regular mock tests, and personalized study plans tailored to each student's needs.",
    },
    {
      question:
        "What are the benefits of enrolling in PU Courses at Aims Academy?",
      answer:
        "Enrolling in PU Courses at Aims Academy offers several benefits, including experienced faculty, personalized mentoring, comprehensive study materials, and a supportive learning environment designed to foster academic success.",
    },
    {
      question: "How can I enroll in PU Courses at Aims Academy?",
      answer:
        "Enrollment in PU Courses can be done online through our website or by visiting our campus. Our admissions team is available to guide you through the process and answer any questions you may have.",
    },
    {
      question:
        "Are there any scholarships available for students enrolling in PU Courses?",
      answer:
        "Yes, Aims Academy offers scholarships based on merit and need. Please contact our admissions office for detailed information on eligibility criteria and application procedures.",
    },
  ],

  // NEET for PU students FAQs
  "neet-for-pu-students": [
    {
      question:
        "How does Aims Academy specifically support PU students preparing for NEET?",
      answer:
        "Aims Academy offers specialized NEET coaching tailored for PU students, focusing on biology, with regular mock tests and personalized study plans designed to maximize their potential.",
    },
    {
      question: "Which subjects are essential for NEET preparation?",
      answer:
        "NEET preparation at Aims Academy covers Physics, Chemistry, and Biology comprehensively, emphasizing both theoretical understanding and practical application.",
    },
    {
      question:
        "What resources are provided to students preparing for NEET at Aims Academy?",
      answer:
        "Students preparing for NEET have access to comprehensive online study materials, video lectures, and regular workshops designed to enhance their learning experience and boost their exam readiness.",
    },
  ],

  // JEE for PU students FAQs
  "jee-for-pu-students": [
    {
      question:
        "How does Aims Academy assist PU students in preparing for the JEE exam?",
      answer:
        "Aims Academy provides intensive coaching for PU students focusing on mathematics and physics, along with regular mock tests and customized study plans tailored to individual needs.",
    },
    {
      question: "What are the key subjects covered during JEE preparation?",
      answer:
        "JEE preparation at Aims Academy encompasses Physics, Chemistry, and Mathematics, with a strong emphasis on both theoretical knowledge and practical problem-solving skills.",
    },
    {
      question: "What additional resources are available to JEE students?",
      answer:
        "JEE students have access to a wide range of online study materials, video lectures, and regular workshops, all aimed at enhancing their learning and improving their exam performance.",
    },
  ],

  // KCET for PU students FAQs
  "kcet-for-pu-students": [
    {
      question: "How does Aims Academy support PU students preparing for KCET?",
      answer:
        "Aims Academy provides specialized coaching tailored for PU students preparing for KCET, with a focus on mathematics and science, complemented by regular mock tests and personalized study plans.",
    },
    {
      question: "Which subjects are crucial for effective KCET preparation?",
      answer:
        "KCET preparation at Aims Academy includes comprehensive coverage of Physics, Chemistry, and Mathematics, emphasizing both theoretical understanding and practical application.",
    },
    {
      question: "What resources can KCET students access at Aims Academy?",
      answer:
        "KCET students at Aims Academy benefit from a variety of online study materials, video lectures, and regular workshops, all designed to enhance their learning and improve their readiness for the exam.",
    },
  ],

  // Long Term for NEET Repeaters FAQs
  "long-term-for-neet-repeaters": [
    {
      question:
        "What specific support does Aims Academy offer to NEET repeaters?",
      answer:
        "Aims Academy provides NEET repeaters with daily doubt-clearing sessions, personalized study plans, and enhanced test series analysis to facilitate significant improvement in their scores.",
    },
    {
      question: "How long is the long-term program for NEET repeaters?",
      answer:
        "The long-term program for NEET repeaters is an intensive 12-month course that includes 2 revision cycles and 15 full-length mock tests.",
    },
    {
      question:
        "How does Aims Academy's repeater program improve students' NEET scores?",
      answer:
        "Our repeater program focuses on pinpointing and strengthening weak areas, offering extensive practice through mock tests, and providing individualized mentoring to maximize students' potential.",
    },
    {
      question:
        "What additional resources or support are available for students in the repeater program?",
      answer:
        "Students in the repeater program have access to a wealth of online study materials, video lectures, and regular workshops to enrich their learning experience and exam preparation.",
    },
    {
      question:
        "Is it possible for repeaters to prepare for other exams concurrently?",
      answer:
        "Yes, our repeater program is adaptable and allows students to prepare for other exams such as JEE or KCET alongside their NEET preparation.",
    },
  ],
};

export type CourseSlug = keyof typeof COURSE_FAQS;
