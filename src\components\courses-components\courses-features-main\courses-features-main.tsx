import { TypewriterEffectSmooth } from "~/components/ui/typewriter-effect";
import { BentoGrid, BentoGridItem } from "~/components/ui/bento-grid";
import Link from "next/link";
import { <PERSON>zy<PERSON>ottieHeader } from "./LazyLottieHeader";
export const courseAnimationMap = {
  "course-1": "Animation-pcmb",
  "course-2": "Animation-pcmc",
  "course-3": "Animation-jee",
  "course-4": "Animation-doctor",
  "course-5": "Animation-kcet",
  "course-6": "Animation-pu-course",
  "course-7": "Animation-neet-long-term",
} as const;

export type CourseId = keyof typeof courseAnimationMap;

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  url: string;
}

interface Feature72Props {
  heading?: { text: string; className: string }[];
  description?: string;
  features?: Feature[];
}

const customHeading: Feature72Props["heading"] = [
  { text: "Our", className: "text-black text-xl sm:text-2xl lg:text-3xl" },
  { text: "Courses", className: "text-black text-xl md:text-2xl lg:text-3xl" },
  { text: "-", className: "text-black text-xl md:text-2xl lg:text-3xl" },
  {
    text: "Explore",
    className:
      "text-blue-800 dark:text-blue-400 text-xl md:text-2xl lg:text-3xl",
  },
  {
    text: "and",
    className:
      "text-blue-500 dark:text-blue-200 text-xl md:text-2xl lg:text-3xl",
  },
  {
    text: "Excel",
    className:
      "text-blue-800 dark:text-blue-400 text-xl md:text-2xl lg:text-3xl",
  },
];

const CoursesFeaturesMain = ({
  heading = customHeading,
  description = "Explore our specialized programs designed to help you excel in competitive exams and academic pursuits.",
  features = [
    {
      id: "course-1",
      title: "PCMB",
      description:
        "Master Physics, Chemistry, Mathematics, and Biology with our comprehensive curriculum.",
      icon: "🧪",
      url: "/courses/pcmb",
    },
    {
      id: "course-2",
      title: "PCMC",
      description:
        "Specialized training in Physics, Chemistry, Mathematics, and Computer Science.",
      icon: "💻",
      url: "/courses/pcmc",
    },
    {
      id: "course-3",
      title: "PU Course",
      description:
        "Focused preparation for Pre-University examinations in science streams.",
      icon: "📚",
      url: "/courses/pu-course",
    },
    {
      id: "course-4",
      title: "NEET Coaching",
      description:
        "Personalized medical entrance preparation with expert faculty.",
      icon: "⚕️",
      url: "/courses/neet-for-pu-students",
    },
    {
      id: "course-5",
      title: "JEE for PU Students",
      description:
        "Comprehensive preparation for JEE entrance exam with test series.",
      icon: "📐",
      url: "/courses/jee-for-pu-students",
    },
    {
      id: "course-6",
      title: "KCET for PU Students",
      description:
        "Comprehensive KCET preparation for State-level engineering entrance focused preparation.",
      icon: "🏛️",
      url: "/courses/kcet-for-pu-students",
    },
    {
      id: "course-7",
      title: "Long Term for NEET Repeaters",
      description:
        "Special program for NEET aspirants aiming to improve their scores.",
      icon: "⚕️",
      url: "/courses/long-term-for-neet-repeaters",
    },
  ],
}: Feature72Props) => {
  return (
    <section className="w-full py-2">
      <div className="px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <TypewriterEffectSmooth words={heading} />
          <p className="max-w-[700px] text-gray-500 dark:text-gray-400 md:text-xl">
            {description}
          </p>
        </div>

        <div className="mx-auto mt-12 max-w-6xl">
          <BentoGrid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <Link href={feature.url} key={feature.id}>
                <BentoGridItem
                  key={feature.id}
                  title={
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400">
                        {feature.icon}
                      </div>
                      <span>{feature.title}</span>
                    </div>
                  }
                  description={
                    <div className="flex flex-col gap-2">
                      <p className="line-clamp-3">{feature.description}</p>
                    </div>
                  }
                  header={
                    <LazyLottieHeader courseId={feature.id as CourseId} />
                  }
                  className={[6].includes(index) ? "md:col-span-3" : ""}
                />
              </Link>
            ))}
          </BentoGrid>
        </div>
      </div>
    </section>
  );
};

export { CoursesFeaturesMain };
