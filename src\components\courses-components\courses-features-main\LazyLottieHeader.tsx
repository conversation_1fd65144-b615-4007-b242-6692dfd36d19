"use client";

import React from "react";
import { useInView } from "~/hooks/useInView";
import { LottieAnimation } from "~/components/ui/lottie-wrapper";
import { courseAnimationMap, type CourseId } from "./courses-features-main";

interface LazyLottieHeaderProps {
  courseId: CourseId;
}

export const LazyLottieHeader: React.FC<LazyLottieHeaderProps> = ({
  courseId,
}) => {
  const [ref, inView] = useInView({ threshold: 0.1 });
  return (
    <div
      ref={ref}
      className="relative flex h-40 w-full items-center justify-center rounded-xl bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30"
    >
      {inView && (
        <LottieAnimation
          filename={courseAnimationMap[courseId]}
          className="h-40"
        />
      )}
    </div>
  );
};
