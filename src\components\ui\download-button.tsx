"use client";

import { useRef } from "react";
import { Button, type ButtonProps } from "~/components/ui/button";
import { Download } from "lucide-react";
import { sendGAEvent } from "@next/third-parties/google";

interface TrackingEvent {
  eventName: string;
  eventData?: Record<string, string | number | boolean>;
}

interface DownloadButtonProps extends Omit<ButtonProps, "onClick"> {
  downloadUrl: string;
  fileName?: string;
  trackingEvent?: TrackingEvent;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
}

export function DownloadButton({
  downloadUrl,
  fileName,
  trackingEvent,
  children,
  ...props
}: DownloadButtonProps) {
  const anchorRef = useRef<HTMLAnchorElement>(null);

  const handleDownload = () => {
    // Track the download event if tracking info is provided
    if (trackingEvent) {
      sendGAEvent(
        "event",
        trackingEvent.eventName,
        trackingEvent.eventData ?? {},
      );
    }

    // Extract filename from URL if not provided
    const extractedFileName =
      !fileName && downloadUrl
        ? (downloadUrl.split("/").pop()?.split("?")[0] ?? "download")
        : fileName;

    // Update the download attribute if we have an extracted filename
    if (anchorRef.current && extractedFileName) {
      anchorRef.current.setAttribute("download", extractedFileName);
    }

    // Trigger the download using the hidden anchor tag
    if (anchorRef.current) {
      anchorRef.current.click();
    }
  };

  return (
    <>
      {/* Hidden anchor tag for download */}
      <a
        ref={anchorRef}
        href={downloadUrl}
        download={fileName}
        target="_blank"
        rel="noopener noreferrer"
        className="hidden"
        aria-hidden="true"
      >
        Download
      </a>

      {/* Visible button that triggers the download */}
      <Button onClick={handleDownload} {...props}>
        {children ?? (
          <>
            <Download className="mr-2 h-4 w-4" />
            Download
          </>
        )}
      </Button>
    </>
  );
}
