import { type <PERSON>ada<PERSON> } from "next";
import { Card, CardContent } from "~/components/ui/card";
import Faq02 from "~/components/kokonutui/faq-02";
import Script from "next/script";
import { SITE_DOMAIN } from "~/constants/siteConfig";
import { generateCourseSchema, generateFaqSchema } from "~/lib/structured-data";
import Link from "next/link";
import { Download } from "lucide-react";
import { cn } from "~/lib/utils";
import { buttonVariants } from "~/components/ui/button";
import ContactUsForm from "~/components/contact-components/contact-us-form/contact-us-form";

// Define NEET 2025 test series FAQs
const NEET_2025_FAQS = [
  {
    question: "What is included in the NEET 2025 Test Series?",
    answer:
      "Our NEET 2025 Test Series includes 3 full-length mock tests with 180 questions each, subject-wise tests for Physics, Chemistry, and Biology, chapter-wise practice questions, previous years' papers with solutions, and performance analysis tools.",
  },
  {
    question: "How will the Test Series help in my NEET 2025 preparation?",
    answer:
      "The Test Series provides exam-like practice, helps identify weak areas, improves time management, builds confidence, and familiarizes you with the actual exam pattern and difficulty level for NEET 2025.",
  },
  {
    question:
      "Is the Test Series updated according to the latest NEET 2025 pattern?",
    answer:
      "Yes, our Test Series is regularly updated to reflect the latest NEET 2025 exam pattern, syllabus changes, and question trends to ensure you're practicing with the most relevant material.",
  },
  {
    question: "How do I access the Test Series after submitting the form?",
    answer:
      "After submitting the form, you'll receive the test series materials via email with instructions on how to access the online test platform or use the offline materials.",
  },
  {
    question:
      "How many Mock Tests should I solve to score good marks in NEET 2025?",
    answer:
      "Students are advised to solve a minimum of two mock tests every month while still going through the syllabus. The number should gradually increase as you complete the NEET 2025 syllabus. When the exam approaches, aim for 10-12 mock tests every week.",
  },
  {
    question: "What is the ideal time to begin solving NEET 2025 mock tests?",
    answer:
      "Mock tests should be practiced once you are at least halfway through the NEET syllabus. This way you will get an idea of the exam pattern while learning your shortcomings and overcoming them.",
  },
  {
    question:
      "Are the questions in NEET 2025 Mock Tests more difficult than the actual NEET exam?",
    answer:
      "NEET mock tests contain questions of all difficulty levels just like the real exam. Easy, moderate, and difficult questions are added to provide you with hands-on practice for all types of questions you might encounter.",
  },
];

// Define metadata for SEO
export const metadata: Metadata = {
  title: "NEET 2025 Test Series | Aims Academy",
  description:
    "Prepare effectively for NEET 2025 with Aims Academy's comprehensive test series. Practice with exam-like questions, get detailed solutions, and improve your score.",
  keywords: [
    "NEET 2025 Test Series",
    "NEET 2025 Preparation",
    "NEET 2025 Mock Tests",
    "NEET 2025 Practice Papers",
    "NEET 2025 Exam Preparation",
    "Medical Entrance Test Series 2025",
    "NEET 2025 Question Bank",
    "Aims Academy Test Series",
  ],
  alternates: {
    canonical: `${SITE_DOMAIN}/test-series/neet/2025`,
  },
  openGraph: {
    title: "NEET 2025 Test Series | Aims Academy",
    description:
      "Comprehensive NEET 2025 test series with exam-like questions, detailed solutions, and performance analysis. Download free sample tests now!",
    url: `${SITE_DOMAIN}/test-series/neet/2025`,
    siteName: "Aims Academy",
    images: [
      {
        url: `${SITE_DOMAIN}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: "Aims Academy NEET 2025 Test Series",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NEET 2025 Test Series | Aims Academy",
    description:
      "Comprehensive NEET 2025 test series with exam-like questions, detailed solutions, and performance analysis. Download free sample tests now!",
    images: [`${SITE_DOMAIN}/og-image.jpg`],
  },
};

// Define available mock tests
const availableMockTests = [
  {
    id: "mock-test-1",
    title: "NEET 2025 Mock Test 1",
    description:
      "Full-length mock test with 180 questions covering Physics, Chemistry, and Biology.",
    date: "Available Now",
    formId: "neet-2025-mock-test-1",
    available: true,
  },
  {
    id: "mock-test-2",
    title: "NEET 2025 Mock Test 2",
    description:
      "Full-length mock test with increased difficulty level to challenge your preparation.",
    date: "Available Now",
    formId: "neet-2025-mock-test-2",
    available: true,
  },
  {
    id: "mock-test-3",
    title: "NEET 2025 Mock Test 3",
    description:
      "Final full-length mock test simulating the actual NEET exam conditions and difficulty.",
    date: "Available Now",
    formId: "neet-2025-mock-test-3",
    available: true,
  },
];

export default function NEET2025TestSeriesPage() {
  return (
    <article className="mx-auto max-w-6xl">
      {/* Hero Section */}
      <section className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold">NEET 2025 Test Series</h1>
        <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
          Prepare effectively for NEET 2025 with our comprehensive test series
          designed by expert faculty. Practice with exam-like questions and
          improve your score.
        </p>
      </section>

      {/* Introduction Section */}
      <section className="mb-16">
        <div className="rounded-lg bg-muted p-8">
          <h2 className="mb-4 text-2xl font-semibold">NEET Mock Test 2025</h2>
          <p className="mb-4">
            Preparing for the National Eligibility cum Entrance Test (NEET) is
            no small feat, especially for students aiming to secure a seat in
            the highly competitive medical field. One proven strategy to succeed
            in NEET 2025 is incorporating regular mock tests into your study
            routine.
          </p>
          <p className="mb-4">
            Mock tests recreate the exam environment, enabling students to
            assess their preparation, pinpoint weak areas, and enhance time
            management skills. Our NEET 2025 Test Series provides everything you
            need to prepare effectively for the upcoming exam.
          </p>
          <p>
            NEET is among the toughest entrance exams in India, with millions of
            aspirants vying for a limited number of medical and dental seats.
            The exam evaluates students&apos; understanding of Physics,
            Chemistry, and Biology. To achieve success in NEET 2025, practicing
            consistently with mock tests is critical.
          </p>
        </div>
      </section>

      {/* Available Mock Tests Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          Available NEET 2025 Mock Tests
        </h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {availableMockTests.map((test, index) => (
            <Card key={index} className="border-primary/20">
              <CardContent className="p-6">
                <div className="mb-4">
                  <h3 className="text-xl font-medium">{test.title}</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {test.description}
                  </p>
                </div>
                <div className="mb-4 flex items-center">
                  <span className="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    {test.date}
                  </span>
                </div>
                {test.available ? (
                  <Link
                    href={`/test-series/neet/2025/${test.id}`}
                    className={cn(
                      buttonVariants({ variant: "outline" }),
                      "w-full justify-between",
                    )}
                  >
                    Download Test
                    <Download className="h-4 w-4" />
                  </Link>
                ) : (
                  <div
                    className={cn(
                      buttonVariants({ variant: "outline" }),
                      "w-full cursor-not-allowed justify-between opacity-60",
                    )}
                  >
                    Coming Soon
                    <Download className="h-4 w-4" />
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* NEET 2025 Exam Pattern Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          NEET 2025 Exam Pattern
        </h2>
        <div className="overflow-x-auto rounded-lg border">
          <table className="w-full border-collapse">
            <thead className="bg-muted">
              <tr>
                <th className="border-b p-3 text-left">Particulars</th>
                <th className="border-b p-3 text-left">Details</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="p-3 font-medium">Frequency of NEET Exam</td>
                <td className="p-3">Once a Year</td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Total Number of Questions</td>
                <td className="p-3">180 Questions</td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Total Marks</td>
                <td className="p-3">720 Marks</td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Questions per Subject</td>
                <td className="p-3">
                  Physics: 45
                  <br />
                  Chemistry: 45
                  <br />
                  Botany: 45
                  <br />
                  Zoology: 45
                </td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Marking Scheme</td>
                <td className="p-3">
                  + 4 for the correct answer, -1 for the incorrect answer
                </td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Type of Questions</td>
                <td className="p-3">Multiple Choice Questions (MCQs)</td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Exam Mode</td>
                <td className="p-3">Offline (Pen and Paper Test)</td>
              </tr>
              <tr className="border-b">
                <td className="p-3 font-medium">Duration</td>
                <td className="p-3">3 hours</td>
              </tr>
              <tr>
                <td className="p-3 font-medium">Language options</td>
                <td className="p-3">
                  13 languages: English, Hindi, Assamese, Bengali, Gujarati,
                  Kannada, Malayalam, Marathi, Odia, Punjabi, Tamil, Telugu, and
                  Urdu.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      {/* Form Section */}
      <section id="download-form" className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          Get Access to All NEET 2025 Test Series
        </h2>
        <p className="mb-8 text-center">
          Fill out the form below to receive our comprehensive NEET 2025 test
          series materials.
        </p>
        <ContactUsForm formId="neet-2025-test-series" />
      </section>

      {/* Benefits Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          Benefits of NEET 2025 Mock Tests
        </h2>
        <div className="rounded-lg bg-muted p-6">
          <ul className="ml-6 list-disc space-y-3">
            <li>
              <span className="font-medium">Exam Simulation:</span> Mock tests
              mimic the actual exam conditions, helping you get accustomed to
              the exam format, timing, and pressure.
            </li>
            <li>
              <span className="font-medium">Self-Assessment:</span> Regular mock
              tests allow you to assess your strengths and weaknesses, enabling
              targeted preparation.
            </li>
            <li>
              <span className="font-medium">Time Management:</span> Practicing
              with mock tests helps you develop strategies to manage your time
              effectively during the exam.
            </li>
            <li>
              <span className="font-medium">Confidence Building:</span> As you
              continue to practice, you become more confident in your ability to
              tackle the actual exam.
            </li>
            <li>
              <span className="font-medium">Identification of Weak Areas:</span>{" "}
              Analyzing mock test results helps you identify areas where you
              need to focus more.
            </li>
          </ul>
        </div>
      </section>

      {/* FAQ Section */}
      <Faq02 items={NEET_2025_FAQS} />

      {/* Structured Data */}
      <Script
        id="test-series-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            generateCourseSchema({
              name: "NEET 2025 Test Series",
              description:
                "Comprehensive test series for NEET 2025 preparation with mock tests, subject-wise tests, and detailed solutions.",
              courseCode: "NEET-TS-2025",
            }),
          ),
        }}
      />

      <Script
        id="faq-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateFaqSchema(NEET_2025_FAQS)),
        }}
      />
    </article>
  );
}
