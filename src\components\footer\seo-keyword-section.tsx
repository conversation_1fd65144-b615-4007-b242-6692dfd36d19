import Link from "next/link";

interface SEOKeywordSectionProps {
  className?: string;
}

// Define our course links with proper URLs
const courseLinks = [
  {
    text: "NEET for PU Students",
    url: "/courses/neet-for-pu-students",
    variations: [
      "NEET Coaching for PU Students",
      "NEET PU Integrated Program",
      "NEET Classes for PU Students",
    ],
  },
  {
    text: "Long Term for NEET Repeaters",
    url: "/courses/long-term-for-neet-repeaters",
    variations: [
      "NEET Repeaters Program",
      "NEET Long Term Coaching",
      "NEET Coaching for Droppers",
    ],
  },
  {
    text: "JEE for PU Students",
    url: "/courses/jee-for-pu-students",
    variations: [
      "JEE Coaching in Bangalore",
      "JEE Preparation for PU Students",
    ],
  },
  {
    text: "KCET for PU Students",
    url: "/courses/kcet-for-pu-students",
    variations: [
      "KCET Coaching in Bangalore",
      "KCET Preparation for PU Students",
    ],
  },
  {
    text: "PU Course",
    url: "/courses/pu-course",
    variations: ["PU Science Courses in Bangalore", "PU College in Yelahanka"],
  },
];

// Define key areas in Bangalore (reduced list for better appearance)
const bangaloreAreas = [
  "Yelahanka",
  "North Bangalore",
  "Hebbal",
  "Sahakarnagar",
  "RT Nagar",
  "Jakkur",
  "Devanahalli",
  "Bagalur",
];

const SEOKeywordSection: React.FC<SEOKeywordSectionProps> = ({ className }) => {
  return (
    <div className={`mt-12 border-t border-gray-200 pt-6 ${className}`}>
      <h3 className="mb-4 text-center text-base font-medium text-muted-foreground">
        Popular Searches
      </h3>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* First column - Our Courses */}
        <div>
          <h4 className="mb-3 text-sm font-semibold text-primary">
            Our Programs
          </h4>
          <ul className="space-y-1.5">
            {courseLinks.map((course, index) => (
              <li key={index} className="text-xs">
                <Link
                  href={course.url}
                  className="text-muted-foreground hover:text-primary hover:underline"
                  aria-label={course.text}
                >
                  {course.text} in Bangalore
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Second column - Course Variations */}
        <div>
          <h4 className="mb-3 text-sm font-semibold text-primary">
            Specialized Coaching
          </h4>
          <ul className="space-y-1.5">
            {courseLinks
              .flatMap((course, courseIndex) =>
                course.variations.map((variation, varIndex) => (
                  <li key={`${courseIndex}-${varIndex}`} className="text-xs">
                    <Link
                      href={course.url}
                      className="text-muted-foreground hover:text-primary hover:underline"
                      aria-label={variation}
                    >
                      {variation}
                    </Link>
                  </li>
                )),
              )
              .slice(0, 10)}
          </ul>
        </div>

        {/* Third column - Locations */}
        <div>
          <h4 className="mb-3 text-sm font-semibold text-primary">
            Serving Areas
          </h4>
          <ul className="space-y-1.5">
            {bangaloreAreas.map((area, index) => (
              <li key={index} className="text-xs">
                <Link
                  href="/contact"
                  className="text-muted-foreground hover:text-primary hover:underline"
                  aria-label={`NEET Coaching in ${area}`}
                >
                  NEET Coaching in {area}
                </Link>
              </li>
            ))}
            <li className="text-xs">
              <Link
                href="/courses/neet-for-pu-students"
                className="text-muted-foreground hover:text-primary hover:underline"
                aria-label="Best NEET Coaching in Bangalore"
              >
                Best NEET Coaching in Bangalore
              </Link>
            </li>
            <li className="text-xs">
              <Link
                href="/courses/long-term-for-neet-repeaters"
                className="text-muted-foreground hover:text-primary hover:underline"
                aria-label="Top NEET Long Term Coaching in Bangalore"
              >
                Top NEET Long Term Coaching in Bangalore
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export { SEOKeywordSection };
