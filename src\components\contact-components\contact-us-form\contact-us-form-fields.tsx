"use client";

import { useCallback } from "react";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectValue,
  SelectItem,
} from "~/components/ui/select";
import { courseOptions } from "~/constants/courses";

interface FormData {
  name: string;
  phone: string;
  email: string;
  course: string;
  message?: string;
}

interface ContactUsFormFieldsProps {
  formData: FormData;
  onFormChange: (field: keyof FormData, value: string) => void;
  formId?: string;
}

export function ContactUsFormFields({
  formData,
  onFormChange,
  formId = "default",
}: ContactUsFormFieldsProps) {
  const handleChange = useCallback(
    (field: keyof FormData, value: string) => {
      onFormChange(field, value);
    },
    [onFormChange],
  );

  return (
    <div className="space-y-4">
      <input type="hidden" name="_template" value="box" />

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div>
          <Label htmlFor={`name-${formId}`}>Name *</Label>
          <Input
            id={`name-${formId}`}
            value={formData.name}
            onChange={(e) => handleChange("name", e.target.value)}
            placeholder="Your Name"
          />
        </div>

        <div>
          <Label htmlFor={`phone-${formId}`}>Phone *</Label>
          <Input
            id={`phone-${formId}`}
            value={formData.phone}
            onChange={(e) => handleChange("phone", e.target.value)}
            placeholder="Your Phone Number"
          />
        </div>

        <div>
          <Label htmlFor={`email-${formId}`}>Email</Label>
          <Input
            id={`email-${formId}`}
            type="email"
            value={formData.email}
            onChange={(e) => handleChange("email", e.target.value)}
            placeholder="Your Email"
          />
        </div>

        <div>
          <Label htmlFor={`course-${formId}`}>Select Course</Label>
          <Select
            value={formData.course}
            onValueChange={(value: string) => handleChange("course", value)}
          >
            <SelectTrigger id={`course-${formId}`}>
              <SelectValue placeholder="Select Course" />
            </SelectTrigger>
            <SelectContent>
              {courseOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor={`message-${formId}`}>Message</Label>
        <Textarea
          id={`message-${formId}`}
          value={formData.message}
          onChange={(e) => handleChange("message", e.target.value)}
          placeholder="Your Message (Optional)"
        />
      </div>
    </div>
  );
}
