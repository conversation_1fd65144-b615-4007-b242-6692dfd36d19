import { type ReviewRoot } from "./types";
import { EXAMPLE_REVIEWS } from "~/constants/reviews-example";

export async function fetchGoogleReviews() {
  const startTime = Date.now();
  const formatTime = () => {
    const now = new Date();
    return `${now.toISOString()} [${Date.now() - startTime}ms]`;
  };

  console.log(`=== REVIEW FETCH PROCESS STARTED === ${formatTime()}`);
  console.log("Environment:", process.env.NODE_ENV);
  console.log("Next phase:", process.env.NEXT_PHASE);

  // Always attempt to fetch during build for testing purposes
  // Comment this out later if you want to use example data during build
  /*
  if (process.env.NEXT_PHASE === "phase-production-build") {
    console.log("BUILD TIME: Using example reviews during static build");
    return EXAMPLE_REVIEWS;
  }
  */

  // Check if API key exists
  if (!process.env.FEATURABLE_API_KEY) {
    console.log(`ERROR: FEATURABLE_API_KEY is missing ${formatTime()}`);
    return EXAMPLE_REVIEWS; // Return example data if API key is missing
  }

  console.log(
    `API Key exists (first 4 chars): ${process.env.FEATURABLE_API_KEY.substring(0, 4)}... ${formatTime()}`,
  );

  try {
    console.log(
      `Attempting to fetch reviews from Featurable API ${formatTime()}`,
    );
    const apiUrl = `https://featurable.com/api/v1/widgets/${process.env.FEATURABLE_API_KEY}`;
    console.log("API URL:", apiUrl);

    const fetchStartTime = Date.now();
    const res = await fetch(
      apiUrl,
      { next: { revalidate: 86400 } }, // Revalidate every 24 hours
    );
    const fetchEndTime = Date.now();
    const fetchDuration = fetchEndTime - fetchStartTime;

    console.log(
      `API Response status: ${res.status} (fetch took ${fetchDuration}ms) ${formatTime()}`,
    );

    if (!res.ok) {
      console.error(
        `Failed to fetch reviews, status: ${res.status}, text:`,
        await res.text(),
        formatTime(),
      );
      console.log(
        `FALLBACK: Using example reviews due to failed API call ${formatTime()}`,
      );
      return EXAMPLE_REVIEWS;
    }

    const jsonStartTime = Date.now();
    const data = (await res.json()) as ReviewRoot;
    const jsonEndTime = Date.now();
    const jsonDuration = jsonEndTime - jsonStartTime;

    console.log(
      `SUCCESS: Received data from API with ${data.reviews?.length || 0} reviews (JSON parsing took ${jsonDuration}ms) ${formatTime()}`,
    );
    return data;
  } catch (error) {
    console.error(`EXCEPTION: Error fetching reviews:`, error, formatTime());
    console.log(
      `FALLBACK: Using example reviews due to exception ${formatTime()}`,
    );
    return EXAMPLE_REVIEWS;
  } finally {
    const endTime = Date.now();
    const totalDuration = endTime - startTime;
    console.log(
      `=== REVIEW FETCH PROCESS COMPLETED === (Total time: ${totalDuration}ms) ${formatTime()}`,
    );
  }
}
