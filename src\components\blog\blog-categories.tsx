import Link from "next/link";
import { type BlogCategory as ContentlayerBlogCategory } from ".contentlayer/generated";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import {
  BookOpen,
  Target,
  GraduationCap,
  Trophy,
  Newspaper,
  Filter,
} from "lucide-react";

interface BlogCategoriesProps {
  categories: ContentlayerBlogCategory[];
  currentCategory?: string;
  showPostCount?: boolean;
  className?: string;
}

// Icon mapping for categories
const categoryIcons: Record<string, React.ReactNode> = {
  "study-tips": <BookOpen className="h-4 w-4" />,
  "exam-strategy": <Target className="h-4 w-4" />,
  "subject-guides": <GraduationCap className="h-4 w-4" />,
  "success-stories": <Trophy className="h-4 w-4" />,
  "news-updates": <Newspaper className="h-4 w-4" />,
};

export default function BlogCategories({
  categories,
  currentCategory,
  showPostCount = false,
  className,
}: BlogCategoriesProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Section Header */}
      <div className="flex items-center gap-2">
        <Filter className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold text-foreground">
          Browse by Category
        </h3>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-5">
        {/* All Categories Link */}
        <Link href="/blog">
          <Button
            variant={!currentCategory ? "default" : "outline"}
            className={cn(
              "h-auto w-full justify-start gap-2 px-4 py-3",
              !currentCategory && "bg-primary text-primary-foreground",
            )}
          >
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                <span className="text-sm font-bold text-primary">All</span>
              </div>
              <div className="text-left">
                <div className="text-sm font-medium">All Articles</div>
                {showPostCount && (
                  <div className="text-xs text-muted-foreground">
                    View all posts
                  </div>
                )}
              </div>
            </div>
          </Button>
        </Link>

        {/* Individual Categories */}
        {categories.map((category) => {
          const isActive = currentCategory === category.slug;
          const icon = categoryIcons[category.slug] ?? (
            <BookOpen className="h-4 w-4" />
          );

          return (
            <Link key={category.slug} href={`/blog/${category.slug}`}>
              <Button
                variant={isActive ? "default" : "outline"}
                className={cn(
                  "group h-auto w-full justify-start gap-2 px-4 py-3 hover:border-primary/50",
                  isActive && "bg-primary text-primary-foreground",
                )}
              >
                <div className="flex items-center gap-2">
                  <div
                    className={cn(
                      "flex h-8 w-8 items-center justify-center rounded-full transition-colors",
                      isActive
                        ? "bg-primary-foreground/20"
                        : "bg-primary/10 group-hover:bg-primary/20",
                    )}
                    style={{
                      backgroundColor: isActive
                        ? undefined
                        : `${category.color}20`,
                    }}
                  >
                    <div
                      className={cn(
                        isActive ? "text-primary-foreground" : "text-primary",
                      )}
                    >
                      {icon}
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="line-clamp-1 text-sm font-medium">
                      {category.name}
                    </div>
                    {showPostCount && (
                      <div className="text-xs text-muted-foreground">
                        {/* This would need to be calculated from posts */}
                        View posts
                      </div>
                    )}
                  </div>
                </div>
              </Button>
            </Link>
          );
        })}
      </div>

      {/* Category Description */}
      {currentCategory && (
        <div className="mt-6 rounded-lg border bg-muted/50 p-4">
          {(() => {
            const category = categories.find(
              (cat) => cat.slug === currentCategory,
            );
            if (!category) return null;

            return (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div
                    className="flex h-6 w-6 items-center justify-center rounded-full"
                    style={{ backgroundColor: `${category.color}20` }}
                  >
                    <div style={{ color: category.color }}>
                      {categoryIcons[category.slug] ?? (
                        <BookOpen className="h-3 w-3" />
                      )}
                    </div>
                  </div>
                  <h4 className="font-semibold text-foreground">
                    {category.name}
                  </h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  {category.description}
                </p>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}

// Compact version for sidebar
export function BlogCategoriesCompact({
  categories,
  currentCategory,
  className,
}: BlogCategoriesProps) {
  return (
    <div className={cn("space-y-3", className)}>
      <h4 className="font-semibold text-foreground">Categories</h4>

      <div className="space-y-2">
        {/* All Categories Link */}
        <Link
          href="/blog"
          className={cn(
            "flex items-center gap-2 rounded-md px-3 py-2 text-sm transition-colors",
            !currentCategory
              ? "bg-primary text-primary-foreground"
              : "hover:bg-muted",
          )}
        >
          <span className="font-medium">All Articles</span>
        </Link>

        {/* Individual Categories */}
        {categories.map((category) => {
          const isActive = currentCategory === category.slug;
          const icon = categoryIcons[category.slug] ?? (
            <BookOpen className="h-3 w-3" />
          );

          return (
            <Link
              key={category.slug}
              href={`/blog/${category.slug}`}
              className={cn(
                "flex items-center gap-2 rounded-md px-3 py-2 text-sm transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted",
              )}
            >
              <div
                className="flex h-4 w-4 items-center justify-center"
                style={{ color: isActive ? undefined : category.color }}
              >
                {icon}
              </div>
              <span className="font-medium">{category.name}</span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
