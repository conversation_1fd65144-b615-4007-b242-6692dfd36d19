import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "~/components/ui/card";
import GoogleIcon from "~/icons/GoogleIcon";
import { Star } from "lucide-react";
import { Skeleton } from "../ui/skeleton";

interface ReviewCardProps {
  rating: number; // average rating, e.g. 4.8
  totalReviews: number; // total number of reviews, e.g. 502
  userImages: string[]; // URLs of user images
  reviews?: {
    reviewer: {
      displayName: string;
    };
  }[];
}

export function ReviewBadgeSkeleton() {
  return (
    <div className="flex flex-row flex-wrap items-center justify-center gap-4">
      <Card className="max-w-sm rounded-lg border shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex flex-row items-center justify-center gap-2">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex flex-col items-start gap-1">
              <CardTitle className="flex items-center gap-2 text-xl">
                <div className="flex items-center gap-1">
                  {Array.from({ length: 5 }, (_, i: number) => (
                    <Skeleton key={i} className="h-5 w-5 rounded-sm" />
                  ))}
                </div>
              </CardTitle>
              <CardDescription className="text-sm">
                <Skeleton className="h-4 w-48" />
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col items-center gap-4 p-0 py-1">
          <div className="flex items-center -space-x-2">
            {Array.from({ length: 5 }, (_, i: number) => (
              <Skeleton key={i} className="h-8 w-8 rounded-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function ReviewBadge({
  rating = 4.8,
  totalReviews = 502,
  userImages = [],
  reviews = [],
}: ReviewCardProps) {
  // Optional: Generate the star rating. You can also show partial stars if you want to reflect 4.8 precisely.
  const stars = Array.from({ length: 5 }, (_, i) =>
    i < Math.round(rating) ? "filled" : "empty",
  );

  return (
    <div className="flex flex-row flex-wrap items-center justify-center gap-4">
      <Card className="max-w-sm rounded-lg border shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex flex-row items-center justify-center gap-2">
            <GoogleIcon
              style={{
                width: "2.5rem",
                height: "2.5rem",
              }}
            />
            <div className="flex flex-col items-start gap-1">
              <CardTitle className="flex items-center gap-2 text-xl">
                {/* Render the stars */}
                <div className="flex items-center">
                  {stars.map((status, index) => (
                    <Star
                      key={index}
                      fill={status === "filled" ? "currentColor" : "none"}
                      className={`h-5 w-5 ${
                        status === "filled"
                          ? "text-yellow-500"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
              </CardTitle>
              <CardDescription className="text-sm">
                <span className="font-semibold">
                  {rating} rating from {totalReviews} reviews
                </span>
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex flex-col items-center gap-4 p-0 py-1">
          <div className="flex items-center -space-x-2">
            {userImages.map((imgUrl, index) => (
              <Avatar key={index} className="h-8 w-8 border-2 border-white">
                <AvatarImage
                  src={imgUrl}
                  alt={`User ${index + 1}`}
                  className="rounded-full"
                  loading="eager"
                  width={32}
                  height={32}
                />
                <AvatarFallback
                  className={` ${["bg-pink-500", "bg-purple-500", "bg-blue-500", "bg-green-500", "bg-yellow-500"][index % 5]} font-medium text-white`}
                >
                  {reviews[index]?.reviewer.displayName
                    .split(" ")
                    .map((n: string) => n[0])
                    .join("")
                    .slice(0, 2)
                    .toUpperCase() ?? "U"}
                </AvatarFallback>
              </Avatar>
            ))}
            {userImages.length > 3 && (
              <div className="inline-flex h-8 w-8 items-center justify-center rounded-full border-2 border-white bg-gray-100 text-sm font-medium text-gray-600">
                +{userImages.length - 3}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
