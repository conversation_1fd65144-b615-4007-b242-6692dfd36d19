import { SITE_CONFIG } from "~/constants/siteConfig";
import FacebookIcon from "~/icons/facebook";
import Instagram from "~/icons/instagram";
import Telegram from "~/icons/telegram";
import YouTube from "~/icons/youtube";

export type SocialLinks = {
  text: string;
  url: string;
  icon?: React.ReactNode;
}[];

const IconStyle = { width: "1.5rem", height: "1.5rem" };

export const SOCIAL_LINKS: SocialLinks = [
  {
    text: "Telegram",
    url: "#",
    icon: <Telegram style={IconStyle} />,
  },
  {
    text: "Instagram",
    url: SITE_CONFIG.social.instagram,
    icon: <Instagram style={IconStyle} />,
  },
  {
    text: "Youtube",
    url: "#",
    icon: <YouTube style={IconStyle} />,
  },
  {
    text: "facebook",
    url: SITE_CONFIG.social.facebook,
    icon: <FacebookIcon style={{ width: "1.75rem", height: "1.75rem" }} />,
  },
];
