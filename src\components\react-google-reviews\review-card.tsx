import { Card, CardHeader, CardContent } from "~/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "~/components/ui/avatar";
import { Star } from "lucide-react";

export interface ReviewCardProps {
  name: string;
  date: string;
  rating: number;
  comment: string;
  avatarUrl?: string;
}

export function ReviewCard({
  name,
  date,
  rating,
  comment,
  avatarUrl,
}: ReviewCardProps) {
  return (
    <Card className="h-44 overflow-hidden rounded-xl border bg-background dark:border-zinc-700 md:w-96">
      <CardHeader className="flex flex-row items-center space-x-3 p-4 pb-2">
        <Avatar>
          <AvatarImage
            src={avatarUrl}
            alt={`User ${name}'s avatar`}
            className="rounded-full"
            loading="lazy"
            width={32}
            height={32} 
          />
          <AvatarFallback>{name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">{name}</h3>
            <span className="text-sm text-muted-foreground">{date}</span>
          </div>
          <div className="mt-1 flex">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < rating
                    ? "fill-yellow-400 text-yellow-400"
                    : "fill-muted text-muted"
                }`}
              />
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-2 pt-0">
        <p className="line-clamp-4 text-sm">{comment}</p>
      </CardContent>
    </Card>
  );
}
