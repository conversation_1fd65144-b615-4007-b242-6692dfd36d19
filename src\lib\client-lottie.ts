/* eslint-disable */
"use client";

// Client-side version of the lottie animation loader
export async function fetchAnimationDataClient(filename: string) {
  try {
    // Dynamic import for client-side
    const animationData = await import(`~/assets/lottie/${filename}.json`);
    return animationData.default;
  } catch (error) {
    console.error(`Failed to load animation: ${filename}`, error);
    return null;
  }
}
