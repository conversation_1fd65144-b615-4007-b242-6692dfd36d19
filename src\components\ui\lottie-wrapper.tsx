"use client";
import { fetchAnimationDataClient } from "~/lib/client-lottie";
import { LazyLottie } from "./LazyLottie";

interface LottieAnimationProps {
  filename: string;
  loop?: boolean;
  className?: string;
}

export function LottieAnimation({
  filename,
  loop = true,
  className,
  ...props
}: LottieAnimationProps) {
  return (
    <LazyLottie
      getAnimationData={() => fetchAnimationDataClient(filename)}
      id={filename}
      loop={loop}
      className={className}
      {...props}
    />
  );
}
