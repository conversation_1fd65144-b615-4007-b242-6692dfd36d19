// NOTE This file is auto-generated by Contentlayer

import blogCategories__examStrategyMdx from './blog-categories__exam-strategy.mdx.json' with { type: 'json' }
import blogCategories__newsUpdatesMdx from './blog-categories__news-updates.mdx.json' with { type: 'json' }
import blogCategories__studyTipsMdx from './blog-categories__study-tips.mdx.json' with { type: 'json' }
import blogCategories__subjectGuidesMdx from './blog-categories__subject-guides.mdx.json' with { type: 'json' }
import blogCategories__successStoriesMdx from './blog-categories__success-stories.mdx.json' with { type: 'json' }

export const allBlogCategories = [blogCategories__examStrategyMdx, blogCategories__newsUpdatesMdx, blogCategories__studyTipsMdx, blogCategories__subjectGuidesMdx, blogCategories__successStoriesMdx]
