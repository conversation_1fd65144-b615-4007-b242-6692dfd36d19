import type { Metadata } from "next";
import { CoursesPageHero } from "~/components/courses-components/courses-page-hero/courses-page-hero";
import { ProgramFeatureSection } from "~/components/courses-components/feature-cards-section";
import { TrainingProcessSection } from "~/components/courses-components/training-process-section";
import { WhyChooseUs } from "~/components/courses-components/why-choose-us";
import { kcetForPuStudentsProgramFeatureList } from "~/constants/courses";

export const metadata: Metadata = {
  title: "KCET Coaching in Bangalore | Expert Guidance",
  description:
    "Achieve success in KCET with expert coaching at Aims Academy in Bangalore. Enroll now for personalized mentoring and proven results.",
  keywords: [
    "KCET Coaching Bangalore",
    "Best KCET Classes Yelahanka",
    "KCET Preparation Bangalore",
  ],
  openGraph: {
    title: "KCET Coaching in Bangalore | Expert Guidance",
    description:
      "Join Aims Academy for expert KCET coaching in Bangalore. Proven track record in KCET preparation. Enroll now!",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy KCET Coaching",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "KCET Coaching in Bangalore | Expert Guidance",
    description:
      "Excel in KCET with Aims Academy's expert coaching in Bangalore. Join us for top results!",
  },
};

const KcetForPuStudents = () => {
  const heroProps = {
    heading: "KCET for PU Students",
    subheading: "Expert KCET Preparation",
    description:
      "Our KCET coaching program for PU students is designed to provide expert guidance and extensive practice sessions. Join us to excel in your KCET exams and secure admission to top colleges.",
    buttons: {
      primary: {
        text: "Enroll Now",
        url: "#contact-form",
      },
      secondary: {
        text: "Learn More",
        url: "#contact-form",
      },
    },
    image: {
      src: "/exam_courses_1.svg",
      alt: "KCET for PU Students",
    },
  };

  return (
    <article>
      <CoursesPageHero {...heroProps} />
      <ProgramFeatureSection
        features={kcetForPuStudentsProgramFeatureList}
        imageSrc="/program-feature-img.png"
      />
      <WhyChooseUs />
      <TrainingProcessSection courseType="KCET_FOR_PU_STUDENTS" />
    </article>
  );
};

export default KcetForPuStudents;
