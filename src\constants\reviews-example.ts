import { ReviewRoot } from "~/lib/featurable/types";

export const EXAMPLE_REVIEWS: ReviewRoot = {
  success: true,
  widget: "google-reviews",
  layout: "carousel",
  published: true,
  pinnedReviews: [],
  hiddenReviews: [],
  highlights: [
    {
      text: "Highly rated institution",
      icon: "star",
      color: "gold",
    },
  ],
  titles: ["Excellent Academy", "Top Rated"],
  allowedDomains: ["aimsacademy.com"],
  config: {
    sortBy: "newest",
    theme: "light",
    pageSize: 5,
    logoVariant: "full",
    minStars: 1,
    reviewVariant: "default",
    nameDisplay: "full",
    title: null,
    showTitle: true,
    carouselAutoplay: true,
    carouselSpeed: 5000,
    sliderAutoplay: false,
    sliderSpeed: 3000,
    maxCharacters: 250,
    dateDisplay: "relative",
    floatPosition: "bottom-right",
    floating: false,
    dismissable: true,
    schema: true,
    schemaType: "LocalBusiness",
    schemaSubType: null,
    schemaSubSubType: null,
    showBranding: true,
    summary: true,
    summaryPosition: "top",
    summarySemantic: false,
    summarySemanticOverride: null,
    summaryLogoVariant: "icon",
    summaryReviewButton: true,
    summaryReviewButtonText: null,
    aiTitlesEnabled: false,
    language: "en",
    name: null,
    hideEmptyReviews: true,
    showDatesOnPinnedReviews: false,
    showBadgeLabel: true,
    customCss: "",
    themeId: "default",
    showProfilePictures: true,
  },
  totalReviewCount: 42,
  averageRating: 4.8,
  batchSize: 10,
  profileUrl: "https://g.page/aims-academy/review",
  reviews: [
    {
      reviewId: "review1",
      reviewer: {
        profilePhotoUrl: "/public/faculty/default.jpg",
        displayName: "Happy Parent",
        isAnonymous: false,
      },
      starRating: 5,
      comment: "Excellent teaching faculty and infrastructure",
      createTime: "2025-01-15T10:30:00Z",
      updateTime: "2025-01-15T10:30:00Z",
      reviewReply: {
        comment: "Thank you for your kind words!",
        updateTime: "2025-01-16T09:15:00Z",
      },
    },
  ],
};
