"use client";

import Link from "next/link";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "~/components/ui/navigation-menu";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { Button, buttonVariants } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { ThemeToggleButton } from "../theme-provider/theme-toggle-button";
import { SITE_CONFIG } from "~/constants/siteConfig";
import { COURSES_PAGES, PAGES } from "~/constants/pages";
import Image from "next/image";
import {
  HomeIcon,
  Book,
  ImageIcon,
  Info,
  Contact2,
  <PERSON>u,
  <PERSON>Text,
  BookOpen,
} from "lucide-react";
import { SOCIAL_LINKS } from "./social-links";
import { usePathname } from "next/navigation";

// Type Definitions
type NavigationItem = {
  title: string;
  href?: string;
  icon?: React.ReactNode;
  description?: string;
  submenu?: NavigationItem[];
};

type NavigationConfig = {
  institutionName: string;
  logoPath: string;
  mainMenu: NavigationItem[];
};

export const NAVIGATION_CONFIG: NavigationConfig = {
  institutionName: SITE_CONFIG.name,
  logoPath: "/logo.png",
  mainMenu: [
    {
      title: "Home",
      href: "/",
      icon: <HomeIcon className="size-5 shrink-0" />,
    },
    {
      title: "Courses",
      href: "/courses",
      icon: <Book className="size-5 shrink-0" />,
      submenu: [
        {
          title: COURSES_PAGES.PCMB.title,
          href: COURSES_PAGES.PCMB.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.PCMB.description,
        },
        {
          title: COURSES_PAGES.PCMC.title,
          href: COURSES_PAGES.PCMC.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.PCMC.description,
        },
        {
          title: COURSES_PAGES.PU_COURSE.title,
          href: COURSES_PAGES.PU_COURSE.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.PU_COURSE.description,
        },
        {
          title: COURSES_PAGES.NEET_FOR_PU_STUDENTS.title,
          href: COURSES_PAGES.NEET_FOR_PU_STUDENTS.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.NEET_FOR_PU_STUDENTS.description,
        },
        {
          title: COURSES_PAGES.JEE_FOR_PU_STUDENTS.title,
          href: COURSES_PAGES.JEE_FOR_PU_STUDENTS.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.JEE_FOR_PU_STUDENTS.description,
        },
        {
          title: COURSES_PAGES.KCET_FOR_PU_STUDENTS.title,
          href: COURSES_PAGES.KCET_FOR_PU_STUDENTS.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.KCET_FOR_PU_STUDENTS.description,
        },
        {
          title: COURSES_PAGES.NEET_FOR_REPEATERS.title,
          href: COURSES_PAGES.NEET_FOR_REPEATERS.url,
          icon: <Book className="size-5 shrink-0" />,
          description: COURSES_PAGES.NEET_FOR_REPEATERS.description,
        },
      ],
    },
    {
      title: "Gallery",
      href: "/gallery",
      icon: <ImageIcon className="size-5 shrink-0" />,
    },
    {
      title: "About Us",
      href: "/about",
      icon: <Info className="size-5 shrink-0" />,
    },
    {
      title: "Test Series",
      href: PAGES.TEST_SERIES.url,
      icon: <FileText className="size-5 shrink-0" />,
    },
    {
      title: "Blog",
      href: "/blog",
      icon: <BookOpen className="size-5 shrink-0" />,
    },
    {
      title: "Contact",
      href: "/contact",
      icon: <Contact2 className="size-5 shrink-0" />,
    },
  ],
};

export const NavMenuWrapper: React.FC = () => {
  const pathname = usePathname();

  // Add helper function to check active path
  const isActive = (href: string) => {
    // For the home page, only exact match
    if (href === "/") return pathname === href;

    // For other pages, check if the current path starts with the href
    // This ensures that subpages like /test-series/neet/2025/mock-test-3
    // will highlight the Test Series menu item
    return pathname.startsWith(href);
  };

  return (
    <section className="sticky top-0 z-50 bg-inherit p-2">
      {/* Desktop Navigation */}
      <div className="hidden items-center justify-between lg:flex">
        <div className="flex items-center gap-6">
          <Link href="/" className="flex items-center gap-2">
            <div className="flex items-center gap-2 lg:justify-start">
              <Image
                src={"/logo.png"}
                alt="Institution Logo"
                title={NAVIGATION_CONFIG.institutionName}
                width={128}
                height={119}
                className="aspect-[128/119] h-10 w-[43px] object-contain" // Enforce precise aspect ratio
                priority={true}
              />
              <p className="text-xl font-semibold">
                {NAVIGATION_CONFIG.institutionName}
              </p>
            </div>
          </Link>

          <NavigationMenu>
            <NavigationMenuList>
              {NAVIGATION_CONFIG.mainMenu.map((item, idx) => (
                <NavigationMenuItem key={idx} className="text-muted-foreground">
                  {item.submenu ? (
                    <>
                      <NavigationMenuTrigger className="flex items-center gap-2">
                        <Link
                          href={item.href ?? "#"}
                          className="flex items-center gap-2 text-muted-foreground"
                        >
                          {item.icon}
                          {item.title}
                        </Link>
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="w-80 p-3">
                          {item.submenu.map((subItem, subIdx) => (
                            <li key={subIdx}>
                              <Link
                                href={subItem.href ?? "#"}
                                className={cn(
                                  "flex items-center gap-4 rounded-md p-3",
                                  "no-underline hover:bg-accent",
                                )}
                              >
                                {subItem.icon}
                                <div>
                                  <div className="text-sm font-semibold">
                                    {subItem.title}
                                  </div>
                                  {subItem.description && (
                                    <p className="text-sm text-muted-foreground">
                                      {subItem.description}
                                    </p>
                                  )}
                                </div>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </NavigationMenuContent>
                    </>
                  ) : (
                    <NavigationMenuLink
                      asChild
                      active={isActive(item.href ?? "")}
                    >
                      <Link
                        href={item.href ?? "#"}
                        className={cn(
                          navigationMenuTriggerStyle(),
                          buttonVariants({ variant: "ghost" }),
                          "flex items-center gap-2 text-muted-foreground",
                        )}
                      >
                        {item.icon}
                        {item.title}
                      </Link>
                    </NavigationMenuLink>
                  )}
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        <div className="flex flex-row gap-3">
          <ThemeToggleButton />
          <Button>
            <Link href="/contact">Admissions Open</Link>
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center gap-2">
            <div className="flex items-center gap-2 lg:justify-start">
              <Image
                src={"/logo.png"}
                alt="Institution Logo"
                title={NAVIGATION_CONFIG.institutionName}
                width={128}
                height={119}
                className="aspect-[128/119] h-10 w-[43px] object-contain" // Enforce precise aspect ratio
                priority={true}
              />
              <p className="text-xl font-semibold">
                {NAVIGATION_CONFIG.institutionName}
              </p>
            </div>
          </Link>

          <Sheet>
            <div className="flex items-center gap-4">
              <ThemeToggleButton />
              <SheetTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  aria-label="Navigation Menu"
                >
                  <Menu className="size-4" />
                </Button>
              </SheetTrigger>
            </div>
            <SheetContent className="overflow-y-auto pt-12">
              <SheetHeader>
                <SheetTitle>
                  <span>{NAVIGATION_CONFIG.institutionName}</span>
                </SheetTitle>
                <SheetDescription>
                  Transforming Aspirants to Achievers
                </SheetDescription>
              </SheetHeader>

              <div className="mt-6 space-y-4">
                {NAVIGATION_CONFIG.mainMenu.map((item, idx) =>
                  item.submenu ? (
                    <Accordion
                      key={idx}
                      type="single"
                      collapsible
                      className="w-full"
                    >
                      <AccordionItem value={item.title} className="border-b-0">
                        <AccordionTrigger className="flex items-center justify-start gap-2 py-2 font-semibold hover:no-underline">
                          {item.icon}
                          {item.title}
                        </AccordionTrigger>
                        <AccordionContent>
                          {item.submenu.map((subItem, subIdx) => (
                            <SheetClose asChild key={subIdx}>
                              <Link
                                href={subItem.href ?? "#"}
                                className={cn(
                                  "flex items-center gap-4 rounded-md p-3 hover:bg-accent",
                                  "transition-colors",
                                )}
                              >
                                {subItem.icon}
                                <div>
                                  <div className="font-semibold">
                                    {subItem.title}
                                  </div>
                                  {subItem.description && (
                                    <p className="text-sm text-muted-foreground">
                                      {subItem.description}
                                    </p>
                                  )}
                                </div>
                              </Link>
                            </SheetClose>
                          ))}
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  ) : (
                    <SheetClose asChild key={idx}>
                      <Link
                        href={item.href ?? "#"}
                        className={cn(
                          "flex items-center gap-2 py-2 font-semibold",
                          isActive(item.href ?? "") &&
                            "underline underline-offset-4",
                        )}
                      >
                        {item.icon}
                        {item.title}
                      </Link>
                    </SheetClose>
                  ),
                )}
              </div>

              <div className="mt-6 space-y-4">
                <Button className="w-full">
                  <Link href="/contact">Admissions Open</Link>
                </Button>
                <section className="flex items-center justify-center gap-4">
                  {SOCIAL_LINKS.map((item, idx) => (
                    <Button variant="ghost" key={idx}>
                      <Link href={item.url} role={item.text}>
                        {item.icon}
                      </Link>
                    </Button>
                  ))}
                </section>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </section>
  );
};
