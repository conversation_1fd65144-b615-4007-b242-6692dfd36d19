"use client";

import { usePathname } from "next/navigation";
import { RelatedCourseList } from "~/components/courses-components/courses-seo/explore-related-courses/explore-related-courses";
import Faq02 from "~/components/kokonutui/faq-02";
import { COURSE_FAQS } from "~/constants/courses";
import { COURSES_PAGES, type PagesMetadata } from "~/constants/pages";

export default function DynamicComponents() {
  const pathname = usePathname();
  const isRootPage = pathname === "/courses";

  // Get current course slug from pathname
  const courseSlug = (Object.values(COURSES_PAGES) as PagesMetadata[])
    .find((course) => {
      return course.url === `${pathname}`;
    })
    ?.url.split("/")[2];

  return (
    <>
      {isRootPage ? (
        <>
          <RelatedCourseList excludeCourses="" />
          <Faq02 items={COURSE_FAQS.root ?? []} />
        </>
      ) : (
        courseSlug && (
          <>
            <Faq02 items={COURSE_FAQS[courseSlug] ?? []} />
            <RelatedCourseList excludeCourses={courseSlug.toUpperCase()} />
          </>
        )
      )}
    </>
  );
}
