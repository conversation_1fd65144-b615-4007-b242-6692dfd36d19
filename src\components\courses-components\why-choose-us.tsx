import { BentoGrid, BentoGridItem } from "~/components/ui/bento-grid";
import {
  GraduationCap,
  Users,
  BookOpenCheck,
  ClipboardList,
  CalendarCheck,
  Award,
  Clock,
} from "lucide-react";
import { FacultyAvatars } from "../home-components/hero/FacultyAvatars";

const benefits = [
  {
    title: "Expert Faculty",
    description:
      "Highly qualified instructors with extensive experience in NEET preparation",
    icon: <GraduationCap className="h-6 w-6 text-blue-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-blue-50 to-purple-50">
        <FacultyAvatars />
      </div>
    ),
  },
  {
    title: "Personalized Attention",
    description:
      "Individual mentoring to address specific strengths and weaknesses",
    icon: <Users className="h-6 w-6 text-purple-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-purple-50 to-pink-50">
        <Users className="h-16 w-16 text-purple-600" />
      </div>
    ),
  },
  {
    title: "Comprehensive Curriculum",
    description:
      "Covers all topics in the NEET syllabus with detailed explanations",
    icon: <BookOpenCheck className="h-6 w-6 text-green-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-green-50 to-teal-50">
        <BookOpenCheck className="h-16 w-16 text-green-600" />
      </div>
    ),
  },
  {
    title: "Regular Assessments",
    description:
      "Mock tests and quizzes to evaluate progress and identify areas for improvement",
    icon: <ClipboardList className="h-6 w-6 text-yellow-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-yellow-50 to-orange-50">
        <ClipboardList className="h-16 w-16 text-yellow-600" />
      </div>
    ),
  },
  {
    title: "Structured Preparation",
    description:
      "Ample time to master the vast NEET syllabus without last-minute stress",
    icon: <CalendarCheck className="h-6 w-6 text-red-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-red-50 to-pink-50">
        <CalendarCheck className="h-16 w-16 text-red-600" />
      </div>
    ),
  },
  {
    title: "Competitive Edge",
    description:
      "Early preparation puts students ahead of peers who start late",
    icon: <Award className="h-6 w-6 text-indigo-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-50 to-violet-50">
        <Award className="h-16 w-16 text-indigo-600" />
      </div>
    ),
  },
  {
    title: "Confidence Building",
    description: "Regular practice and assessments boost self-assurance",
    icon: <Clock className="h-6 w-6 text-teal-600" />,
    header: (
      <div className="relative flex h-40 items-center justify-center rounded-xl bg-gradient-to-br from-teal-50 to-emerald-50">
        <Clock className="h-16 w-16 text-teal-600" />
      </div>
    ),
  },
];

export function WhyChooseUs() {
  return (
    <section className="container mx-auto py-12">
      <h2 className="mb-8 text-center text-3xl font-bold">Why Choose Us?</h2>
      <BentoGrid className="max-w-7xl">
        {benefits.map((benefit, i) => (
          <BentoGridItem
            key={i}
            title={
              <div className="flex items-center gap-3">
                {benefit.icon}
                <span>{benefit.title}</span>
              </div>
            }
            description={benefit.description}
            header={benefit.header}
            className={i === 0 ? "md:col-span-2" : ""}
          />
        ))}
      </BentoGrid>
    </section>
  );
}
