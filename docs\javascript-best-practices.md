# JavaScript Best Practices for Performance

This document outlines best practices for JavaScript usage in our Next.js application to ensure optimal performance.

## Avoiding Inline JavaScript

Inline JavaScript can block HTML rendering and prevent efficient caching. Follow these guidelines:

### 1. Use Next.js Script Component

Always use the Next.js `Script` component instead of regular `<script>` tags:

```tsx
import Script from 'next/script';

// Good - uses Next.js Script component with strategy
<Script 
  src="/path/to/script.js"
  strategy="afterInteractive" 
/>

// Bad - uses regular script tag
<script src="/path/to/script.js"></script>
```

### 2. Choose the Right Loading Strategy

Next.js provides several loading strategies:

- `beforeInteractive`: Critical scripts that need to load before page becomes interactive
- `afterInteractive`: (Default) Load after page becomes interactive
- `lazyOnload`: Load during idle time
- `worker`: (Experimental) Load in a web worker

For most scripts, use `afterInteractive` or `lazyOnload`.

### 3. Use the LazyScript Component for Non-Critical Scripts

For scripts that aren't needed immediately, use our custom `LazyScript` component:

```tsx
import { LazyScript } from '~/components/ui/lazy-script';

<LazyScript 
  src="/path/to/non-critical.js"
  delay={3000} // Load after 3 seconds
/>
```

### 4. Structured Data (JSON-LD)

For SEO structured data, use the centralized utility functions in `src/lib/structured-data.ts` and load them with the Next.js Script component:

```tsx
import Script from 'next/script';
import { generateOrganizationSchema } from '~/lib/structured-data';

<Script
  id="organization-schema"
  type="application/ld+json"
  strategy="afterInteractive"
  dangerouslySetInnerHTML={{
    __html: JSON.stringify(generateOrganizationSchema(...))
  }}
/>
```

### 5. Bundle Size Considerations

- Use dynamic imports for large libraries that aren't needed immediately
- Consider code-splitting for different routes
- Use the bundle analyzer to identify large dependencies:

```bash
ANALYZE=true npm run build
```

## General JavaScript Performance Tips

1. **Avoid Large Inline Scripts**: Keep inline JavaScript under 10,000 bytes per page
2. **Defer Non-Critical JavaScript**: Use `defer` or `async` attributes when possible
3. **Minimize DOM Manipulations**: Batch DOM updates to reduce reflows
4. **Use Event Delegation**: Attach event listeners to parent elements when possible
5. **Debounce/Throttle Event Handlers**: For scroll, resize, and other frequent events
6. **Optimize Animations**: Use CSS transitions/animations when possible
7. **Lazy Load Components**: Use dynamic imports for components not needed immediately

## Testing Performance

Use Lighthouse or PageSpeed Insights to measure JavaScript performance metrics:

1. Time to Interactive (TTI)
2. Total Blocking Time (TBT)
3. Largest Contentful Paint (LCP)
4. First Input Delay (FID)
5. Cumulative Layout Shift (CLS)

## Resources

- [Next.js Script Documentation](https://nextjs.org/docs/app/building-your-application/optimizing/scripts)
- [Web.dev JavaScript Performance](https://web.dev/fast/#optimize-your-javascript)
- [Google PageSpeed Insights](https://pagespeed.web.dev/)
